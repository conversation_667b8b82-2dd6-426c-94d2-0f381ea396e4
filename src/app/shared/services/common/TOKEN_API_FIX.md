# 🔧 Token API Fix - Bypass DataService

## 🚨 **Vấn đề đã được fix:**

**Root cause**: `DataService.get()` bỏ qua headers parameter và tự động sử dụng `securityService.getCookieToken()` để set Authorization header.

**Vấn đề cụ thể:**
1. ✅ Token được lấy thành công từ cookie `"access_token"`
2. ❌ Nhưng `DataService.get()` không sử dụng token đó
3. ❌ Thay vào đó, `DataService.get()` sử dụng `securityService.getCookieToken()`
4. ❌ `securityService.getCookieToken()` tìm cookie `"authorizationData"` (sai key)

## ✅ **Giải pháp đã implement:**

### **1. Bypass DataService - Sử dụng HttpClient trực tiếp:**

```typescript
// TRƯỚC: Sử dụng DataService (bị override headers)
const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
};
return this.service.get(newApiUrl, null, headers); // ← Headers bị ignore!

// SAU: Sử dụng HttpClient trực tiếp
const headers = new HttpHeaders({
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
});
return this.http.get(newApiUrl, { headers }); // ← Headers được sử dụng đúng!
```

### **2. Inject HttpClient vào UserService:**

```typescript
// Import HttpClient
import { HttpClient, HttpHeaders } from '@angular/common/http';

// Inject vào constructor
constructor(
    // ... other services
    private http: HttpClient
) { }
```

### **3. Enhanced logging để debug:**

```typescript
console.log('✅ Calling new account API with token from cookie...');
console.log('Headers:', headers.keys()); // Show header keys
```

## 🔄 **Luồng hoạt động mới:**

```
ESaleKitComponent.ngOnInit()
    ↓
checkTokenAndLoadPermissions()
    ↓
UserService.getAccessTokenFromCookie() → cookieService.get('access_token') ✅
    ↓
Create HttpHeaders with Bearer token ✅
    ↓
HttpClient.get(newApiUrl, { headers }) ✅ (Bypass DataService)
    ↓
API call với đúng Authorization header ✅
    ↓
Convert response → Save to sessionStorage ✅
```

## 🧪 **Expected Results:**

### **✅ Console logs:**
```
E-Sale-Kit: Checking token and loading permissions...
=== COOKIE DEBUG ===
1. All cookies via document.cookie: access_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
2. CookieService methods:
   - cookieService.get("access_token"): eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
5. Test getAccessTokenFromCookie():
   - Result: Found: eyJ0eXAiOiJKV1QiLCJhbGci...
==================
=== DEBUG: checkTokenAndLoadPermissions started ===
✅ Found access_token in cookie, loading permissions...
Token preview: eyJ0eXAiOiJKV1QiLCJhbGci...
=== DEBUG: loadAccountFromNewApi started ===
API URL: https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account
✅ Calling new account API with token from cookie...
Headers: ["Authorization", "Content-Type"]
New API response: {roles: [...]}
Converted permissions: [...]
✅ E-Sale-Kit: Permissions loaded successfully
```

### **✅ Network tab:**
- **Request URL**: `https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account`
- **Request Headers**: 
  ```
  Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
  Content-Type: application/json
  ```
- **Response**: `{roles: [...]}`

### **✅ SessionStorage:**
```javascript
// Key: "permissions"
{
  "property": {
    "unit": {
      "import": { "isAuthor": true }
    }
  },
  "esalekit": {
    "edit": { "isAuthor": true }
  }
}
```

## 🔍 **Debug commands:**

```javascript
// 1. Check token được lấy đúng chưa
userService.debugCookieAccess()

// 2. Check API call
userService.checkTokenAndLoadPermissions().subscribe(console.log)

// 3. Check Network tab có request không
// 4. Check sessionStorage có permissions không
sessionStorage.getItem('permissions')
```

## 📝 **Key Changes:**

1. **✅ Bypass DataService**: Sử dụng HttpClient trực tiếp
2. **✅ Proper headers**: HttpHeaders được truyền đúng cách
3. **✅ No token override**: Không bị DataService override Authorization header
4. **✅ Enhanced logging**: Debug logs chi tiết
5. **✅ Clean implementation**: Không cần hack SecurityService

## 🚀 **Test ngay:**

1. **Load trang**: `http://localhost:4400/esalekit/hc04-fpt-3169113062724011`
2. **Mở DevTools Console** và xem logs
3. **Mở Network tab** và xem API request có Authorization header đúng không
4. **Check sessionStorage** có permissions không

Bây giờ API sẽ được gọi với đúng Bearer token từ cookie! 🎉
