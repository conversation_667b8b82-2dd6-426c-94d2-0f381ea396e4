import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, OnInit, ViewChild, TemplateRef, } from '@angular/core';
import {FormGroup, FormBuilder, Validators, FormArray, AbstractControl} from '@angular/forms';
import { MatAutocompleteSelectedEvent, MatDialog, MatTabChangeEvent } from '@angular/material';
import { ActivatedRoute, Router } from '@angular/router';
import { ProjectService } from 'app/pages/project/project.service';
import { Constant, EventTypeEnum, PropertyPrimaryStatusText } from 'app/shared/constant/constant';
import { ProjectStatusEnum } from 'app/shared/enum/project.enum';
import { MoneyToNumber } from 'app/shared/parse/money-to-number';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, map, startWith, switchMap, takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs/Rx';
import { TrainingService } from '../training.service';
import { ProjectConstant } from 'app/shared/constant/project.constant';
const moment = require('moment');
import { EditorPopup } from 'app/shared/components/editor-popup/editor-popup';
import { DataService } from 'app/shared/services';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { SalesProgramService } from 'app/pages/project/sales-program.service';
import { InteractionReportDialogComponent } from '../interaction-report-dialog/interaction-report-dialog.component';
import { WsNotifyService } from 'app/shared/components/ws-notify.service';
import { EventChangeService } from 'app/shared/services/event.change.service';
import { AuthorizeService } from 'app/shared/authenticate';
import { P } from '@angular/cdk/keycodes';
import { InputInterestedAreaAppSelectComponent } from 'app/shared/components/input-interested-area-app-select';
import { CPagination } from "app/api-models";
import { PagingComponent } from "app/shared/components/paging";
import { GridApi, GridOptions, SortChangedEvent } from "ag-grid-community";
import { TemplateRendererComponent } from "app/pages/project/event-sale/list/template-renderer/template-renderer.component";
import { ConfirmPopup } from 'app/shared/components/confirm-popup/confirm-popup';
import { FamilyModel } from 'app/shared/models/family.model';
import { SettingSmsDialogComponent } from 'app/shared/components/setting-sms/setting-sms-dialog.component';
import { TextareaPopup } from 'app/shared/components/textarea-popup/textarea-popup';

export const trimString = (alias) => {
    var str = alias;
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, " ");
    str = str.replace(/ + /g, " ");
    str = str.trim();
    return str;
}

@Component({
    selector: 'app-training-event-detail',
    templateUrl: './training-event-detail.component.html',
    styleUrls: ['./training-event-detail.component.scss'],
    animations: [
        trigger('expandCollapse', [
            state('expandCollapseState', style({ height: '*' })),
            transition('* => void', [style({ height: '*' }), animate(500, style({ height: "0" }))]),
            transition('void => *', [style({ height: '0' }), animate(500, style({ height: "*" }))])
        ])
    ]
})
export class TrainingEventDetailComponent implements OnInit {
    @ViewChild('interestedArea') interestedArea: InputInterestedAreaAppSelectComponent;
    @ViewChild('paging') paging: PagingComponent;
    @ViewChild("agCodeYCHD") agCodeYCHD: TemplateRef<any>;
    @ViewChild("statusCheckIn") statusCheckIn: TemplateRef<any>;
    @ViewChild("statusCheckOut") statusCheckOut: TemplateRef<any>;
    @ViewChild("modifiedDate") modifiedDate: TemplateRef<any>;
    @ViewChild("remove") remove: TemplateRef<any>;

    formGroup: FormGroup;
    dynamicFormGroup: FormGroup;
    eventId = '';
    isSendMailAgain = false;
    isSendSmsAgain = false;
    isOnlineType = true;
    projects: any[] = [];
    listPos: any[];
    listPerson: Observable<any[]>;
    public Constant = Constant;
    eventTypeEnum = EventTypeEnum
    isPos = true;
    isPerson = false;
    isShowData: boolean = false;
    isShowLinkLive: boolean = false;
    unsubscribe$: Subject<any> = new Subject();
    listItemEmployee: any[] = [];
    person: any;
    imageUrl = '';
    logoLeft = '';
    logoRight = '';
    prizeBackgroundLive = '';
    prizeImages = [];
    prizeImage = '';
    transactionPrizeImages = [];
    transactionPrizeImage = '';
    color!: string;
    public listAdmin = [];
    public isLoadingListAdmin$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    public filteredListAdmin: any[] = [];
    transactionStatusList = Object.keys(PropertyPrimaryStatusText).map(txn => {
        return { id: txn, name: PropertyPrimaryStatusText[txn] };
    });
    importedCustomers: any[] = [];
    listSpinAdditional: any[] = [];
    requestDemands: any[] = [];
    training: any = {};
    salesPrograms: any;

    // RESEND MAIL
    @ViewChild('sendEmailHeader') sendEmailHeader: TemplateRef<any>;
    @ViewChild('sendEmailRow') sendEmailRow: TemplateRef<any>;
    isSelectAllForEmail: boolean = false;
    selectedToSendEmail: any[] = [];
    listNotSentDeliveryEmail: any[] = [];

    typeLiveStream: any = [
        {id: "wowza", value:"wowza"},
        {id: "facebook", value:"facebook"},
        {id: "youtube", value:"youtube"},
        {id: "video", value:"video"},
    ]

    constructor(
        private _fb: FormBuilder,
        private _projectService: ProjectService,
        private route: ActivatedRoute,
        private router: Router,
        private trainingService: TrainingService,
        private toastr: ToastrService,
        private dialog: MatDialog,
        private dataService: DataService,
        private salesProgramService: SalesProgramService,
        private wsNotifyService: WsNotifyService,
        private eventChangeService: EventChangeService,
        private authorizeService: AuthorizeService,
    ) { }

    saleUnitNames = [];
    saleUnitName = '';
    timeSlotNames = [];
    timeSlotName = '';

    usersPagination: CPagination<any> = new CPagination();
    currentTabIndex: number = 1;
    currentFilter: any = {};
    total : number;
    sortBy = '-isCheckIn,checkInDate';

    public gridApi1: GridApi;
    public columnDefs = [];
    public rowData: any = [];
    public rowSelection = 'single';

    gridOptions: GridOptions = {
        defaultColDef: {
            resizable: true,
            tooltipComponent: "customTooltip",
            sortable: false,
            enableCellChangeFlash: false,
        },
        autoSizePadding: 10,
        cellFlashDelay: 500,
        rowBuffer: 30,
        rowHeight: 60,
        animateRows: false,
        suppressRowTransform: true,
        enableCellTextSelection: true,
        singleClickEdit: true,
        enableBrowserTooltips: true,
        tooltipShowDelay: 0,
        overlayNoRowsTemplate: "<span>Không có dữ liệu</span>",
        rowSelection: "multiple",
        groupSelectsChildren: true,
        groupSelectsFiltered: true,
        suppressAggFuncInHeader: true,
        suppressRowClickSelection: true,
        onSortChanged: (event: SortChangedEvent) => {
            this.getDataSortChanged(event.api.getSortModel()[0]);
        },
    };

    onGridReady1(params) {
        this.gridApi1 = params.api;
        this.setAutoSize1();
    }

    setAutoSize1() {
        this.gridApi1 && this.gridApi1.setDomLayout("autoHeight");
        this.gridApi1 && this.gridApi1.sizeColumnsToFit();
    }

    ngAfterViewInit(): void {
        this.columnDefs = this.getColumnDef(true);
    }

    toggleSelectAllForEmail(event) {
        if (event.checked) {
            // Những item chưa check
            let uncheckIds = this.rowData.filter(x => !x.selectedForEmail).map(x => x.id)
            this.rowData.forEach((row: any) => row.selectedForEmail = true);

            // Thêm những item chưa check
            this.selectedToSendEmail = this.selectedToSendEmail.concat(uncheckIds);
        } else {
            // Item của page này
            let uncheckIds = this.rowData.map(x => x.id);
            this.rowData.forEach((row: any) => row.selectedForEmail = false);

            // Xóa Item của page này
            this.selectedToSendEmail = this.selectedToSendEmail.filter(x => !uncheckIds.includes(x));
        }
    }

    toggleSelectForEmail(row, event) {
        if (event.checked) {
            this.selectedToSendEmail.push(row.id);

            // Nếu page check all thì => check header = true
            if (!this.rowData.some(x => x.selectedForEmail === false)) {
                this.isSelectAllForEmail = true;
            }
        } else {
            this.selectedToSendEmail = this.selectedToSendEmail.filter(x => x !== row.id);
            this.isSelectAllForEmail = false;
        }
    }

    reSendEmail() {
        const confirmPopup = this.dialog.open(ConfirmPopup, {
            width: '600px',
            data: {
                title: `GỬI LẠI EMAIL THƯ MỜI`,
                message: `Xác nhận gửi lại email thư mời tham gia sự kiện`,
                isReason: false,
                textCancel: 'Hủy',
                textOk: 'Gửi email',
                contentPadding: 5
            }
        });
        confirmPopup.afterClosed().subscribe(response => {
            if (!response) return;
            if (response.execute) {
                const body = {
                    id: this.eventId,
                    userIds: this.selectedToSendEmail.length ? this.selectedToSendEmail : []
                }
                this.trainingService.resendEmail(body).subscribe(res => {
                    this.toastr.success("Thành công ", "Gửi lại mail thành công! ");
                })
            }
        });
    }

    getColumnDef(isActive) {
        return [
            {
                headerComponentFramework: TemplateRendererComponent,
                headerComponentParams: {
                    ngTemplate: this.sendEmailHeader
                },
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.sendEmailRow
                },
                width: 40
            },
            {
                headerName: "Tên KH",
                field: "name",
                unSortIcon: true,
                cellClass: "user-detail",
                sortable: true,
            },
            {
                headerName: "Email",
                field: "email",
                unSortIcon: true,
                sortable: true,
            },
            {
                headerName: "Check In", field:'isCheckIn',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.statusCheckIn,
                },
                unSortIcon: true,
                sortable: true,
            },
            {
                headerName: "Check Out",field:'checkOutData',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.statusCheckOut,
                },
                unSortIcon: true,
                sortable: true,
            },
            {
                headerName: "Ngày tạo",
                cellRendererFramework: TemplateRendererComponent,
                field: "modifiedDate",
                cellRendererParams: {
                    ngTemplate: this.modifiedDate,
                },
                unSortIcon: true,
                sortable: true,
            },
            {
                headerName: "Suất đăng ký",
                field: "timeSlot",
                unSortIcon: true,
                sortable: true,
            },
            {
                headerName: "Hành Động",
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.remove,
                },
            },
        ];
    }

    async ngOnInit() {
        const params = this.route.snapshot.params;
        this.isShowLinkLive = await this.authorizeService.hasAuthority(['TRAINING', 'CREATE', 'URL', 'LIVE']);
        if (params['id']) {
            this.eventId = params['id'];
            this.getData();
            this.getEmployee();

        }
        this.initForm();
        this.getProjectsByUser();
        this.getAllExchanges();
        this.subscribeWS(this.eventId);
    }

    private subscribeWS(id: string) {
        const infoMessagingPattern = [`${WsNotifyService.messagingPattern}msx-adsg.training.${id}`];
        this.wsNotifyService.subscribe(infoMessagingPattern);
        this.eventChangeService.emitChangeSource.subscribe(data => {
            switch (data.broadcast) {
                case 'userCheckInOut':
                    const user = this.importedCustomers.find(e => e.id === data.message.data.id);
                    user.isCheckIn = data.message.data.isCheckIn;
                    user.checkInDate = data.message.data.checkInDate;
                    user.checkOutDate = data.message.data.checkOutDate;
                    break;
                default:
                    break;
            }
        });

    }
    getEmployee() {
        this.trainingService.getUserByEventId(this.eventId, {isEmployee: true}).subscribe(res => {
            if (res && res.length) {
                this.listItemEmployee = [];
                res.forEach(element => {
                    if (!this.checkEmployee(element.email)) {
                        if (!element.posId) {
                            this.listItemEmployee.push({
                                isFirst: true,
                                posId: element.posId,
                                posName: element.dvbh,
                                id: element.id,
                                code: element.code,
                                name: element.name,
                                email: element.email,
                                phoneNumber: element.phone,
                                cmnd: element.cmnd,
                                show: true,
                            })
                        } else if (!this.checkHasPos(element.posId)) {
                            this.listItemEmployee.push({
                                isFirst: true,
                                posId: element.posId,
                                posName: element.dvbh,
                                id: element.id,
                                code: element.code,
                                name: element.name,
                                email: element.email,
                                phoneNumber: element.phone,
                                cmnd: element.cmnd,
                                show: false,
                            })
                        } else {
                            this.listItemEmployee.push({
                                isFirst: false,
                                posId: element.posId,
                                posName: element.dvbh,
                                id: element.id,
                                code: element.code,
                                name: element.name,
                                email: element.email,
                                phoneNumber: element.phone,
                                cmnd: element.cmnd,
                                show: false,
                            })
                        }
                    }
                });
                if (this.listItemEmployee.length) {
                    this.listItemEmployee.sort((a, b) => {
                        return a.posId - b.posId;
                    })
                }
            }
        })
    }
    getCustomer(params?: any) {
        let p: any = {};
        p.page = this.usersPagination.page;
        p.pageSize = this.usersPagination.pageSize;
        if(params)
        {
            p.query = params.query;
        }
        //p.type = '';
        p.sort = this.sortBy;
        p.isCustomer = true;
        delete p.inActive;
        this.showLoadingOverlay();
        this.trainingService
        .getUserByEventId(this.eventId, p ? p : {page: 1, pageSize: 20,query: p.query, sort:p.sort , isCustomer: true})
        .subscribe((res) => {
                this.setCustomer(res);
                this.rowData = res.rows;
                this.isSelectAllForEmail = true;
                if (this.selectedToSendEmail.length) {
                    this.rowData.forEach(element => {
                        if (this.selectedToSendEmail.some(x => x === element.id)) {
                            element.selectedForEmail = true;
                        } else {
                            this.isSelectAllForEmail = false;
                            element.selectedForEmail = false;
                        }
                    });
                } else {
                    this.isSelectAllForEmail = false;
                }
                this.gridApi1.setRowData(this.rowData);
                this.usersPagination.items = this.rowData;
                this.usersPagination.total = res.total
                this.usersPagination.totalPage = res.totalPage
        });
    }
    onChangePaging(event: CPagination<any>) {
        this.currentFilter = event;
        this.usersPagination = event;
        this.getCustomer(this.currentFilter);
    }

    showLoadingOverlay() {
        this.gridApi1 && this.gridApi1.showLoadingOverlay();
    }

    hideOverlay() {
        this.gridApi1 && this.gridApi1.hideOverlay();
    }

    setCustomer(data: any) {
        this.usersPagination = new CPagination().decode(data);
        this.setAutoSize1();
    }

    onLinkClick(event: MatTabChangeEvent) {
        this.currentTabIndex = event.index + 1;
        switch (this.currentTabIndex) {
            case 1:
                this.getEmployee();
                break;
            case 2:
                this.getCustomer();
                break;
        }
    }

    onChangeColorTitle (color: string) {
        this.formGroup.get("colorTitle").setValue(color)
    }

    onChangeColorWiner (color: string) {
        this.formGroup.get("colorWiner").setValue(color)
    }

    onChangeColorPrize (color: string) {
        this.formGroup.get("colorPrize").setValue(color)
    }

    onChangeColorTitleTransaction (color: string) {
        this.formGroup.get("colorTitleTransaction").setValue(color)
    }

    onChangeColorWinerTransaction (color: string) {
        this.formGroup.get("colorWinerTransaction").setValue(color)
    }

    onChangeColorPrizeTransaction (color: string) {
        this.formGroup.get("colorPrizeTransaction").setValue(color)
    }

    checkHasPos(posId: string) {
        if (this.listItemEmployee && this.listItemEmployee.length) {
            return !!this.listItemEmployee.filter(item => item.posId === posId).length;
        }
        return false;
    }

    getData() {
        this.trainingService.getEventById(this.eventId).subscribe(res => {
            if (res) {
                if(res.eventType === this.eventTypeEnum.OFFLINE) {
                    this.isOnlineType = false
                } else {
                    this.isOnlineType = true
                }
                this.patchValue(res);
                this.training = res;
                this.listAdmin = res.listAdmin;
                this.listSpinAdditional = this.sortPhoneNumber(res.listSpinAdditional);
                this.saleUnitNames = res.saleUnitNames || [];
                this.timeSlotNames = res.timeSlotNames || [];
            }
        })
    }

    patchValue(p: any) {
        if (!this.formGroup) this.initForm;
        const startTimeBanner = p.displayBannerStartTime ? moment(p.displayBannerStartTime).format('HH:mm') : '';
        const startDateBanner = p.displayBannerStartTime ? moment(p.displayBannerStartTime).format('YYYY-MM-DD') : '';
        const endTimeBanner = p.displayBannerEndTime ? moment(p.displayBannerEndTime).format('HH:mm') : '';
        const endDateBanner = p.displayBannerEndTime ? moment(p.displayBannerEndTime).format('YYYY-MM-DD') : '';
        const startCheckInTime =  p.startTimeCheckIn ? moment(p.startTimeCheckIn).format('HH:mm') : '';
        const endCheckInTime =  p.endTimeCheckIn ? moment(p.endTimeCheckIn).format('HH:mm') : '';
        const startCheckInDate = p.startTimeCheckIn ? moment(p.startTimeCheckIn).format('YYYY-MM-DD') : '';
        const endCheckInDate = p.endTimeCheckIn ? moment(p.endTimeCheckIn).format('YYYY-MM-DD') : '';
        this.imageUrl = p.image;
        this.logoLeft = p.logoLeft;
        this.logoRight = p.logoRight;
        this.prizeBackgroundLive = p.prizeBackgroundLive;
        if (p.interestedArea) {
            this.interestedArea && this.interestedArea.patchValue(p.interestedArea);
        }
        this.formGroup.patchValue({
            code: p.code,
            name: p.name,
            urlEvent: p.urlEvent || `${trimString(p.name).replace(/ /g, "-")}`,
            typeLiveStream: p.typeLiveStream,
            contentEvent: p.contentEvent,
            contentOffEvent: p.contentOffEvent,
            timeStartEvent: p.timeStartEvent,
            projectId: p.project && p.project.id,
            livestreamUrl: p.livestreamUrl,
            registerUrl: p.registerUrl,
            hotline: p.hotline,
            fLive: p.fLive,
            displayBannerStartTime: startTimeBanner,
            displayBannerStartDate: startDateBanner,
            displayBannerEndTime: endTimeBanner,
            displayBannerEndDate: endDateBanner,
            startCheckInTime: startCheckInTime,
            endCheckInTime: endCheckInTime,
            startCheckInDate: startCheckInDate,
            endCheckInDate: endCheckInDate,
            note: p.note,
            isActive: p.isActive,
            isEventOff: p.isEventOff,
            priceName: p.prizeName,
            colorTitle: p.colorTitle,
            colorPrize: p.colorPrize,
            colorWiner: p.colorWiner,
            colorTitleTransaction: p.colorTitleTransaction,
            colorPrizeTransaction: p.colorPrizeTransaction,
            colorWinerTransaction: p.colorWinerTransaction,
            transactionPriceName: p.transactionPrizeName,
            skipAuthen: p.skipAuthen,
            spinByTransaction: p.spinByTransaction,
            allowAdminConfirm: p.allowAdminConfirm,
            allowIDCardSpin: p.allowIDCardSpin,
            allowPassersby: p.allowPassersby,
            allowAdminConfirmTransaction: p.allowAdminConfirmTransaction,
            transactionStatuses: p.transactionStatuses,
            salesProgramId: p.salesProgram  && p.salesProgram.id,
            hideChat: p.hideChat,
            hideReaction: p.hideReaction,
            lockComment: p.lockComment,
            disableFullScreen: p.disableFullScreen,
            allowRequestDemand: p.allowRequestDemand,
            allowRaiseHand: p.allowRaiseHand,
            unRequiredEmailRegister: p.unRequiredEmailRegister,
            allowRegisterCustomer: p.allowRegisterCustomer, // field cho phép đơn vị bán hàng đăng ký khách hàng
            prizeBackground: p.prizeBackground,
            transactionPrizeBackground: p.transactionPrizeBackground,
            emailTemplateSubject: p.templateConfig && p.templateConfig.subject ? p.templateConfig.subject: null,
            emailTemplateBody: p.templateConfig && p.templateConfig.body ? p.templateConfig.body : null,
            smsBrandName: p.templateConfig && p.templateConfig.smsBrandName ? p.templateConfig.smsBrandName: null,
            smsContent: p.templateConfig && p.templateConfig.smsContent ? p.templateConfig.smsContent : null,
            eventType: p.eventType ? p.eventType : this.eventTypeEnum.ONLINE,
            interestedArea: p.interestedArea,
            eventDescription: p.eventDescription,
            limitRegister: p.limitRegister,
        }, { onlySelf: true, emitEvent: false });

        if(p.project && p.project.id)  {
            this.getListSalesProgram(p.project.id);
        }

        if (p.prizes && p.prizes.length) {
            const prizes = this.dynamicFormGroup.controls.prizes as FormArray;
            p.prizes.forEach(element => {
                prizes.push(this._fb.group({
                    priceName: [element.priceName, Validators.required],
                    prizeType: [element.prizeType, Validators.required],
                    prizeValue: [element.prizeValue, Validators.required],
                    amount: [MoneyToNumber.parse(element.amount), Validators.required]
                }));
                this.prizeImages.push(element.prizeImage);
            });
        }

        if (p.transactionPrizes && p.transactionPrizes.length) {
            const transactionPrizes = this.dynamicFormGroup.controls.transactionPrizes as FormArray;
            p.transactionPrizes.forEach(element => {
                transactionPrizes.push(this._fb.group({
                    priceName: [element.priceName, Validators.required],
                    prizeType: [element.prizeType, Validators.required],
                    prizeValue: [element.prizeValue, Validators.required],
                    amount: [MoneyToNumber.parse(element.amount), Validators.required]
                }));
                this.transactionPrizeImages.push(element.prizeImage);
            });
        }
    }

    onProjectChange(event) {
        if (event) {
            this.getListSalesProgram(event.id);
        }
    }

    getListSalesProgram(projectId) {
        const params = {
          projectId: projectId,
          page: 1,
          pageSize: 100,
        };
        this.salesProgramService.getListSalesProgram(params).subscribe(res => {
            this.salesPrograms = res.rows;
        });
    }

    getSalesProgram() {
        if(this.salesPrograms && this.salesPrograms.length > 0) {
            const p = this.salesPrograms.find(i => i.id === this.formGroup.get('salesProgramId').value);
            if (p) {
                return {
                    id: p.id,
                    name: p.name
                }
            }
        }
        return null;
    }

    initForm() {
        this.formGroup = this._fb.group({
            code: [null],
            name: [null, Validators.required],
            urlEvent: [null],
            contentEvent: [null],
            contentOffEvent: [null],
            timeStartEvent: [null],
            projectId: [null, Validators.required],
            livestreamUrl: [null],
            typeLiveStream: ['wowza'],
            registerUrl: [null],
            hotline: [null],
            fLive: [null],
            displayBannerStartTime: [moment(new Date()).format('HH:mm'), Validators.required],
            displayBannerStartDate: [new Date(),  Validators.required],
            displayBannerEndTime: [moment(new Date()).format('HH:mm')],
            displayBannerEndDate: [new Date()],
            image: [null],
            note: [null],
            isActive: [true],
            isEventOff: [false],
            prizeName: [null, Validators.required],
            prizeValue: [null, Validators.required],
            prizeAmount: [null, Validators.required],
            transactionPrizeName: [null, Validators.required],
            transactionPrizeValue: [null, Validators.required],
            transactionPrizeAmount: [null, Validators.required],
            pos: [null],
            person: [null],
            priceName: [],
            colorTitle: [null],
            colorPrize: [null],
            colorWiner: [null],
            colorTitleTransaction: [null],
            colorPrizeTransaction: [null],
            colorWinerTransaction: [null],
            transactionPriceName: [],
            skipAuthen: [false],
            spinByTransaction: [false],
            allowAdminConfirm: [false],
            allowIDCardSpin: [false],
            allowPassersby: [false],
            allowAdminConfirmTransaction: [false],
            hideChat: [false],
            hideReaction: [false],
            lockComment: [false],
            transactionStatuses: [null],
            salesProgramId: [null],
            disableFullScreen: [false],
            allowRequestDemand: [false],
            allowRaiseHand: [false],
            unRequiredEmailRegister: [false],
            listAdminSearch: [''],
            notificationContent: [null, Validators.required],
            prizeBackground: [null],
            prizeBackgroundLive: [null],
            transactionPrizeBackground: [null],
            //Email
            emailTemplateSubject: [{ value: null, disabled: false }],
            emailTemplateBody: [{ value: null, disabled: false }],
            // sms
            smsBrandName: [{ value: null, disabled: false }],
            smsContent: [{ value: null, disabled: false }],
            startCheckInTime: [{ value: null, disabled: false }],
            endCheckInTime: [{ value: null, disabled: false }],
            startCheckInDate: [{ value: null, disabled: false }],
            endCheckInDate: [{ value: null, disabled: false }],
            allowRegisterCustomer: [false],
            eventType: [{ value: this.eventTypeEnum.ONLINE, disabled: false }],
            interestedArea: [{ value: null }],
            eventDescription: [null],
            limitRegister: [null]
        });
        this.dynamicFormGroup = this._fb.group({
            prizes: new FormArray([]),
            transactionPrizes: new FormArray([]),
            participants: new FormArray([])
        });

        this.listPerson = this.formGroup.get('person').valueChanges
            .pipe(
                debounceTime(300),
                startWith(''),
                switchMap((value: string) => {
                    return this.filterPerson(value ? value.trim() : '');
                }),
                takeUntil(this.unsubscribe$));

        this.formGroup.get('listAdminSearch').valueChanges
            .pipe(
                debounceTime(300),
                distinctUntilChanged(),
                takeUntil(this.unsubscribe$))
            .subscribe((value: string) => this.searchSalesAdmin(value));
        this.formGroup.get('name').valueChanges
        .pipe(
            debounceTime(300),
            distinctUntilChanged(),
            takeUntil(this.unsubscribe$))
        .subscribe((value: string) => {
            const urlEvent =  `${trimString(value).replace(/ /g, "-")}`
            this.formGroup.get('urlEvent').setValue(urlEvent);
        })
    }

    private filterPerson(filterValue: string): Observable<any[]> {
        return this.trainingService.getListEmployee(filterValue)
            .pipe(
                takeUntil(this.unsubscribe$),
                map((res: any) => res && res.rows)
            );
    }

    get prizes() {
        return this.dynamicFormGroup.controls.prizes as FormArray;
    }

    get transactionPrizes() {
        return this.dynamicFormGroup.controls.transactionPrizes as FormArray;
    }

    get participants() {
        return this.dynamicFormGroup.controls.participants as FormArray;
    }

    getAllExchanges() {
        this._projectService.getAllSan().subscribe(res => {
            res = res.rows ? res.rows : res;
            this.listPos = [...res];
        });
    }

    getProjectsByUser() {
        const status: string = `${ProjectStatusEnum.COMING_SALE};${ProjectStatusEnum.TRADING}`;
        this._projectService.getProjectListByName('', status).subscribe((res) => {
            const arr: any[] = [];
            $.each(res.data, (i, obj) => {
                arr.push({
                    id: obj.id,
                    name: obj.name
                });
            });
            this.projects = arr;
        });
    }

    addSaleUnitNames() {
        if (!this.saleUnitName || !this.saleUnitName.trim().length) {
            this.toastr.error("Đơn vị bán hàng bị trống");
            return;
        }
        if (this.saleUnitNames.includes(this.saleUnitName)) {
            this.toastr.error("Đơn vị bán hàng vị trùng");
            return;
        }
        this.saleUnitNames.push(this.saleUnitName);
        this.saleUnitName = '';
    }
    addTimeSlotNames() {
        if (!this.timeSlotName || !this.timeSlotName.trim().length) {
            this.toastr.error("Đơn vị bán hàng bị trống");
            return;
        }
        if (this.timeSlotNames.includes(this.saleUnitName)) {
            this.toastr.error("Đơn vị bán hàng vị trùng");
            return;
        }
        this.timeSlotNames.push(this.timeSlotName);
        this.timeSlotName = '';
    }
    removeSaleUnitNames(value) {
        this.saleUnitNames = this.saleUnitNames.filter(x => x !== value)
    }
    removeTimeSlotNames(value) {
        this.timeSlotNames = this.timeSlotNames.filter(x => x !== value)
    }

    addPrize() {
        const prizeName = this.formGroup.get('prizeName').value;
        const prizeValue = this.formGroup.get('prizeValue').value;
        const prizeAmount = this.formGroup.get('prizeAmount').value;

        if (!prizeName || !prizeValue || !prizeAmount) {
            this.toastr.error("Vui lòng nhập đủ thông tin", "Thêm giải! ");
            return;
        }

        const prizes = this.dynamicFormGroup.controls.prizes as FormArray;
        prizes.push(this._fb.group({
            priceName: [prizeName, Validators.required],
            prizeType: [prizes.length + 1, Validators.required],
            prizeValue: [prizeValue, Validators.required],
            amount: [MoneyToNumber.parse(prizeAmount), Validators.required],
        }));
        this.prizeImages.push(this.prizeImage);
        this.formGroup.patchValue({
            prizeName: '',
            prizeValue: '',
            prizeAmount: '',
        })
        this.prizeImage = '';
        this.formGroup.get('prizeName').markAsUntouched();
        this.formGroup.get('prizeValue').markAsUntouched();
        this.formGroup.get('prizeAmount').markAsUntouched();
    }

    addTransactionPrize() {
        const prizeName = this.formGroup.get('transactionPrizeName').value;
        const prizeValue = this.formGroup.get('transactionPrizeValue').value;
        const prizeAmount = this.formGroup.get('transactionPrizeAmount').value;

        if (!prizeName || !prizeValue || !prizeAmount) {
            this.toastr.error("Vui lòng nhập đủ thông tin", "Thêm giải! ");
            return;
        }

        const prizes = this.dynamicFormGroup.controls.transactionPrizes as FormArray;
        prizes.push(this._fb.group({
            priceName: [prizeName, Validators.required],
            prizeType: [prizes.length + 1, Validators.required],
            prizeValue: [prizeValue, Validators.required],
            amount: [MoneyToNumber.parse(prizeAmount), Validators.required],
        }));
        this.transactionPrizeImages.push(this.transactionPrizeImage);
        this.formGroup.patchValue({
            transactionPrizeName: '',
            transactionPrizeValue: '',
            transactionPrizeAmount: '',
        })
        this.transactionPrizeImage = '';
        this.formGroup.get('transactionPrizeName').markAsUntouched();
        this.formGroup.get('transactionPrizeValue').markAsUntouched();
        this.formGroup.get('transactionPrizeAmount').markAsUntouched();
    }

    deletePrize(i: any) {
        this.prizes.removeAt(i);
        this.prizeImages.splice(i, 1);
    }

    deleteTransactionPrize(i: any) {
        this.transactionPrizes.removeAt(i);
        this.transactionPrizeImages.splice(i, 1);
    }

    removeCustomer(row: any) {
        this.trainingService.removeCustomer(row.id).subscribe(res => {
            if (res) {
                let index = this.rowData.indexOf(row);
                this.rowData.splice(index, 1);
                this.gridApi1.setRowData(this.rowData);
                this.listItemEmployee = this.rowData;
            }
        })
    }

    onBack() {
        this.router.navigate([`training/event`]);
    }

    onSave(back=true) {
        if (this.getFormControl('name').invalid || this.getFormControl('projectId').invalid) {
            this.getFormControl('name').markAsTouched();
            this.getFormControl('projectId').markAsTouched();
            this.toastr.error("Vui lòng điền đầy đủ thông tin ", this.eventId ? "Cập nhật sự kiện! " : "Tạo mới sự kiện! ");
            return;
        }
        if(this.isOnlineType && this.isShowLinkLive && this.getFormControl('livestreamUrl').invalid) {
            this.getFormControl('livestreamUrl').markAsTouched();
            this.toastr.error("Vui lòng điền đầy đủ thông tin ", this.eventId ? "Cập nhật sự kiện! " : "Tạo mới sự kiện! ");
            return;
        }
        if(!this.imageUrl) {
            this.toastr.error("Vui lòng cập nhật hình ảnh đại diện ", this.eventId ? "Cập nhật sự kiện! " : "Tạo mới sự kiện! ");
            return;
        }
        const livestreamUrlControls = this.getFormControl('livestreamUrl')
        if(this.isOnlineType) {
            if(!livestreamUrlControls.value) {
                livestreamUrlControls.setErrors({required: true})
                this.toastr.error("Vui lòng điền đầy đủ thông tin ", this.eventId ? "Cập nhật sự kiện! " : "Tạo mới sự kiện! ");
                return;
            }
        }


        const displayBannerStartTime = this.formGroup.get('displayBannerStartTime').value;
        const displayBannerStartDate = this.formGroup.get('displayBannerStartDate').value;
        const displayBannerEndTime = this.formGroup.get('displayBannerEndTime').value;
        const displayBannerEndDate = this.formGroup.get('displayBannerEndDate').value;
        const startDateBanner = displayBannerStartDate ? moment(displayBannerStartDate).format('YYYY-MM-DD') : '';
        const endDateBanner = displayBannerEndDate ? moment(displayBannerEndDate).format('YYYY-MM-DD') : '';
        // Khai báo params cho phần thời gian check in sự kiện
        const startCheckInTime = this.formGroup.get('startCheckInTime').value;
        const startCheckInDate = this.formGroup.get('startCheckInDate').value;
        const endCheckInTime = this.formGroup.get('endCheckInTime').value;
        const endCheckInDate = this.formGroup.get('endCheckInDate').value;
        const startCheckIn = startCheckInDate ? moment(startCheckInDate).format('YYYY-MM-DD') : '';
        const endCheckIn = endCheckInDate ? moment(endCheckInDate).format('YYYY-MM-DD') : '';
        const startTimeCheckIn =  moment(`${startCheckIn} ${startCheckInTime}`, "YYYY-MM-DD HH:mm");
        const endTimeCheckIn = moment(`${endCheckIn} ${endCheckInTime}`, "YYYY-MM-DD HH:mm");
        const interestedArea = this.formGroup.get('interestedArea').value;
        if(startTimeCheckIn && endTimeCheckIn) {
            if(startTimeCheckIn >= endTimeCheckIn) {
                this.formGroup.get('endCheckInDate').setErrors({invalidDate: true})
                this.toastr.error("Có lỗi xảy ra",  "Thời gian check in hợp lệ quay thưởng bắt đầu phải lớn hơn Thời gian check in hợp lệ quay thưởng kết thúc");
                return;
            }
        }

        let prizes = this.dynamicFormGroup.controls.prizes.value;
        if (prizes && prizes.length) {
            prizes.forEach((item, index) => {
                item.prizeImage = this.prizeImages[index];
                item.prizeStatus = 'INIT';
            })
        }
        let transactionPrizes = this.dynamicFormGroup.controls.transactionPrizes.value;
        if (transactionPrizes && transactionPrizes.length) {
            if (!this.getFormControl('salesProgramId').value) {
                this.toastr.error("Vui lòng chọn chương trình bán hàng");
                return;
            }
            if (!this.getFormControl('transactionStatuses').value || !this.getFormControl('transactionStatuses').value.length) {
                this.toastr.error("Vui lòng chọn trạng thái được quay thưởng");
                return;
            }
            transactionPrizes.forEach((item, index) => {
                item.prizeImage = this.transactionPrizeImages[index];
                item.prizeStatus = 'INIT';
            })
        }


        const p: any = {
            project: this.getProject(),
            prizes: prizes,
            transactionPrizes: transactionPrizes,
            isActive: this.formGroup.get('isActive').value,
            isEventOff: this.formGroup.get('isEventOff').value,
            name: this.formGroup.get('name').value,
            urlEvent: this.formGroup.get('urlEvent').value,
            contentEvent: this.formGroup.get('contentEvent').value,
            contentOffEvent: this.formGroup.get('contentOffEvent').value,
            timeStartEvent: this.formGroup.get('timeStartEvent').value,
            displayBannerStartTime: moment(`${startDateBanner} ${displayBannerStartTime}`, "YYYY-MM-DD HH:mm"),
            displayBannerEndTime: moment(`${endDateBanner} ${displayBannerEndTime}`, "YYYY-MM-DD HH:mm"),
            // Thêm field vào params
            startTimeCheckIn: startTimeCheckIn,
            endTimeCheckIn: endTimeCheckIn,
            typeLiveStream: this.formGroup.get('typeLiveStream').value,
            image: this.imageUrl,
            logoLeft: this.logoLeft,
            logoRight: this.logoRight,
            hotline: this.formGroup.get('hotline').value,
            fLive: this.formGroup.get('fLive').value,
            note: this.formGroup.get('note').value,
            livestreamUrl: this.formGroup.get('livestreamUrl').value,
            registerUrl: this.formGroup.get('registerUrl').value,
            prizeName: this.formGroup.get('priceName').value,
            colorTitle: this.formGroup.get('colorTitle').value,
            colorPrize: this.formGroup.get('colorPrize').value,
            colorWiner: this.formGroup.get('colorWiner').value,
            colorTitleTransaction: this.formGroup.get('colorTitleTransaction').value,
            colorPrizeTransaction: this.formGroup.get('colorPrizeTransaction').value,
            colorWinerTransaction: this.formGroup.get('colorWinerTransaction').value,
            transactionPrizeName: this.formGroup.get('transactionPriceName').value,
            skipAuthen: this.formGroup.get('skipAuthen').value,
            spinByTransaction: this.formGroup.get('spinByTransaction').value,
            allowAdminConfirm: this.formGroup.get('allowAdminConfirm').value,
            allowIDCardSpin: this.formGroup.get('allowIDCardSpin').value,
            allowPassersby: this.formGroup.get('allowPassersby').value,
            allowAdminConfirmTransaction: this.formGroup.get('allowAdminConfirmTransaction').value,
            transactionStatuses: this.formGroup.get('transactionStatuses').value,
            salesProgram: this.getSalesProgram(),
            hideChat: this.formGroup.get('hideChat').value,
            hideReaction: this.formGroup.get('hideReaction').value,
            lockComment: this.formGroup.get('lockComment').value,
            disableFullScreen: this.formGroup.get('disableFullScreen').value,
            allowRequestDemand: this.formGroup.get('allowRequestDemand').value,
            allowRaiseHand: this.formGroup.get('allowRaiseHand').value,
            unRequiredEmailRegister: this.formGroup.get('unRequiredEmailRegister').value,
            allowRegisterCustomer: this.formGroup.get('allowRegisterCustomer').value, // field cho phép đơn vị bán hàng đăng ký khách hàng
            eventType: this.formGroup.get('eventType').value ,
            prizeBackground: this.formGroup.get('prizeBackground').value,
            prizeBackgroundLive: this.prizeBackgroundLive,
            transactionPrizeBackground: this.formGroup.get('transactionPrizeBackground').value,
            listAdmin: this.listAdmin,
            listSpinAdditional: this.listSpinAdditional,
            templateConfig: {
                subject: this.formGroup.get('emailTemplateSubject').value,
                body: this.formGroup.get('emailTemplateBody').value,
                smsBrandName: this.formGroup.get('smsBrandName').value,
                smsContent: this.formGroup.get('smsContent').value,
            },
            interestedArea: this.isOnlineType ? null : interestedArea,
            eventDescription: this.formGroup.get('eventDescription').value,
            limitRegister: this.formGroup.get('limitRegister').value,
            saleUnitNames: this.saleUnitNames,
            timeSlotNames: this.timeSlotNames,
        }

        if (!this.eventId) {
            this.trainingService.createEvent(p).subscribe((res: any) => {
                if (res && res._body) {
                    this.toastr.success("Thành công ", "Tạo sự kiện! ");
                    back && this.onBack();
                }
            })
            return;
        }
        p.id = this.eventId;
        this.trainingService.updateEvent(p).subscribe(res => {
            this.toastr.success("Thành công ", "Cập nhật sự kiện! ");
            back && this.onBack();
        })
    }

    onChangeCheckbox(isPer: boolean) {
        if (isPer) {
            this.isPos = true;
            this.isPerson = false;
        } else {
            this.isPos = false;
            this.isPerson = true;
        }
    }

    addEmployee() {
        if (this.isPos) {
            this.getEmployeePos();
        } else {
            if (this.person && this.person.id) {
                let personMap;
                if (!this.checkEmployee(this.person.email)) {
                    if (!this.person.pos) {
                        personMap = {
                            isFirst: false,
                            posId: '',
                            posName: '',
                            dvbh: '',
                            id: this.person.id,
                            code: this.person.code,
                            name: this.person.name,
                            email: this.person.email,
                            phoneNumber: this.person.phone,
                            cmnd: this.person.identityCode,
                            show: true,
                        }
                        this.listItemEmployee.push(personMap)
                    } else if (!this.checkHasPos(this.person.pos.id)) {
                        personMap = {
                            isFirst: true,
                            posId: this.person.pos.id,
                            posName: this.person.pos.name,
                            dvbh: this.person.pos.name,
                            id: this.person.id,
                            code: this.person.code,
                            name: this.person.name,
                            email: this.person.email,
                            phoneNumber: this.person.phone,
                            cmnd: this.person.identityCode,
                            show: false,
                        }
                        this.listItemEmployee.push(personMap)
                    } else {
                        personMap = {
                            isFirst: false,
                            posId: this.person.pos.id,
                            posName: this.person.pos.name,
                            dvbh: this.person.pos.name,
                            id: this.person.id,
                            code: this.person.code,
                            name: this.person.name,
                            email: this.person.email,
                            phoneNumber: this.person.phone,
                            cmnd: this.person.identityCode,
                            show: false,
                        }
                        this.listItemEmployee.push(personMap)
                    }
                }
                this.formGroup.patchValue({
                    person: '',
                })
                this.trainingService.addEmployee([personMap], this.eventId).subscribe((r: any) => {
                    if (r) {
                        this.toastr.success("Thành công ", "Cập nhật nhân viên! ");
                    }
                })
            }
        }
        if (this.listItemEmployee.length) {
            this.listItemEmployee.sort((a, b) => {
                return a.posId - b.posId;
            })

        }
        this.formGroup.patchValue({
            pos: '',
            person: '',
        })

    }

    getEmployeePos() {
        if (!this.formGroup.get('pos').value) {
            this.toastr.error("Vui lòng chọn đơn vị", "Thêm đơn vị! ");
            return;
        }
        this.trainingService.getListEmployeeInPos(this.formGroup.get('pos').value).subscribe(res => {
            if (res && res.length) {
                let personList = [];
                res.forEach((item, index) => {
                    if (!this.checkEmployee(item.email)) {
                        if (index === 0) {
                            personList.push({
                                isFirst: true,
                                posId: item.pos && item.pos.id,
                                posName: item.pos && item.pos.name,
                                dvbh: item.pos && item.pos.name,
                                id: item.id,
                                code: item.code,
                                name: item.name,
                                email: item.email,
                                phoneNumber: item.phone,
                                cmnd: item.identityCode,
                                show: false,
                            })
                            return;
                        }
                        personList.push({
                            isFirst: false,
                            posId: item.pos && item.pos.id,
                            posName: item.pos && item.pos.name,
                            dvbh: item.pos && item.pos.name,
                            id: item.id,
                            code: item.code,
                            name: item.name,
                            email: item.email,
                            phoneNumber: item.phone,
                            cmnd: item.identityCode,
                            show: false,
                        })
                    }
                })
                this.trainingService.addEmployee(personList, this.eventId).subscribe((r: any) => {
                    if (r) {
                        this.toastr.success("Thành công ", "Cập nhật nhân viên! ");
                        this.listItemEmployee = this.listItemEmployee.concat(personList)
                    }
                })
            }
            this.listItemEmployee.sort((a, b) => {
                return a.posId - b.posId;
            })
        })
    }

    checkEmployee(email) {
        if (this.listItemEmployee && this.listItemEmployee.length) {
            this.listItemEmployee.filter(i => {
                return i.email === email
            })
            return !!this.listItemEmployee.filter(i => i.email === email).length;
        }
        return false;
    }

    deleteEmployee(item) {
        this.trainingService.removeCustomer(item.id).subscribe(res => {
            if (res) {
                this.listItemEmployee = this.listItemEmployee.filter(i => i.id !== item.id);
                this.listItemEmployee.sort((a, b) => {
                    return a.posId - b.posId;
                })
                if (item.isFirst) {
                    const itemFirst = this.listItemEmployee.find(i => i.posId === item.posId);
                    if (itemFirst) {
                        itemFirst.isFirst = true;
                    }
                }
            }
        })
    }


    deleteCustomerAdditional(item , index) {
        this.listSpinAdditional = this.listSpinAdditional.filter((i,temp) => i.id !== item.id || temp !== index);
    }

    setShow(item) {
        this.listItemEmployee.forEach(i => {
            if (i.posId === item.posId) {
                i.show = !i.show;
            }
        })
    }
    getShow(item) {
        return item.show;
    }

    onSelectPerson(item: any) {
        this.formGroup.patchValue({
            person: item.name,
        })
        this.person = item;
    }

    getProject() {
        const p = this.projects.find(i => i.id === this.formGroup.get('projectId').value);
        if (p) {
            return {
                id: p.id,
                name: p.name
            }
        }
        return null;
    }


    getTotalPos() {
        if (this.listItemEmployee && this.listItemEmployee.length) {
            const listItems = this.listItemEmployee.map(i => i.posId)
            return listItems.filter((item, index) => listItems.indexOf(item) === index).length;
        }
        return 0;
    }

    getFormControl(name: string) {
        return this.formGroup.get(name);
    }

    loadListAdmin($event: MatAutocompleteSelectedEvent) {
        const employee = $event.option.value;
        if (employee) {
            // Patch value
            this.formGroup.get('listAdminSearch').setValue('');
            if (this.listAdmin.findIndex((item: any) => item.id === employee.id) === -1) {
                this.listAdmin.push(employee);
            }
        }
    }

    searchSalesAdmin(value: string) {
        if (value) {
            this.isLoadingListAdmin$.next(true);
            this._projectService.searchEmployee(value)
                .pipe(
                    finalize(() => this.isLoadingListAdmin$.next(false)),
                    takeUntil(this.unsubscribe$))
                .subscribe((res) => this.filteredListAdmin = res);
        }
    }

    handleListAdmin(value: any, isRemove: boolean = false) {
        if (value) {
            if (isRemove) {
                this.listAdmin.splice(this.listAdmin.findIndex(member => member.id === value.id), 1);
            } else if (this.listAdmin.findIndex(member => member.id === value.id) < 0) {
                this.formGroup.get('listAdminSearch').reset();
                this.listAdmin.push(value);
                this.filteredListAdmin = this.filteredListAdmin.filter(admin => admin.id !== value.id);
            }
        }
    }

    sendNotification() {
        if (this.getFormControl('notificationContent').invalid) {
            this.getFormControl('notificationContent').markAsTouched();
            this.toastr.error("Vui lòng điền nội dung thông báo sự kiện! ");
            return;
        }
        const params = {
            eventId: this.eventId,
            content: this.getFormControl('notificationContent').value,
        };
        this.trainingService.sendNotification(params).subscribe(res => {
            if (res) {
                this.toastr.success("Thành công ", "Gửi thông báo sự kiện thành công! ");
            }
        })
    }

    downloadTemplate() {
        let link = document.createElement('a');
        link.download = 'Template-Event-Customer';
        link.href = 'assets/excel/Template-Event-Customer.xlsx';
        link.click();
    }

    handleClickInputFile(fileInput) {
        fileInput.value = '';
        fileInput.click();
    }

    import(oEvent) {
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        this.toastr.success('Đang tải lên', 'Danh sách khách mời! ');
        return this.trainingService.importUser(selectedFile, this.eventId).subscribe((res: any) => {
            if (res.status < 400) {
                const result = JSON.parse(res._body);
                if (result.errors) {
                    this.toastr.error(result.errors.message);
                    return;
                }
                this.toastr.success('Thành công', 'Tải danh sách khách mời! ');
                if (result.models.length) {
                    this.importedCustomers = [...this.importedCustomers, ...result.models];
                    this.getCustomer();
                }
            } else {
                this.toastr.error('Lỗi');
            }
        });
    }

    downloadTemplateAdditional() {
        let link = document.createElement('a');
        link.download = 'Template-Event-Customer-Additional';
        link.href = 'assets/excel/Template-Event-Customer-Additional.xlsx';
        link.click();
    }

    handleClickAdditionalInputFile(fileInputAdditional) {
        fileInputAdditional.value = '';
        fileInputAdditional.click();
    }

    importSpinAdditional(oEvent) {
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        this.toastr.success('Đang tải lên', 'Danh sách quay thưởng! ');
        return this.trainingService.importSpinAdditional(selectedFile, this.eventId).subscribe((res: any) => {
            if (res.status < 400) {
                const result = JSON.parse(res._body);
                if (result.errors) {
                    this.toastr.error(result.errors.message);
                    return;
                }
                this.toastr.success('Thành công', 'Tải danh sách quay thưởng! ');
                if (result.listSpinAdditional.length) {
                    this.listSpinAdditional = this.sortPhoneNumber([...result.listSpinAdditional]);;
                }
            } else {
                this.toastr.error('Lỗi');
            }
        });
    }

    downloadImportList() {
        this.trainingService.exportUser(this.eventId).subscribe(res => {});
    }

    downloadRequestDemandList() {
        this.trainingService.exportRequestDemands(this.eventId).subscribe(res => {});
    }

    checkFileExtension(file: File): boolean {
        if (!file) {
            return false;
        }
        return ['xls', 'xlsx'].includes(file.name.split('.').pop().toLocaleLowerCase());
    }
    onBackgroundChange($event, type: string) {
        switch (type) {
            case 'transaction':
                this.formGroup.patchValue({
                    transactionPrizeBackground: $event
                });
                break;
            default:
                this.formGroup.patchValue({
                    prizeBackground: $event
                });
                break;
        }
    }
    // Email
    editEmailContent(control){
        if(!control.value){
            const url = 'assets/data/training-created.html';
            const offlineUrl = 'assets/data/training-created-offline.html';
            if(this.isOnlineType) {
                if(url){
                    this.dataService.getText(url).subscribe(res => {
                        this.openEmailDialog(control, res);
                    })
                } else {
                    this.openEmailDialog(control, control.value);
                }
            } else {
                if(offlineUrl){
                    this.dataService.getText(offlineUrl).subscribe(res => {
                        this.openEmailDialog(control, res);
                    })
                } else {
                    this.openEmailDialog(control, control.value);
                }
            }

        }else {
            this.openEmailDialog(control, control.value);
        }
    }

    openEmailDialog(control: any, body: string){
        const emailDialog = this.dialog.open(EditorPopup, {
            width: '800px',
            data: {
            title: 'Chỉnh sửa mẫu Email',
            isReason: false,
            textCancel: 'Hủy',
            textOk: 'Cập nhật',
            body: body
            }
        });
        emailDialog.afterClosed().subscribe(result => {
            if (typeof result === 'undefined' || !result.execute) {
            return;
            }
            control.setValue(result.body) ;
        });
    }
    goGuestReport(id: string) {
        this.router.navigate([`training/guest-report/${id}`]);
    }

    goAwardReport(id: string) {
        this.router.navigate([`training/award-report/${id}`]);
    }

    goInteractionReport(id: String) {
        const popupSync = this.dialog.open(InteractionReportDialogComponent, {
            width: '500px',
            data: {
                id: this.eventId
            },
            disableClose: true,
            panelClass: 'app-dialog'
        });
    }

    onChangeRadio(event) {
        this.formGroup.patchValue({
            emailTemplateBody: null
        })
        if(event === this.eventTypeEnum.ONLINE) {
            this.isOnlineType = true
        } else {
            this.isOnlineType = false
        }
    }

    sortPhoneNumber(array) {
        return array.sort(function(a, b){
            if(a.phoneNumber < b.phoneNumber) { return -1; }
            if(a.phoneNumber > b.phoneNumber) { return 1; }
            return 0;
        })
    }

    getDataSortChanged(data: any) {
        this.sortBy = (data.sort === 'asc' ? '' : '-') + data.colId;
        this.getCustomer();
    }

    onApply($event) {
        // reset check email
        this.isSelectAllForEmail = false;
        this.selectedToSendEmail = [];

        this.usersPagination.page = 1;
        this.currentFilter = Object.assign({}, $event);
        this.getCustomer(this.currentFilter);
    }

    onRemoveUser(record:FamilyModel){
        const confirmPopup = this.dialog.open(ConfirmPopup, {
            width: '600px',
            data: {
                title: `Xóa người tham dự?`,
                message: `Bạn có chắc chắn muốn xóa khách mời <strong>${record.name}</strong> không?`,
                isReason: false,
                textCancel: 'Hủy',
                textOk: 'Xác nhận',
                contentPadding: 5
            }
        });

        confirmPopup.afterClosed().subscribe(response => {
            if (!response) {
                return;
            }
            if (response.execute) {
                this.removeCustomer(record);
            }
        });
    }
    onRemoveEmployee(record:FamilyModel){
        const confirmPopup = this.dialog.open(ConfirmPopup, {
            width: '600px',
            data: {
                title: `Xóa Nhân viên?`,
                message: `Bạn có chắc chắn muốn xóa nhân viên <strong>${record.name}</strong> không?`,
                isReason: false,
                textCancel: 'Hủy',
                textOk: 'Xác nhận',
                contentPadding: 5
            }
        });

        confirmPopup.afterClosed().subscribe(response => {
            if (!response) {
                return;
            }
            if (response.execute) {
                this.deleteEmployee(record);
            }
        });
    }

    editSmsContent(control: any){
        const smsDialog = this.dialog.open(TextareaPopup, {
          width: '800px',
          data: {
            title: 'Chỉnh sửa mẫu SMS',
            isReason: false,
            textCancel: 'Hủy',
            textOk: 'Cập nhật',
            body: control.value
          }
        });
        smsDialog.afterClosed().subscribe(result => {
          if (typeof result === 'undefined' || !result.execute) {
            return;
          }
          control.setValue(result.body) ;
        });
      }
}
