import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { AuthApiService, UserAccount } from '../../../shared/services/auth/auth-api.service';
import { ESaleKitService } from './e-sale-kit.service';
import { 
  EDIT_PERMISSION, 
  EDIT_PERMISSION_OVERVIEW,
  EDIT_PERMISSION_GROUND,
  EDIT_PERMISSION_UTILITY,
  EDIT_PERMISSION_PRODUCT,
  EDIT_PERMISSION_TERM,
  EDIT_PERMISSION_MEDIA,
  EDIT_PERMISSION_DOCUMENT,
  EDIT_PERMISSION_NEWS
} from '../constants/constants';

export interface ESaleKitPermissions {
  canEdit: boolean;
  canEditOverview: boolean;
  canEditGround: boolean;
  canEditUtility: boolean;
  canEditProduct: boolean;
  canEditTerm: boolean;
  canEditMedia: boolean;
  canEditDocument: boolean;
  canEditNews: boolean;
  canView: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ESaleKitAuthService {
  private permissionsSubject = new BehaviorSubject<ESaleKitPermissions>({
    canEdit: false,
    canEditOverview: false,
    canEditGround: false,
    canEditUtility: false,
    canEditProduct: false,
    canEditTerm: false,
    canEditMedia: false,
    canEditDocument: false,
    canEditNews: false,
    canView: false
  });

  public permissions$ = this.permissionsSubject.asObservable();
  public isEdit$ = new BehaviorSubject<boolean>(false);

  constructor(
    private authApiService: AuthApiService,
    private eSaleKitService: ESaleKitService
  ) {
    this.initializePermissions();
  }

  /**
   * Khởi tạo permissions khi service được tạo
   */
  private initializePermissions(): void {
    // Lắng nghe thay đổi authorities từ AuthApiService
    this.authApiService.authorities$.subscribe(() => {
      this.updatePermissions();
    });

    // Load permissions ban đầu
    this.updatePermissions();
  }

  /**
   * Cập nhật permissions dựa trên authorities của user
   */
  private updatePermissions(): void {
    const currentUser = this.authApiService.getCurrentUser();
    const authorities = this.authApiService.getCurrentAuthorities();

    if (!currentUser || !authorities) {
      this.resetPermissions();
      return;
    }

    const permissions: ESaleKitPermissions = {
      canEdit: this.authApiService.hasAuthority(EDIT_PERMISSION),
      canEditOverview: this.authApiService.hasAuthority(EDIT_PERMISSION_OVERVIEW),
      canEditGround: this.authApiService.hasAuthority(EDIT_PERMISSION_GROUND),
      canEditUtility: this.authApiService.hasAuthority(EDIT_PERMISSION_UTILITY),
      canEditProduct: this.authApiService.hasAuthority(EDIT_PERMISSION_PRODUCT),
      canEditTerm: this.authApiService.hasAuthority(EDIT_PERMISSION_TERM),
      canEditMedia: this.authApiService.hasAuthority(EDIT_PERMISSION_MEDIA),
      canEditDocument: this.authApiService.hasAuthority(EDIT_PERMISSION_DOCUMENT),
      canEditNews: this.authApiService.hasAuthority(EDIT_PERMISSION_NEWS),
      canView: true // Mặc định cho phép xem
    };

    this.permissionsSubject.next(permissions);
    this.isEdit$.next(permissions.canEdit);
  }

  /**
   * Reset tất cả permissions về false
   */
  private resetPermissions(): void {
    const permissions: ESaleKitPermissions = {
      canEdit: false,
      canEditOverview: false,
      canEditGround: false,
      canEditUtility: false,
      canEditProduct: false,
      canEditTerm: false,
      canEditMedia: false,
      canEditDocument: false,
      canEditNews: false,
      canView: false
    };

    this.permissionsSubject.next(permissions);
    this.isEdit$.next(false);
  }

  /**
   * Lấy permissions hiện tại
   */
  public getCurrentPermissions(): ESaleKitPermissions {
    return this.permissionsSubject.value;
  }

  /**
   * Kiểm tra quyền chỉnh sửa chung
   */
  public canEdit(): boolean {
    return this.permissionsSubject.value.canEdit;
  }

  /**
   * Kiểm tra quyền chỉnh sửa theo module cụ thể
   */
  public canEditModule(module: string): boolean {
    const permissions = this.permissionsSubject.value;
    
    switch (module.toLowerCase()) {
      case 'overview':
        return permissions.canEditOverview;
      case 'ground':
        return permissions.canEditGround;
      case 'utility':
        return permissions.canEditUtility;
      case 'product':
        return permissions.canEditProduct;
      case 'term':
        return permissions.canEditTerm;
      case 'media':
        return permissions.canEditMedia;
      case 'document':
        return permissions.canEditDocument;
      case 'news':
        return permissions.canEditNews;
      default:
        return permissions.canEdit;
    }
  }

  /**
   * Kiểm tra quyền xem
   */
  public canView(): boolean {
    return this.permissionsSubject.value.canView;
  }

  /**
   * Kiểm tra authentication và load user data nếu cần
   */
  public checkAuthenticationAndLoadUser(): Observable<boolean> {
    return this.authApiService.currentUser$.pipe(
      switchMap(user => {
        if (!user) {
          // Nếu chưa có user, thử load từ API
          return this.authApiService.loadUserAccount().pipe(
            map(() => true),
            catchError(() => {
              console.warn('Failed to load user account');
              return [false];
            })
          );
        }
        return [true];
      })
    );
  }

  /**
   * Refresh permissions từ server
   */
  public refreshPermissions(): Observable<ESaleKitPermissions> {
    return this.authApiService.refreshUserData().pipe(
      map(() => {
        this.updatePermissions();
        return this.getCurrentPermissions();
      })
    );
  }

  /**
   * Kiểm tra quyền truy cập project cụ thể
   */
  public canAccessProject(projectId: string): Observable<boolean> {
    return combineLatest([
      this.checkAuthenticationAndLoadUser(),
      this.permissions$
    ]).pipe(
      map(([isAuthenticated, permissions]) => {
        if (!isAuthenticated) {
          return false;
        }

        // Có thể thêm logic kiểm tra quyền truy cập project cụ thể ở đây
        // Ví dụ: kiểm tra user có thuộc organization của project không
        
        return permissions.canView;
      })
    );
  }

  /**
   * Lấy thông tin user hiện tại
   */
  public getCurrentUser(): UserAccount | null {
    return this.authApiService.getCurrentUser();
  }

  /**
   * Kiểm tra user đã đăng nhập chưa
   */
  public isAuthenticated(): boolean {
    return this.authApiService.isAuthenticated();
  }

  /**
   * Đăng xuất
   */
  public logout(): void {
    this.authApiService.logout();
    this.resetPermissions();
  }
}
