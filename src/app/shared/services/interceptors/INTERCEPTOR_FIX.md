# 🔧 TokenInterceptor Fix - Bypass for New API

## 🚨 **Root Cause Found:**

**TokenInterceptor** đang override tất cả Authorization headers với `securityService.GetToken()` (line 29).

```typescript
// TokenInterceptor.intercept() - line 29
headers['Authorization'] = `Bearer ${this.securityService.GetToken()}`;
```

**Vấn đề:**
1. ✅ UserService tạo headers đúng với token từ cookie
2. ❌ TokenInterceptor override Authorization header với token từ SecurityService
3. ❌ SecurityService.GetToken() lấy từ sessionStorage, không phải cookie
4. ❌ API call cuối cùng có sai token

## ✅ **Giải pháp: Skip Interceptor Flag**

### **1. Thêm skip flag trong UserService:**

```typescript
// UserService.loadAccountFromNewApi()
const headersWithSkip = headers.set('skip-token-interceptor', 'true');
return this.http.get(newApiUrl, { headers: headersWithSkip });

// UserService.testApiCall()
const headers = new HttpHeaders({
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
    'skip-token-interceptor': 'true'  // ← Skip interceptor
});
```

### **2. Cập nhật TokenInterceptor:**

```typescript
intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Check if request should skip token interceptor
    if (request.headers.has('skip-token-interceptor')) {
        console.log('Skipping token interceptor for:', request.url);
        // Remove the skip flag and pass through without modifying Authorization
        const modifiedRequest = request.clone({
            headers: request.headers.delete('skip-token-interceptor')
        });
        return next.handle(modifiedRequest);
    }

    // Normal interceptor logic...
    const headers = { timezoneclient: 'Asia/Ho_Chi_Minh', 'X-Client-Code': environment.xClientCode };
    // ... rest of interceptor
}
```

## 🔄 **Luồng hoạt động mới:**

```
UserService.loadAccountFromNewApi()
    ↓
Create headers với token từ cookie + skip flag
    ↓
HttpClient.get(url, { headers })
    ↓
TokenInterceptor.intercept()
    ↓
Check skip-token-interceptor header → SKIP override
    ↓
Remove skip flag → Pass through với original Authorization
    ↓
API call với đúng token từ cookie ✅
```

## 🧪 **Expected Results:**

### **✅ Console logs:**
```
Creating headers with token...
Token for headers: eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
Authorization header value: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
Headers created: ["Authorization", "Content-Type", "skip-token-interceptor"]
Authorization header: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
Headers with skip flag: ["Authorization", "Content-Type", "skip-token-interceptor"]
Making HTTP GET request to: https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account

=== TEST API CALL ===
Test headers: ["Authorization", "Content-Type", "skip-token-interceptor"]
Test Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
Skipping token interceptor for: https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account
✅ Test API Success: {roles: [...]}
```

### **✅ Network tab:**
- **Request URL**: `https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account`
- **Request Headers**:
  ```
  Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9... ← Đúng token từ cookie
  Content-Type: application/json
  ```
- **NO skip-token-interceptor header** (đã bị remove)

### **✅ API Response:**
```json
{
  "roles": [
    {
      "functionRoles": [
        {
          "features": [
            {
              "featureId": "uuid1",
              "featureName": "property.unit.import"
            }
          ]
        }
      ]
    }
  ]
}
```

## 🔍 **Debug Commands:**

```javascript
// Test sau khi fix
userService.testApiCall()
userService.checkTokenAndLoadPermissions().subscribe(console.log)

// Check Network tab có Authorization header đúng không
// Check console có log "Skipping token interceptor" không
```

## 📝 **Key Changes:**

1. **✅ Skip flag**: Thêm `skip-token-interceptor` header
2. **✅ Interceptor logic**: Check flag và skip override
3. **✅ Clean removal**: Remove flag trước khi gửi request
4. **✅ Preserve original**: Giữ nguyên Authorization header từ UserService
5. **✅ Backward compatible**: Không ảnh hưởng đến requests khác

## 🚀 **Test ngay:**

1. **Load trang**: `http://localhost:4400/esalekit/hc04-fpt-3169113062724011`
2. **Check console**: Có log "Skipping token interceptor" không?
3. **Check Network tab**: Authorization header có đúng token không?
4. **Check API response**: Có data roles không?

Bây giờ TokenInterceptor sẽ không override Authorization header cho API này! 🎉
