import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthApiService } from '../../../shared/services/auth/auth-api.service';
import { ESaleKitAuthService } from '../services/e-sale-kit-auth.service';

@Component({
  selector: 'app-auth-test',
  template: `
    <div class="auth-test-container">
      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Test Authentication với access_token từ cookies</mat-card-title>
          <mat-card-subtitle>Kiểm tra việc lấy thông tin quyền từ API CRM</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Token Status -->
          <div class="section">
            <h3>Token Status</h3>
            <div class="status-item">
              <strong>Token có sẵn:</strong> 
              <span [class]="hasToken ? 'success' : 'error'">
                {{ hasToken ? 'Có' : 'Không' }}
              </span>
            </div>
            <div class="status-item" *ngIf="hasToken">
              <strong>Token:</strong> 
              <code class="token-display">{{ tokenDisplay }}</code>
            </div>
          </div>

          <!-- User Info -->
          <div class="section">
            <h3>Thông tin User</h3>
            <div *ngIf="currentUser; else noUser">
              <div class="status-item">
                <strong>Username:</strong> {{ currentUser.username }}
              </div>
              <div class="status-item">
                <strong>Email:</strong> {{ currentUser.email }}
              </div>
              <div class="status-item">
                <strong>Full Name:</strong> {{ currentUser.fullName }}
              </div>
            </div>
            <ng-template #noUser>
              <div class="error">Chưa có thông tin user</div>
            </ng-template>
          </div>

          <!-- Authorities -->
          <div class="section">
            <h3>Authorities</h3>
            <div *ngIf="authorities && authorities.length > 0; else noAuthorities">
              <mat-chip-list>
                <mat-chip *ngFor="let authority of authorities" 
                         [color]="authority.includes('esalekit') ? 'primary' : 'accent'"
                         selected>
                  {{ authority }}
                </mat-chip>
              </mat-chip-list>
            </div>
            <ng-template #noAuthorities>
              <div class="error">Chưa có authorities</div>
            </ng-template>
          </div>

          <!-- E-Sale-Kit Permissions -->
          <div class="section">
            <h3>E-Sale-Kit Permissions</h3>
            <div class="permissions-grid">
              <div class="permission-item">
                <strong>Can View:</strong>
                <span [class]="permissions.canView ? 'success' : 'error'">
                  {{ permissions.canView ? 'Có' : 'Không' }}
                </span>
              </div>
              <div class="permission-item">
                <strong>Can Edit:</strong>
                <span [class]="permissions.canEdit ? 'success' : 'error'">
                  {{ permissions.canEdit ? 'Có' : 'Không' }}
                </span>
              </div>
              <div class="permission-item">
                <strong>Can Edit Overview:</strong>
                <span [class]="permissions.canEditOverview ? 'success' : 'error'">
                  {{ permissions.canEditOverview ? 'Có' : 'Không' }}
                </span>
              </div>
              <div class="permission-item">
                <strong>Can Edit Media:</strong>
                <span [class]="permissions.canEditMedia ? 'success' : 'error'">
                  {{ permissions.canEditMedia ? 'Có' : 'Không' }}
                </span>
              </div>
              <div class="permission-item">
                <strong>Can Edit Document:</strong>
                <span [class]="permissions.canEditDocument ? 'success' : 'error'">
                  {{ permissions.canEditDocument ? 'Có' : 'Không' }}
                </span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="section">
            <h3>Actions</h3>
            <div class="actions">
              <button mat-raised-button color="primary" (click)="refreshData()" [disabled]="isLoading">
                <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
                <span *ngIf="!isLoading">Refresh Data</span>
                <span *ngIf="isLoading">Loading...</span>
              </button>

              <button mat-raised-button color="warn" (click)="clearToken()">
                Clear Token
              </button>

              <button mat-raised-button (click)="testApiCall()" [disabled]="!hasToken">
                Test API Call
              </button>

              <button mat-raised-button color="accent" (click)="debugCookies()">
                Debug Cookies
              </button>

              <button mat-raised-button (click)="setTestToken()" *ngIf="isLocalhost">
                Set Test Token (Localhost)
              </button>

              <button mat-raised-button color="warn" (click)="testCookiePersistence()" *ngIf="isLocalhost">
                Test Cookie Persistence
              </button>
            </div>
          </div>

          <!-- Error Display -->
          <div class="section" *ngIf="errorMessage">
            <h3>Error</h3>
            <div class="error-display">
              <mat-icon>error</mat-icon>
              <span>{{ errorMessage }}</span>
            </div>
          </div>

          <!-- API Response -->
          <div class="section" *ngIf="lastApiResponse">
            <h3>Last API Response</h3>
            <pre class="api-response">{{ lastApiResponse | json }}</pre>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Demo Permissions -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Demo Permission Directive</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div *hasPermission="'esalekit.edit'" class="demo-section">
            <h4>✅ Hiển thị khi có quyền 'esalekit.edit'</h4>
            <p>Bạn có quyền chỉnh sửa E-Sale-Kit</p>
          </div>

          <div *hasPermission="'esalekit.edit.media'" class="demo-section">
            <h4>✅ Hiển thị khi có quyền 'esalekit.edit.media'</h4>
            <p>Bạn có quyền quản lý media</p>
          </div>

          <div *hasPermission="['esalekit.edit', 'esalekit.admin']" class="demo-section">
            <h4>✅ Hiển thị khi có ít nhất một trong các quyền</h4>
            <p>Bạn có quyền edit hoặc admin</p>
          </div>

          <div *hasPermission="['esalekit.edit', 'esalekit.admin']; requireAll: true" class="demo-section">
            <h4>✅ Hiển thị khi có TẤT CẢ các quyền</h4>
            <p>Bạn có cả quyền edit và admin</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .auth-test-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .test-card, .demo-card {
      margin-bottom: 20px;
    }

    .section {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e0e0e0;
    }

    .section:last-child {
      border-bottom: none;
    }

    .status-item, .permission-item {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .permissions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
    }

    .success {
      color: #4caf50;
      font-weight: bold;
    }

    .error {
      color: #f44336;
      font-weight: bold;
    }

    .token-display {
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      word-break: break-all;
      max-width: 300px;
      display: inline-block;
    }

    .actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .error-display {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #f44336;
      background: #ffebee;
      padding: 12px;
      border-radius: 4px;
    }

    .api-response {
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
    }

    .demo-section {
      background: #e8f5e8;
      padding: 12px;
      margin: 8px 0;
      border-radius: 4px;
      border-left: 4px solid #4caf50;
    }

    mat-chip-list {
      margin: 8px 0;
    }
  `]
})
export class AuthTestComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  hasToken = false;
  tokenDisplay = '';
  currentUser: any = null;
  authorities: string[] = [];
  permissions: any = {
    canView: false,
    canEdit: false,
    canEditOverview: false,
    canEditMedia: false,
    canEditDocument: false
  };
  isLoading = false;
  errorMessage = '';
  lastApiResponse: any = null;
  isLocalhost = false;

  constructor(
    private authApiService: AuthApiService,
    private eSaleKitAuthService: ESaleKitAuthService
  ) {}

  ngOnInit(): void {
    // Kiểm tra localhost
    this.isLocalhost = window.location.hostname === 'localhost' ||
                      window.location.hostname === '127.0.0.1' ||
                      window.location.hostname.includes('localhost');

    this.updateTokenStatus();
    this.subscribeToChanges();

    // Auto load data nếu có token
    if (this.hasToken) {
      this.refreshData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateTokenStatus(): void {
    const token = this.authApiService.getTokenFromCookie();
    this.hasToken = !!token;
    this.tokenDisplay = token ? `${token.substring(0, 20)}...` : '';
  }

  private subscribeToChanges(): void {
    // Lắng nghe thay đổi user
    this.authApiService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUser = user;
      });

    // Lắng nghe thay đổi authorities
    this.authApiService.authorities$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authorities => {
        this.authorities = authorities;
      });

    // Lắng nghe thay đổi permissions
    this.eSaleKitAuthService.permissions$
      .pipe(takeUntil(this.destroy$))
      .subscribe(permissions => {
        this.permissions = permissions;
      });
  }

  refreshData(): void {
    this.isLoading = true;
    this.errorMessage = '';
    this.updateTokenStatus();

    if (!this.hasToken) {
      this.errorMessage = 'Không tìm thấy access_token trong cookies';
      this.isLoading = false;
      return;
    }

    this.authApiService.loadUserAccount().subscribe({
      next: (user) => {
        this.lastApiResponse = user;
        this.isLoading = false;
        console.log('Data refreshed successfully');
      },
      error: (error) => {
        this.errorMessage = error.message || 'Có lỗi xảy ra khi gọi API';
        this.isLoading = false;
        console.error('Error refreshing data:', error);
      }
    });
  }

  clearToken(): void {
    this.authApiService.removeTokenFromCookie();
    this.updateTokenStatus();
    this.currentUser = null;
    this.authorities = [];
    this.permissions = {
      canView: false,
      canEdit: false,
      canEditOverview: false,
      canEditMedia: false,
      canEditDocument: false
    };
    this.lastApiResponse = null;
    this.errorMessage = '';
  }

  testApiCall(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authApiService.loadUserAccount().subscribe({
      next: (response) => {
        this.lastApiResponse = response;
        this.isLoading = false;
        console.log('API test successful:', response);
      },
      error: (error) => {
        this.errorMessage = `API Error: ${error.message}`;
        this.isLoading = false;
        console.error('API test failed:', error);
      }
    });
  }

  debugCookies(): void {
    this.authApiService.debugCookies();
  }

  setTestToken(): void {
    if (this.isLocalhost) {
      this.authApiService.setTestTokenForLocalhost();
      // Refresh sau khi set token
      setTimeout(() => {
        this.updateTokenStatus();
      }, 200);
    }
  }

  testCookiePersistence(): void {
    if (this.isLocalhost) {
      console.log('Starting cookie persistence test...');
      this.authApiService.testCookiePersistence();
    }
  }
}
