import { Component, Inject, OnInit } from "@angular/core";
import {
    FormArray,
    FormBuilder,
    FormControl,
    FormGroup,
    Validators
} from "@angular/forms";
import { MatDialogRef } from "@angular/material";
import { MatDialog, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { Constant } from "app/shared/constant/constant";
import { StorageService } from "app/shared/services";
import { UploadFileService } from "app/shared/services/common/upload-file.service";
import { ToastrService } from "ngx-toastr";
import { LivestreamV2Service } from "../live-stream-v2.service";

@Component({
    selector: "app-request-demand-dialog",
    templateUrl: "./request-demand-dialog.component.html",
    styleUrls: ["./request-demand-dialog.component.scss"],
})
export class RequestDemandDialogComponent implements OnInit {
    public form: FormGroup;
    user: any;
    Constant = Constant;

    constructor(
        private dialogRef: MatDialogRef<RequestDemandDialogComponent>,
        private toastr: ToastrService,
        private livestreamService: LivestreamV2Service,
        public matDialog: MatDialog,
        private storageService: StorageService,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {}

    ngOnInit() {
        this.getUserById();
        this.initForm();
    }

    getUserById() {
        this.livestreamService
            .getUserById(this.data.userId)
            .subscribe((res) => {
                this.user = res;
                this.patchData();
            });
    }

    initForm() {
        this.form = new FormGroup({
            name: new FormControl(
                null,
                Validators.compose([
                    Validators.required,
                    Validators.maxLength(25),
                ])
            ),
            phoneNumber: new FormControl(
                null,
                Validators.compose([
                    Validators.required,
                    Validators.minLength(6),
                    Validators.pattern(Constant.REGEX_NUMBER),
                ])
            ),
            cmnd: new FormControl(
                null,
                Validators.compose([
                    Validators.required,
                    Validators.minLength(6),
                    Validators.pattern(Constant.REGEX_NUMBER),
                ])
            ),
            email: new FormControl(
                null,
                Validators.compose([Validators.pattern(Constant.REGEX_EMAIL)])
            ),
            requestDemand: new FormControl(
                null,
                Validators.compose([Validators.required])
            ),
        });
    }

    patchData() {
        this.form.patchValue({
            name: this.user && this.user.name ? this.user.name : "",
            phoneNumber:
                this.user && this.user.phoneNumber ? this.user.phoneNumber : "",
            cmnd:
                this.user && this.user.cmnd ? this.user.cmnd : "",
            email: "",
            requestDemand: "",
        });

        // if (this.user && this.user.name) {
        //     this.form.get('name').disable();
        // }
        if (this.user && this.user.phoneNumber ) {
            if(this.user.isUpdatedProfile){
                this.form.get('phoneNumber').disable();
            } else {
                this.form.get('phoneNumber').setValue('');
            }
        }
        if (this.user && this.user.cmnd ) {
            if(this.user.isUpdatedProfile){
                this.form.get('cmnd').disable();
            } else {
                this.form.get('cmnd').setValue('');
            }
        }
        if (this.user && this.user.email) {
            this.form.get('email').setValue(this.user.email);
        }
        if (this.user && this.user.requestDemand) {
            this.form.get('requestDemand').setValue(this.user.requestDemand);
        }
    }

    isControlInvalid(formControl: string): boolean {
        return (
            this.form.get(formControl).invalid &&
            (this.form.get(formControl).dirty ||
                this.form.get(formControl).touched)
        );
    }

    onSubmit() {
        this.markAsTouched(this.form);
        if (this.form.invalid) {
            return;
        }

        const body = this.form.getRawValue();
        body.userId = this.data.userId;
        delete body.id;

        this.livestreamService.requestDemand(this.data.eventId, body).subscribe(
            (data) => {
                this.storageService.store('name', body.name);
                this.storageService.store('phoneNumber', body.phoneNumber);
                this.storageService.store('cmnd', body.cmnd);
                this.toastr.info(
                    "Gửi yêu cầu tư vấn/ đặt hàng thành công, đội ngũ chúng tôi sẽ liên hệ với quý khách sớm nhất"
                );
                this.closeDialog();
            },
            (error) => {}
        );
    }

    private markAsTouched(group: FormGroup | FormArray) {
        group.markAsTouched({ onlySelf: true });

        Object.keys(group.controls).map((field) => {
            const control = group.get(field);
            if (control instanceof FormControl) {
                control.markAsTouched({ onlySelf: true });
            } else if (control instanceof FormGroup) {
                this.markAsTouched(control);
            }
        });
    }

    closeDialog() {
        this.dialogRef.close();
    }
}
