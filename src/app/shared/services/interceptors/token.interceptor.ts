import { Injectable } from '@angular/core';
import {
    HttpRequest,
    HttpHandler,
    HttpEvent,
    HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { SecurityService } from '..';
import { environment } from 'environments/environment';
import { ErpService } from '../common/erp.service';
import { AuthApiService } from '../auth/auth-api.service';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {

    constructor(
        private securityService: SecurityService,
        private erpService: ErpService,
        private authApiService: AuthApiService
    ) {
    }

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        const headers = { timezoneclient: 'Asia/Ho_Chi_Minh', 'X-Client-Code': environment.xClientCode };

        // Kiểm tra nếu request đến API CRM mới
        if (request.url.includes('uat-api-crm.datxanh.com.vn')) {
            const token = this.authApiService.getTokenFromCookie();
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }
        // Kiểm tra nếu request đến ERP
        else if (request.url.includes(this.erpService.url)) {
            if (this.erpService.checkTokenErp()) {
                headers['Authorization'] = `Bearer ${this.erpService.getToken()}`;
            } else {
                return;
            }
        }
        // Các request khác sử dụng token cũ
        else {
            const oldToken = this.securityService.GetToken();
            const newToken = this.authApiService.getTokenFromCookie();

            // Ưu tiên sử dụng token mới nếu có, fallback về token cũ
            const token = newToken || oldToken;
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }

        if (request.url.includes(environment.voiceCall.uriAPI)) {
            headers['X-STRINGEE-AUTH'] = `${JSON.parse(sessionStorage.getItem(`voice_call_authen_token`))}`;
        }

        request = request.clone({
            setHeaders: headers
        });

        return next.handle(request);
    }
}
