import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, retry, delay } from 'rxjs/operators';
import { CookieService } from 'ngx-cookie-service';
import { environment } from 'environments/environment';

export interface UserAccount {
  id: string;
  username: string;
  email: string;
  fullName: string;
  authorities: string[];
  roles: any[];
  permissions: any[];
  isActive: boolean;
  organizationId?: string;
  departmentId?: string;
}

export interface AuthResponse {
  success: boolean;
  data: UserAccount;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthApiService {
  private readonly API_URL = 'https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account';
  private readonly TOKEN_COOKIE_NAME = 'access_token';

  private currentUserSubject = new BehaviorSubject<UserAccount | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private authoritiesSubject = new BehaviorSubject<string[]>([]);
  public authorities$ = this.authoritiesSubject.asObservable();

  // Config options
  private readonly isLocalhost = window.location.hostname === 'localhost' ||
                                window.location.hostname === '127.0.0.1' ||
                                window.location.hostname.includes('localhost');
  private readonly preserveTokenOnError = this.isLocalhost; // Giữ token khi có lỗi trên localhost

  constructor(
    private http: HttpClient,
    private cookieService: CookieService
  ) {
    this.initializeAuth();
  }

  /**
   * Khởi tạo authentication từ cookie (không auto-load để tránh xóa token)
   */
  private initializeAuth(): void {
    const token = this.getTokenFromCookie();
    if (token) {
      console.log('Token found during initialization, ready for manual load');
      // Không tự động load user để tránh xóa token khi có lỗi network
      // User sẽ được load khi cần thiết (lazy loading)
    } else {
      console.log('No token found during initialization');
    }
  }

  /**
   * Lấy bearer token từ cookie (hỗ trợ localhost)
   */
  public getTokenFromCookie(): string | null {
    try {
      // Thử lấy từ cookie service trước
      let token = this.cookieService.get(this.TOKEN_COOKIE_NAME);

      // Nếu không có, thử lấy trực tiếp từ document.cookie (cho localhost)
      if (!token) {
        token = this.getTokenFromDocumentCookie();
      }

      console.log('Token from cookie:', token ? 'Found' : 'Not found');
      return token || null;
    } catch (error) {
      console.error('Error getting token from cookie:', error);
      return null;
    }
  }

  /**
   * Lấy token trực tiếp từ document.cookie (dành cho localhost)
   */
  private getTokenFromDocumentCookie(): string | null {
    try {
      const cookies = document.cookie.split(';');
      console.log('All cookies:', document.cookie);

      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === this.TOKEN_COOKIE_NAME) {
          console.log('Found token in document.cookie:', name);
          return decodeURIComponent(value);
        }
      }

      console.log('Token not found in document.cookie');
      return null;
    } catch (error) {
      console.error('Error getting token from document.cookie:', error);
      return null;
    }
  }

  /**
   * Lưu token vào cookie (hỗ trợ localhost)
   */
  public setTokenToCookie(token: string, expirationDays: number = 7): void {
    try {
      const expires = new Date();
      expires.setDate(expires.getDate() + expirationDays);

      // Kiểm tra nếu đang chạy trên localhost
      const isLocalhost = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.hostname.includes('localhost');

      if (isLocalhost) {
        // Cho localhost, set cookie đơn giản hơn
        this.cookieService.set(
          this.TOKEN_COOKIE_NAME,
          token,
          expires,
          '/',
          undefined,
          false, // không secure cho localhost
          'Lax' // sameSite lax cho localhost
        );

        // Backup: set trực tiếp vào document.cookie
        const cookieString = `${this.TOKEN_COOKIE_NAME}=${encodeURIComponent(token)}; expires=${expires.toUTCString()}; path=/`;
        document.cookie = cookieString;
        console.log('Token set for localhost:', cookieString);
      } else {
        // Cho production, sử dụng secure settings
        this.cookieService.set(
          this.TOKEN_COOKIE_NAME,
          token,
          expires,
          '/',
          undefined,
          true, // secure
          'Strict' // sameSite
        );
      }

      console.log('Token saved to cookie successfully');
    } catch (error) {
      console.error('Error setting token to cookie:', error);
    }
  }

  /**
   * Xóa token khỏi cookie (hỗ trợ localhost)
   */
  public removeTokenFromCookie(): void {
    try {
      // Xóa bằng cookie service
      this.cookieService.delete(this.TOKEN_COOKIE_NAME, '/');

      // Backup: xóa trực tiếp từ document.cookie (cho localhost)
      const isLocalhost = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.hostname.includes('localhost');

      if (isLocalhost) {
        document.cookie = `${this.TOKEN_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        console.log('Token removed from localhost cookie');
      }

      // Reset user data
      this.currentUserSubject.next(null);
      this.authoritiesSubject.next([]);

      console.log('Token removed successfully');
    } catch (error) {
      console.error('Error removing token from cookie:', error);
    }
  }

  /**
   * Tạo HTTP headers với bearer token
   */
  private createAuthHeaders(): HttpHeaders {
    const token = this.getTokenFromCookie();
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Debug method: Hiển thị thông tin cookies (dành cho localhost testing)
   */
  public debugCookies(): void {
    console.log('=== Cookie Debug Info ===');
    console.log('Current domain:', window.location.hostname);
    console.log('All cookies:', document.cookie);
    console.log('Looking for cookie:', this.TOKEN_COOKIE_NAME);

    // Thử lấy bằng cookie service
    const tokenFromService = this.cookieService.get(this.TOKEN_COOKIE_NAME);
    console.log('Token from CookieService:', tokenFromService || 'Not found');

    // Thử lấy từ document.cookie
    const tokenFromDocument = this.getTokenFromDocumentCookie();
    console.log('Token from document.cookie:', tokenFromDocument || 'Not found');

    // Hiển thị tất cả cookies
    const allCookies = document.cookie.split(';').map(c => c.trim());
    console.log('All cookies array:', allCookies);
    console.log('========================');
  }

  /**
   * Helper method: Set test token cho localhost
   */
  public setTestTokenForLocalhost(testToken: string = 'test-token-123'): void {
    console.log('Setting test token for localhost:', testToken);
    this.setTokenToCookie(testToken);

    // Verify
    setTimeout(() => {
      const retrievedToken = this.getTokenFromCookie();
      console.log('Test token verification:', retrievedToken === testToken ? 'SUCCESS' : 'FAILED');
      this.debugCookies();
    }, 100);
  }

  /**
   * Gọi API để lấy thông tin tài khoản và quyền từ access_token có sẵn trong cookies
   */
  public loadUserAccount(): Observable<UserAccount> {
    const token = this.getTokenFromCookie();

    if (!token) {
      const error = new Error('No access_token found in cookies');
      console.error('Auth Error:', error.message);
      this.handleAuthError();
      return throwError(() => error);
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('Loading user account from API with token from cookies...');

    return this.http.get<AuthResponse>(this.API_URL, { headers }).pipe(
      map(response => {
        if (response.success && response.data) {
          const user = response.data;
          this.currentUserSubject.next(user);
          this.authoritiesSubject.next(user.authorities || []);
          console.log('User account loaded successfully:', {
            username: user.username,
            authorities: user.authorities
          });
          return user;
        } else {
          throw new Error(response.message || 'Failed to load user account');
        }
      }),
      catchError(error => {
        console.error('Error loading user account:', error);

        // Phân loại lỗi để xử lý phù hợp
        const isNetworkError = !error.status || error.status === 0;
        const isServerError = error.status >= 500;
        const isUnauthorized = error.status === 401 || error.status === 403;

        console.log('Error classification:', {
          isNetworkError,
          isServerError,
          isUnauthorized,
          status: error.status,
          preserveTokenOnError: this.preserveTokenOnError
        });

        // Chỉ xóa token khi:
        // 1. Token thực sự invalid (401/403) VÀ
        // 2. Không phải localhost hoặc user cho phép xóa
        if (isUnauthorized && !this.preserveTokenOnError) {
          console.warn('Token invalid and not preserving, removing from cookie');
          this.removeTokenFromCookie();
        } else if (isUnauthorized && this.preserveTokenOnError) {
          console.warn('Token invalid but preserving for localhost development');
          // Chỉ reset user data, không xóa token
          this.currentUserSubject.next(null);
          this.authoritiesSubject.next([]);
        } else if (isNetworkError) {
          console.warn('Network error, keeping token for retry');
          // Không làm gì, giữ nguyên token và user data
        } else if (isServerError) {
          console.warn('Server error, keeping token');
          // Không làm gì, có thể là lỗi tạm thời
        } else {
          console.warn('Other error, handling conservatively');
          // Chỉ reset user data nếu không preserve token
          if (!this.preserveTokenOnError) {
            this.handleAuthError();
          }
        }

        return throwError(() => error);
      })
    );
  }

  /**
   * Kiểm tra quyền của user
   */
  public hasAuthority(authorities: string[]): boolean {
    const userAuthorities = this.authoritiesSubject.value;
    if (!userAuthorities || userAuthorities.length === 0) {
      return false;
    }

    // Kiểm tra xem user có ít nhất một trong các quyền yêu cầu
    return authorities.some(authority => 
      userAuthorities.includes(authority.toLowerCase())
    );
  }

  /**
   * Kiểm tra quyền async
   */
  public hasAuthorityAsync(authorities: string[]): Promise<boolean> {
    return new Promise((resolve) => {
      const currentAuthorities = this.authoritiesSubject.value;
      if (currentAuthorities && currentAuthorities.length > 0) {
        resolve(this.hasAuthority(authorities));
      } else {
        // Nếu chưa có authorities, thử load lại
        this.loadUserAccount().subscribe(
          () => resolve(this.hasAuthority(authorities)),
          () => resolve(false)
        );
      }
    });
  }

  /**
   * Lấy thông tin user hiện tại
   */
  public getCurrentUser(): UserAccount | null {
    return this.currentUserSubject.value;
  }

  /**
   * Lấy danh sách quyền hiện tại
   */
  public getCurrentAuthorities(): string[] {
    return this.authoritiesSubject.value;
  }

  /**
   * Kiểm tra xem user đã đăng nhập chưa
   */
  public isAuthenticated(): boolean {
    const token = this.getTokenFromCookie();
    const user = this.getCurrentUser();
    return !!(token && user);
  }

  /**
   * Xử lý lỗi authentication (không tự động xóa token)
   */
  private handleAuthError(): void {
    this.currentUserSubject.next(null);
    this.authoritiesSubject.next([]);
    // Không tự động xóa token nữa - để caller quyết định
    console.log('Auth error handled, user data cleared but token preserved');
  }

  /**
   * Xử lý lỗi authentication và xóa token (dùng khi chắc chắn cần xóa)
   */
  private handleAuthErrorAndRemoveToken(): void {
    this.currentUserSubject.next(null);
    this.authoritiesSubject.next([]);
    this.removeTokenFromCookie();
    console.log('Auth error handled, user data and token cleared');
  }

  /**
   * Đăng xuất (xóa token và user data)
   */
  public logout(): void {
    this.handleAuthErrorAndRemoveToken();
  }

  /**
   * Refresh thông tin user và quyền
   */
  public refreshUserData(): Observable<UserAccount> {
    return this.loadUserAccount();
  }

  /**
   * Load user data an toàn (không xóa token trừ khi thực sự cần)
   */
  public loadUserAccountSafely(): Observable<UserAccount> {
    console.log('Loading user account safely (preserving token on errors)...');
    return this.loadUserAccount();
  }

  /**
   * Kiểm tra và load user data nếu cần (lazy loading)
   */
  public ensureUserLoaded(): Observable<UserAccount> {
    const currentUser = this.getCurrentUser();
    const token = this.getTokenFromCookie();

    if (currentUser && token) {
      // Đã có user data, trả về ngay
      return new Observable(subscriber => {
        subscriber.next(currentUser);
        subscriber.complete();
      });
    } else if (token) {
      // Có token nhưng chưa có user data, load từ API
      console.log('Token found, loading user data...');
      return this.loadUserAccountSafely();
    } else {
      // Không có token
      const error = new Error('No access_token found');
      return throwError(() => error);
    }
  }

  /**
   * Force reload user data (bỏ qua cache)
   */
  public forceReloadUserData(): Observable<UserAccount> {
    console.log('Force reloading user data...');
    return this.loadUserAccountSafely();
  }

  /**
   * Test method: Kiểm tra tại sao cookie bị xóa
   */
  public testCookiePersistence(): void {
    console.log('=== COOKIE PERSISTENCE TEST ===');
    console.log('Environment:', this.isLocalhost ? 'localhost' : 'production');
    console.log('Preserve token on error:', this.preserveTokenOnError);

    // Test 1: Set và get cookie
    const testToken = 'test-persistence-' + Date.now();
    console.log('1. Setting test token:', testToken);
    this.setTokenToCookie(testToken);

    setTimeout(() => {
      const retrieved = this.getTokenFromCookie();
      console.log('2. Retrieved token:', retrieved);
      console.log('3. Token match:', retrieved === testToken);

      // Test 2: Simulate page reload
      console.log('4. Simulating page reload...');
      setTimeout(() => {
        const afterReload = this.getTokenFromCookie();
        console.log('5. Token after simulated reload:', afterReload);
        console.log('6. Still matches:', afterReload === testToken);

        // Test 3: Try API call with test token
        console.log('7. Testing API call with test token...');
        this.loadUserAccount().subscribe({
          next: (user) => {
            console.log('8. API Success - token preserved');
          },
          error: (error) => {
            console.log('8. API Error:', error.status || 'Network error');
            const tokenAfterError = this.getTokenFromCookie();
            console.log('9. Token after API error:', tokenAfterError ? 'PRESERVED' : 'DELETED');
            console.log('10. Error handling worked correctly:',
              this.preserveTokenOnError ? (tokenAfterError ? 'YES' : 'NO') : 'N/A');
          }
        });
      }, 500);
    }, 200);

    console.log('================================');
  }
}
