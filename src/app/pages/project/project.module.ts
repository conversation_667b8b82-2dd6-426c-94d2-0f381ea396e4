import { DirectivesModule } from 'app/shared/directives/directives.module';
import { LoadingSpinnerModule } from './../../shared/components/loading-spinner/loading-spinner.module';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { ShareComponentModule } from 'app/pages/share-components/share-components.module';
import { SharedModule } from 'app/shared';
import { PropertyImportHistoryComponent } from './history/property-import.history.component';

import {
  PROJECT_ROUTE,
  PROJECT_VIEW_ROUTE,
  PROJECT_CREATE_ROUTE,
  PROJECT_EDIT_ROUTE, PROJECT_EVENT_SALE,
  PROJECT_INVESTOR_LIST,
  PROJECT_EVENT_DASHBOARD,
  PROPERTY_IMPORT_HISTORY,
  PROJECT_EVENT_SALE_ID,
  PROJECT_EMAIL_HISTORY,
  PROJECT_SALES_PROGRAM,
  PROJECT_SALES_PROGRAM_CREATE,
  PROJECT_SALES_PROGRAM_EDIT,
  PROPERTY_SYNC_HISTORY,
  PROPERTY_CARE_PLUS_HISTORY,
  PROJECT_EDIT_BLOCK_PLAN
} from './project.route';
import { ProjectService } from './project.service';
import { InvestorService } from './investor/investor.service';
import { ProjectListComponent } from './list/project.list.component';
import { ProjectEditComponent } from './edit/project.edit.component';
import { EventSaleListComponent } from './event-sale/list/event-sale.list.component';
import { EventSaleTableDealPanelComponent } from './event-sale/table-deal-panel/event-sale.table-deal-panel.component';
import { EventSaleSettingComponent } from './event-sale/setting/event-sale.setting.component';
import { EventSalePropertyTicketComponent } from './event-sale/property-ticket/event-sale.property-ticket.component';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { PropertyPrimaryService } from './event-sale/property-primary.service';
import { PopupPropertyDetail } from './event-sale/popup-property-detail/popup-property-detail';
import { PropertyTicketSubmitDialogComponent } from './dialog/property-ticket-submit-dialog.component';
import { SettingPosDialogComponent } from './setting-pos-dialog/setting-pos-dialog.component';
import { InvestorListComponent } from './investor/investor.list.component';
import { EventDashboardPageComponent } from './event-dashboard-page/event-dashboard-page.component';
import { CirclePercentageComponent } from './event-dashboard-page/circle-percentage/circle-percentage.component';
import { CustomerInformationTableComponent } from './event-dashboard-page/customer-information-table/customer-information-table.component';
import { SaleInformationTableComponent } from './event-dashboard-page/sale-information-table/sale-information-table.component';
import { EmployeeInformationTableComponent } from './event-dashboard-page/employee-information-table/employee-information-table.component';
import { EventTicketDetailSettingComponent } from './event-sale/ticket-detail/ticket-detail.setting.component';
import { NgxPrintModule } from 'ngx-print';
import { PopupEmailComponent } from './event-sale/popup-email/popup-email.component';
import { DialogMessageNoticeComponent } from './event-sale/dialog-message-notice/dialog-message-notice.component';
import { Ng2SearchPipeModule, Ng2SearchPipe } from 'ng2-search-filter';
import { PopupTransferPropertyComponent } from './event-sale/popup-transfer-property/popup-transfer-property.component';
import { PopupTransferSalesProgramComponent } from './event-sale/popup-transfer-sales-program/popup-transfer-sales-program.component';
import { PopupPublishPriceHistoryComponent } from './event-sale/popup-publish-price-history/popup-publish-price-history.component';
import { PopupPublishStatusComponent } from './event-sale/popup-publish-status/popup-publish-status.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { EmailHistoryComponent } from './email/history/email-history.component';
import { EventSalePopupPropertyTicketComponent } from './event-sale/popup-property-ticket/popup-property-ticket.component';
import { DescriptionDialogComponent } from './event-sale/popup-description/popup-description';
import { PopupComfirmERPComponent } from './event-sale/popup-comfirm-erp/popup-comfirm-erp.component';
import { EventSalePopupPropertyTicketDetailComponent } from './event-sale/popup-property-ticket-detail/popup-property-ticket-detail.component';
import { PopupProcessComponent } from './event-sale/popup-process/popup-process.component';
import { PopupExtendPriorityComponent } from './event-sale/popup-extend-priority/popup-extend-priority.component';
import { PropertyService } from '../property';
import { AgGridModule } from 'ag-grid-angular';
import { TemplateRendererComponent } from './event-sale/list/template-renderer/template-renderer.component';
import { HistoryTicketModule } from './event-sale/history-ticket/history-ticket.module';
import { EventNotificationTableComponent } from './event-dashboard-page/event-notification-table/event-notification-table.component';
import { EditProcessInstructionsComponent } from './event-sale/list/edit-process-instructions/edit-process-instructions.component';
import { SalesProgramComponent } from './sales-program/sales-program.component';
import { EditSalesProgramComponent } from './sales-program/edit/edit-sales-program.component';
import { SalesProgramService } from './sales-program.service';
import { UnitTableComponent } from './event-sale/unit-table/unit-table.component';
import { ContractService } from '../contract';
import { PopupStatisticComponent } from './event-sale/popup-statistic/popup-statistic.component';
import { PopupSyncCRMComponent } from './event-sale/popup-sync-crm/popup-sync-crm.component';
import { PropertySyncHistoryComponent } from './sync-history/property-sync.history.component';
import { PopupConfirmPhoneComponent } from 'app/shared/components/popup-confirm-phone/popup-confirm-phone.component';
import { DemandService } from '../demand';
import { HistoryUnitModule } from './event-sale/history-unit/history-unit.module';
import { PopupSyncEscrowCRMComponent } from './event-sale/popup-sync-escrow-crm/popup-sync-escrow-crm.component';
import { PopupCreateUserCareplusComponent } from './event-sale/popup-create-user-careplus/popup-create-user-careplus';
import { PropertyUserCareHistoryComponent } from './user-care-history/property-user-care.history.component';
import { UploadUrlComponent } from './event-sale/upload-url/upload-url.component';
import { UnitDrawnComponent } from './event-sale/unit-drawn/unit-drawn.component';
import { DrawnComponent } from './event-sale/unit-drawn/drawn/drawn.component';
import { DrawnConfigComponent } from './event-sale/unit-drawn/drawn-config/drawn-config.component';
import { DrawnBoxItemComponent } from './event-sale/unit-drawn/draw-box-item/draw-box-item.component';
import {PopupDrawnConfigComponent} from './event-sale/popup-drawn-config/popup-drawn-config.component';
import { UnitDrawnService } from './event-sale/unit-drawn/unit-drawn.service';
import { CountDownComponent } from './event-sale/table-deal-panel/count-down/count-down.component';
import { PopupComfirmImportComponent } from './event-sale/popup-comfirm-import/popup-comfirm-import.component';
import { FinancialSupportComponent } from './event-sale/list/financial-support/financial-support.component';
import { RegisterFinancialComponent } from './event-sale/list/financial-support/register-financial/register-financial.component';
import { HistoryFinancialComponent } from './event-sale/list/financial-support/history-financial/history-financial.component';
import { SuccessFinancialSupportDialog } from './event-sale/list/financial-support/success-financial-support/success-financial-support.component';
import { PopupImagePropertyComponent } from './event-sale/popup-image-property/popup-image-property.component';
import { PopupUploadImagePropertyUnitComponent } from './event-sale/popup-upload-image-property-unit/popup-upload-image-property-unit.component';
import { EditBlockPlanComponent } from './sales-program/edit-block-plan/edit-block-plan.component';
import { DashboardProjectComponent } from './event-sale/list/dashboard-project/dashboard-project.component';
import { PopupPropertyEdit } from './event-sale/popup-property-edit/popup-property-edit';
// import { OnlinePaymentModule } from './../payment/online-payment/online.payment.module';

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild([
      PROJECT_ROUTE,
      PROJECT_VIEW_ROUTE,
      PROJECT_CREATE_ROUTE,
      PROJECT_EDIT_ROUTE,
      PROJECT_EVENT_SALE,
      PROJECT_EVENT_SALE_ID,
      PROJECT_EVENT_DASHBOARD,
      PROPERTY_IMPORT_HISTORY,
      PROJECT_INVESTOR_LIST,
      PROJECT_EMAIL_HISTORY,
      PROJECT_SALES_PROGRAM,
      PROJECT_SALES_PROGRAM_CREATE,
      PROJECT_SALES_PROGRAM_EDIT,
      PROPERTY_SYNC_HISTORY,
      PROPERTY_CARE_PLUS_HISTORY,
      PROJECT_EDIT_BLOCK_PLAN,
    ]),
    AgGridModule.withComponents([
      TemplateRendererComponent,
    ]),
    FormsModule,
    ShareComponentModule,
    ReactiveFormsModule,
    SharedModule,
    PerfectScrollbarModule,
    NgxMaterialTimepickerModule,
    NgxPrintModule,
    Ng2SearchPipeModule,
    NgSelectModule,
    LoadingSpinnerModule,
    DirectivesModule,
    HistoryTicketModule,
    HistoryUnitModule,
    // OnlinePaymentModule
  ],
  declarations: [
    ProjectListComponent,
    ProjectEditComponent,
    EventSaleListComponent,
    EventSaleTableDealPanelComponent,
    EventSaleSettingComponent,
    PopupPropertyDetail,
    PopupPropertyEdit,
    EventSalePropertyTicketComponent,
    PropertyTicketSubmitDialogComponent,
    SettingPosDialogComponent,
    InvestorListComponent,
    EventDashboardPageComponent,
    CirclePercentageComponent,
    CustomerInformationTableComponent,
    SaleInformationTableComponent,
    EmployeeInformationTableComponent,
    PropertyImportHistoryComponent,
    EventTicketDetailSettingComponent,
    PopupEmailComponent,
    DialogMessageNoticeComponent,
    PopupTransferPropertyComponent,
    PopupTransferSalesProgramComponent,
    PopupPublishPriceHistoryComponent,
    PopupPublishStatusComponent,
    EmailHistoryComponent,
    EventSalePopupPropertyTicketComponent,
    DescriptionDialogComponent,
    PopupComfirmERPComponent,
    EventSalePopupPropertyTicketDetailComponent,
    PopupProcessComponent,
    PopupExtendPriorityComponent,
    EventNotificationTableComponent,
    EditProcessInstructionsComponent,
    SalesProgramComponent,
    EditSalesProgramComponent,
    UnitTableComponent,
    PopupStatisticComponent,
    PopupSyncCRMComponent,
    PropertySyncHistoryComponent,
    PopupSyncEscrowCRMComponent,
    PopupCreateUserCareplusComponent,
    PropertyUserCareHistoryComponent,
    UploadUrlComponent,
    UnitDrawnComponent,
    DrawnComponent,
    DrawnConfigComponent,
    DrawnBoxItemComponent,
    PopupDrawnConfigComponent,
    CountDownComponent,
    PopupComfirmImportComponent,
    FinancialSupportComponent,
    RegisterFinancialComponent,
    HistoryFinancialComponent,
    SuccessFinancialSupportDialog,
    PopupImagePropertyComponent,
    PopupUploadImagePropertyUnitComponent,
    EditBlockPlanComponent,
    DashboardProjectComponent
  ],
  exports: [
    ProjectListComponent,
    ProjectEditComponent,
    InvestorListComponent,
    PopupPropertyDetail,
    PopupPropertyEdit,
    PopupStatisticComponent
  ],
  entryComponents: [
    PopupComfirmImportComponent,
    PopupPropertyDetail,
    PopupPropertyEdit,
    PropertyTicketSubmitDialogComponent,
    SettingPosDialogComponent,
    PopupEmailComponent,
    DialogMessageNoticeComponent,
    PopupTransferPropertyComponent,
    PopupTransferSalesProgramComponent,
    PopupPublishPriceHistoryComponent,
    PopupPublishStatusComponent,
    EventSalePopupPropertyTicketComponent,
    DescriptionDialogComponent,
    PopupComfirmERPComponent,
    EventSalePopupPropertyTicketDetailComponent,
    PopupProcessComponent,
    PopupExtendPriorityComponent,
    PopupStatisticComponent,
    PopupSyncCRMComponent,
    PopupConfirmPhoneComponent,
	  PopupSyncEscrowCRMComponent,
    PopupCreateUserCareplusComponent,
    UploadUrlComponent,
    PopupDrawnConfigComponent,
    RegisterFinancialComponent,
    HistoryFinancialComponent,
    SuccessFinancialSupportDialog,
    PopupImagePropertyComponent,
    PopupUploadImagePropertyUnitComponent
  ],
  providers: [
    ProjectService,
    PropertyPrimaryService,
    InvestorService,
    Ng2SearchPipe,
    PropertyService,
    SalesProgramService,
    ContractService,
    DemandService,
    UnitDrawnService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ProjectModule { }
