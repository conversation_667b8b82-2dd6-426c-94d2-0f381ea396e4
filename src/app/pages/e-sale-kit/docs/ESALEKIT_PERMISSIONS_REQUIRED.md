# 🔐 E-Sale-Kit Permissions Required

## 📋 **Tổng quan quyền cần thiết:**

E-Sale-Kit module cần các quyền sau để hoạt động đầy đủ:

## 🎯 **Core Permissions:**

### **1. Quyền chính - EDIT_PERMISSION:**
```typescript
EDIT_PERMISSION = ['esalekit', 'edit']
```
- **Mục đích**: Quyền chỉnh sửa tổng quát cho e-sale-kit
- **Sử dụng tại**: Menu components, guards, general edit functions

### **2. Quyền chi tiết theo từng section:**

#### **A. Overview (Tổng quan):**
```typescript
EDIT_PERMISSION_OVERVIEW = ['esalekit', 'edit', 'overview']
```

#### **B. Ground/Mat bang (Mặt bằng):**
```typescript
EDIT_PERMISSION_GROUND = ['esalekit', 'edit', 'ground']
```

#### **C. Utility (Tiện ích):**
```typescript
EDIT_PERMISSION_UTILITY = ['esalekit', 'edit', 'utility']
```

#### **D. Product (Sản phẩm):**
```typescript
EDIT_PERMISSION_PRODUCT = ['esalekit', 'edit', 'product']
```

#### **E. Term (Chính sách mua hàng):**
```typescript
EDIT_PERMISSION_TERM = ['esalekit', 'edit', 'term']
```

#### **F. Media (Thư viện hình ảnh):**
```typescript
EDIT_PERMISSION_MEDIA = ['esalekit', 'edit', 'media']
```

#### **G. Document (Tài liệu dự án):**
```typescript
EDIT_PERMISSION_DOCUMENT = ['esalekit', 'edit', 'document']
```

#### **H. News (Tin tức):**
```typescript
EDIT_PERMISSION_NEWS = ['esalekit', 'edit', 'news']
```

## 🗂️ **Permissions theo Menu Structure:**

### **Menu ID mapping với permissions:**

| Menu ID | Menu Name | Permission Required |
|---------|-----------|-------------------|
| `1` | Tổng quan | `['esalekit', 'edit', 'overview']` |
| `2` | Mặt bằng | `['esalekit', 'edit', 'ground']` |
| `3` | Tiện ích | `['esalekit', 'edit', 'utility']` |
| `4` | Sản phẩm | `['esalekit', 'edit', 'product']` |
| `5` | Chính sách mua hàng | `['esalekit', 'edit', 'term']` |
| `6` | Thư viện hình ảnh | `['esalekit', 'edit', 'media']` |
| `7` | Tài liệu dự án | `['esalekit', 'edit', 'document']` |
| `8` | Tin tức | `['esalekit', 'edit', 'news']` |
| `9` | Công cụ tính vay | `['esalekit', 'edit']` (general) |

## 🔧 **Cách permissions được sử dụng:**

### **1. Menu Components:**
```typescript
// e-menu.component.ts & e-short-menu.component.ts
checkPermission() {
  this.authorizeService.hasAuthority(EDIT_PERMISSION)
    .then(isHasPermission => {
      this.hasEditPermission = isHasPermission;
    });
}

getEditPermission(item) {
  switch (item.id) {
    case '1': return EDIT_PERMISSION_OVERVIEW;
    case '3': return EDIT_PERMISSION_UTILITY;
    case '4': return EDIT_PERMISSION_PRODUCT;
    case '5': return EDIT_PERMISSION_TERM;
    default: return;
  }
}
```

### **2. HTML Templates với userAuthority directive:**
```html
<!-- Edit buttons chỉ hiển thị khi có quyền -->
<div *userAuthority="getEditPermission(item)">
  <div class="icon-edit" (click)="onEditMenuBtnClicked(sub)">
    <img src="assets/images/esalekit/pen.svg">
  </div>
</div>

<!-- Add menu button -->
<li *userAuthority="getEditPermission(item)">
  <a (click)="onAddMenuBtnClicked(item)">Thêm sub menu</a>
</li>
```

### **3. Route Guards:**
```typescript
// confirm-deactivate.guard.ts
async canDeactivate(target: any) {
  const canEdit = await target.authorizeService.hasAuthority(EDIT_PERMISSION);
  if (target.hasChanges && canEdit) {
    return await this.handleConfirm();
  }
  return true;
}
```

### **4. Component Level Checks:**
```typescript
// project-common-page.component.ts
async checkPermission() {
  const authorities = this.editPermissions; // Dynamic permissions
  await this.authorizeService.hasAuthority(authorities).then(result => {
    if (result) {
      this.isHasPermission = true;
    }
  });
}
```

## 🎯 **Minimum Required Permissions:**

### **Để sử dụng e-sale-kit cơ bản (view only):**
- **Không cần permissions đặc biệt** - chỉ cần access token hợp lệ

### **Để chỉnh sửa e-sale-kit:**
```typescript
// Minimum permissions for editing:
[
  ['esalekit', 'edit'],                    // General edit permission
  ['esalekit', 'edit', 'overview'],       // Edit overview section
  ['esalekit', 'edit', 'ground'],         // Edit ground plan
  ['esalekit', 'edit', 'utility'],        // Edit utilities
  ['esalekit', 'edit', 'product'],        // Edit products
  ['esalekit', 'edit', 'term'],           // Edit terms/policies
  ['esalekit', 'edit', 'media'],          // Edit media gallery
  ['esalekit', 'edit', 'document'],       // Edit documents
  ['esalekit', 'edit', 'news']            // Edit news
]
```

## 🔍 **Permission Check Flow:**

```
User loads e-sale-kit page
    ↓
checkTokenAndLoadPermissions() → Load permissions from API
    ↓
Components call checkPermission()
    ↓
authorizeService.hasAuthority(permission_array)
    ↓
Check sessionStorage['permissions'] structure
    ↓
Show/Hide edit buttons based on permissions
```

## 📊 **Expected Permission Structure in sessionStorage:**

```json
{
  "esalekit": {
    "edit": {
      "isAuthor": true,
      "overview": { "isAuthor": true },
      "ground": { "isAuthor": true },
      "utility": { "isAuthor": true },
      "product": { "isAuthor": true },
      "term": { "isAuthor": true },
      "media": { "isAuthor": true },
      "document": { "isAuthor": true },
      "news": { "isAuthor": true }
    }
  }
}
```

## 🧪 **Test Commands:**

```javascript
// Check if user has edit permissions
authorizeService.hasAuthority(['esalekit', 'edit'])

// Check specific section permissions
authorizeService.hasAuthority(['esalekit', 'edit', 'overview'])
authorizeService.hasAuthority(['esalekit', 'edit', 'product'])

// Check current permissions in sessionStorage
JSON.parse(sessionStorage.getItem('permissions'))

// Check if edit buttons are visible
document.querySelectorAll('[userAuthority]').length
```

## 📝 **Summary:**

**E-Sale-Kit cần các quyền sau:**
1. **`['esalekit', 'edit']`** - Quyền chỉnh sửa chung
2. **`['esalekit', 'edit', 'overview']`** - Chỉnh sửa tổng quan
3. **`['esalekit', 'edit', 'ground']`** - Chỉnh sửa mặt bằng
4. **`['esalekit', 'edit', 'utility']`** - Chỉnh sửa tiện ích
5. **`['esalekit', 'edit', 'product']`** - Chỉnh sửa sản phẩm
6. **`['esalekit', 'edit', 'term']`** - Chỉnh sửa chính sách
7. **`['esalekit', 'edit', 'media']`** - Chỉnh sửa thư viện
8. **`['esalekit', 'edit', 'document']`** - Chỉnh sửa tài liệu
9. **`['esalekit', 'edit', 'news']`** - Chỉnh sửa tin tức

**Nếu không có permissions này, user chỉ có thể view e-sale-kit, không thể chỉnh sửa.**
