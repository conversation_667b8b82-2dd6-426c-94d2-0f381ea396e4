# 🔧 Cookie Persistence Fix - Tóm tắt thay đổi

## 🚨 Vấn đề đã được fix

**Nguyên nhân chính**: <PERSON><PERSON> `access_token` bị xóa tự động khi load lại trang trên localhost do:

1. **Constructor tự động gọi API** → Lỗi network/CORS → **Xóa token**
2. **X<PERSON> lý lỗi quá aggressive** → Xóa token với mọi loại lỗi
3. **Không phân biệt môi trường** → Localhost bị treat như production

## ✅ Các thay đổi đã thực hiện

### 1. **AuthApiService - Thay đổi chính**

#### **A. Thêm config cho localhost**
```typescript
private readonly isLocalhost = window.location.hostname === 'localhost' || 
                              window.location.hostname === '127.0.0.1' ||
                              window.location.hostname.includes('localhost');
private readonly preserveTokenOnError = this.isLocalhost; // Giữ token khi có lỗi trên localhost
```

#### **B. Sửa initializeAuth() - Không auto-load**
```typescript
// TRƯỚC: Tự động load user → Có thể xóa token
this.loadUserAccount().subscribe();

// SAU: Chỉ log, không auto-load
console.log('Token found during initialization, ready for manual load');
// Không tự động load user để tránh xóa token khi có lỗi network
```

#### **C. Sửa xử lý lỗi trong loadUserAccount()**
```typescript
// TRƯỚC: Xóa token với mọi lỗi 401/403
if (error.status === 401 || error.status === 403) {
  this.removeTokenFromCookie(); // ← Quá aggressive!
}

// SAU: Phân loại lỗi và xử lý thông minh
const isNetworkError = !error.status || error.status === 0;
const isServerError = error.status >= 500;
const isUnauthorized = error.status === 401 || error.status === 403;

// Chỉ xóa token khi:
// 1. Token thực sự invalid (401/403) VÀ
// 2. Không phải localhost hoặc user cho phép xóa
if (isUnauthorized && !this.preserveTokenOnError) {
  this.removeTokenFromCookie();
} else if (isUnauthorized && this.preserveTokenOnError) {
  // Chỉ reset user data, KHÔNG xóa token
  this.currentUserSubject.next(null);
  this.authoritiesSubject.next([]);
} else if (isNetworkError || isServerError) {
  // Không làm gì, giữ nguyên token
}
```

#### **D. Sửa handleAuthError() - Không auto-remove token**
```typescript
// TRƯỚC: Tự động xóa token
private handleAuthError(): void {
  this.currentUserSubject.next(null);
  this.authoritiesSubject.next([]);
  this.removeTokenFromCookie(); // ← Tự động xóa!
}

// SAU: Không tự động xóa token
private handleAuthError(): void {
  this.currentUserSubject.next(null);
  this.authoritiesSubject.next([]);
  // Không tự động xóa token nữa - để caller quyết định
}
```

#### **E. Thêm methods an toàn**
```typescript
// Method load user an toàn
public loadUserAccountSafely(): Observable<UserAccount> {
  return this.loadUserAccount(); // Sử dụng logic xử lý lỗi mới
}

// Method ensure user loaded (lazy loading)
public ensureUserLoaded(): Observable<UserAccount> {
  const currentUser = this.getCurrentUser();
  const token = this.getTokenFromCookie();
  
  if (currentUser && token) {
    return of(currentUser); // Đã có, trả về ngay
  } else if (token) {
    return this.loadUserAccountSafely(); // Load từ API
  } else {
    return throwError(() => new Error('No access_token found'));
  }
}
```

#### **F. Thêm debug tool**
```typescript
public testCookiePersistence(): void {
  // Test đầy đủ: set cookie → get cookie → simulate reload → test API call
  // Kiểm tra token có bị xóa không trong từng bước
}
```

### 2. **ESaleKitAuthService - Cập nhật**

#### **A. Sử dụng method an toàn**
```typescript
// TRƯỚC: 
this.authApiService.loadUserAccount().subscribe()

// SAU:
this.authApiService.loadUserAccountSafely().subscribe()
this.authApiService.ensureUserLoaded().pipe()
```

#### **B. Xử lý lỗi conservative hơn**
```typescript
error: (error) => {
  // Không xóa token, chỉ log lỗi
  const isNetworkError = !error.status || error.status === 0;
  if (isNetworkError) {
    console.warn('Network error during auto-load, will retry later');
  }
}
```

### 3. **AuthTestComponent - Thêm debug tool**

#### **A. Thêm button test**
```html
<button mat-raised-button color="warn" (click)="testCookiePersistence()" *ngIf="isLocalhost">
  Test Cookie Persistence
</button>
```

#### **B. Thêm method test**
```typescript
testCookiePersistence(): void {
  if (this.isLocalhost) {
    this.authApiService.testCookiePersistence();
  }
}
```

## 🎯 Kết quả mong đợi

### ✅ **Trước fix:**
1. Load trang → Constructor → Auto-load user → Network error → **XÓA TOKEN** ❌
2. Reload trang → Mất token → Phải set lại token ❌

### ✅ **Sau fix:**
1. Load trang → Constructor → Không auto-load → **GIỮ TOKEN** ✅
2. Manual load user → Network error → **GIỮ TOKEN** (localhost) ✅
3. Manual load user → 401/403 error → **GIỮ TOKEN** (localhost) ✅
4. Reload trang → Token vẫn còn → Có thể retry ✅

## 🧪 Cách test

### **1. Test cơ bản:**
```javascript
// Trong browser console
authApiService.debugCookies()
authApiService.setTestTokenForLocalhost()
authApiService.testCookiePersistence()
```

### **2. Test trong AuthTestComponent:**
1. Mở `/e-sale-kit/auth-test`
2. Click "Set Test Token (Localhost)"
3. Click "Test Cookie Persistence"
4. Xem console logs
5. Reload trang → Check token vẫn còn

### **3. Test với real token:**
1. Set real access_token vào cookie
2. Reload trang
3. Check token vẫn còn
4. Click "Test API Call"
5. Nếu lỗi network → Token vẫn còn

## 🔍 Debug commands

```javascript
// Check environment
authApiService.isLocalhost
authApiService.preserveTokenOnError

// Check token
authApiService.getTokenFromCookie()
authApiService.debugCookies()

// Test persistence
authApiService.testCookiePersistence()

// Safe loading
authApiService.ensureUserLoaded().subscribe()
authApiService.loadUserAccountSafely().subscribe()
```

## 📝 Notes

- **Localhost**: Token được preserve khi có lỗi
- **Production**: Token vẫn bị xóa khi invalid (401/403)
- **Network errors**: Token được giữ ở mọi môi trường
- **Manual control**: User có thể control khi nào load user data
