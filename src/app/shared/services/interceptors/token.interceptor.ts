import { Injectable } from '@angular/core';
import {
    <PERSON>ttpRequest,
    HttpHandler,
    HttpEvent,
    HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { SecurityService } from '..';
import { environment } from 'environments/environment';
import { ErpService } from '../common/erp.service';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {

    constructor(private securityService: SecurityService,
                private erpService: ErpService) {
    }

    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        // Check if request should skip token interceptor
        if (request.headers.has('skip-token-interceptor')) {
            console.log('Skipping token interceptor for:', request.url);
            // Remove the skip flag and pass through without modifying Authorization
            const modifiedRequest = request.clone({
                headers: request.headers.delete('skip-token-interceptor')
            });
            return next.handle(modifiedRequest);
        }

        const headers = { timezoneclient: 'Asia/Ho_Chi_Minh', 'X-Client-Code': environment.xClientCode };
        if (request.url.includes(this.erpService.url)) {
            if (this.erpService.checkTokenErp()) {
                headers['Authorization'] = `Bearer ${this.erpService.getToken()}`;
            }else {
                return;
            }
        } else {
            headers['Authorization'] = `Bearer ${this.securityService.GetToken()}`;
        }

        if (request.url.includes(environment.voiceCall.uriAPI)) {
            headers['X-STRINGEE-AUTH'] = `${JSON.parse(sessionStorage.getItem(`voice_call_authen_token`))}`;
        }

        request = request.clone({
            setHeaders: headers
        });

        return next.handle(request);
    }
}
