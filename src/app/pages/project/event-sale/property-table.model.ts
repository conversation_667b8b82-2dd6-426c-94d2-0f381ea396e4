import { CategoryEnum } from 'app/shared/constant/constant';
import { AttributeEnum } from '../../ticket-common/ticket-common.excel';
import moment = require('moment');
import { DateConfigEnum } from 'app/enums/date-config.enum';
import { DateToFormat } from 'app/shared/parse/date-to-format';
import { environment } from 'environments/environment';
import { DeliveryStatus, statusSyncErpDataCode } from 'app/shared/models/delivery.model';
import { UNIT_STATUS } from 'app/shared/constant/transaction.constant';

export enum LiquidateType {
  TRANSFER = 'TRANSFER'
}

export interface Priority {
  id?: string;
  customerName?: string;
  employeeName?: string;
  posName?: string;
  priority?: number;
  code?: string;
  posId?: string;
  isChanged: boolean;
  extPosName?: string;
  company?: any;
}
export class PropertyTable {
  stt: number;
  id: string;
  publishPrice: string;
  projectName: string; // tên dự án
  posName: string; // đơn vị bán hàng
  posId: string;
  posNameF1: string; // đơn vị bán hàng f1
  posIdF1: string;
  posIsRegister: boolean;
  code: string; // mã BĐS
  type: string; // loại
  block: string; // block
  floor: string; // tầng
  bedroom: string; // số phòng ngủ
  direction: string; // hướng
  view1: string; // view 1
  view2: string; // view 2
  edge: string; // góc
  carpetArea: string; // diện tích thông thủy
  buildUpArea: string; // diện tích tim tường
  price: number; // giá bán chưa VAT
  priceAbove: number; // đơn giá chưa VAT
  priceAboveVat: number; // đơn giá có VAT
  housePriceVat: number; // giá nhà có VAT
  landPriceVat: number; // giá đất có VAT
  housePrice: number; // giá nhà chưa VAT
  landPrice: number; // giá đất chưa VAT
  contractPrice: number;
  contractPriceForMaintenanceFee: number;
  width: string; // rộng
  length: string; // dài
  lot: string; // lô
  quarter: string; // khu
  area: number; // diện tích
  outsideArea: number; // Diện tích sân vườn
  category: string;
  primaryStatus: string;
  priceVat: number;
  priorities: any;
  projectId: string;
  room: string;
  hasPriority: boolean;
  reasonRevoke: string;
  extendPos: any;
  extendPosId: string;
  extendable: boolean;
  isException: boolean;
  exceptionalReason: string;

  bgColor: string;
  colorSyncErp: string;
  colorHandoverSyncErp: string;
  textColor: string;

  cbExtendtion: boolean;

  currentTicket: any;

  priority0: any;
  priority1: any;
  priority2: any;
  priority3: any;
  description: string;

  registeredDate: Date;
  registeredPos: any;
  registeredQueue: any;
  stage: number;
  isLoading: boolean;
  isRegistering: boolean;
  countPriority: number;
  modifiedDate: any;
  modifiedDateFormatted: any;
  ownerName: string;
  histories: any;
  contract: any;
  liquidateType: string;
  proposalApproved: boolean;
  floorImages: any[];
  unitConfirmDate: Date;
  extData: any;
  typeSyncErp: any;
  isEnoughPaymentContract: boolean;
  handoverSchedule: any;
  lstImage: any[];
  handoverStatus: string;


  public DIRECTION = {
    DONG: 'Đông',
    TAY: 'Tây',
    BAC: 'Bắc',
    NAM: 'Nam',
    DONG_BAC: 'Đông Bắc',
    TAY_BAC: 'Tây Bắc',
    DONG_NAM: 'Đông Nam',
    TAY_NAM: 'Tây Nam',
  };

  constructor(
    param?: any,
    marketPos?: any
  ) {
    this.setValue(param, marketPos);
  }

  get primaryStatusName(): string {
    return this.primaryStatus && UNIT_STATUS[this.primaryStatus] ? UNIT_STATUS[this.primaryStatus] : '';
  }

  setValue(params: any, marketPos: string = null) {
    if (params) {
      this.stt = params.stt || null;
      this.id = params.id;
      this.isEnoughPaymentContract = params.isEnoughPaymentContract || false;
      this.handoverSchedule = params.handoverSchedule || null;
      this.handoverStatus = params.handoverStatus || '';
      this.publishPrice = params.publishPrice ? 'x' : '';
      this.projectName = params.project ? params.project.name || '' : '';
      this.posName = params.pos ? params.pos.name || '' : '';
      this.posId = params.pos ? params.pos.id || '' : '';
      this.posNameF1 = params.f1 && params.f1.pos ? params.f1.pos.name || '' : '';
      this.posIdF1 = params.f1 && params.f1.pos? params.f1.pos.id || '' : '';
      this.posIsRegister = params.pos ? params.pos.isRegister || false : false;
      this.code = params.code || '';
      this.type = params.apartmentType || '';
      this.block = params.block || '';
      this.floor = params.floor || '';
      this.view1 = params.view1 || '';
      this.view2 = '';
      this.edge = '';
      this.width = params.areaWide ? params.areaWide : '';
      this.length = params.areaLong ? params.areaLong : '';
      this.lot = '';
      this.quarter = '';
      this.primaryStatus = params.primaryStatus || '';
      this.liquidateType = params.liquidateType || '';
      this.room = params.shortCode || '';
      this.stage = params.stage || null;
      this.proposalApproved = params.proposalApproved || false;
      this.floorImages = params.floorImages || [];
      this.lstImage = params.lstImage || [];
      if (params.attributes) {
        params.attributes.forEach((attribute) => {
          if (!params.floor && attribute.attributeName === 'Tầng') {
            this.floor = attribute.value || '';
          } else if (!params.block &&  attribute.attributeName === 'Block') {
            this.block = attribute.value || '';
          } else if (!params.view1 && attribute.attributeName === 'View 1') {
            this.view1 = attribute.value || '';
          } else if (attribute.attributeName === 'View 2') {
            this.view2 = attribute.value || '';
          } else if (attribute.attributeName === 'Căn góc') {
            this.edge = attribute.value || '';
          } else if (attribute.attributeName === 'Diện tích tim tường') {
            this.buildUpArea = attribute.value || '';
          } else if (attribute.attributeName === 'Diện tích thông thủy') {
            this.carpetArea = attribute.value || '';
          } else if (attribute.attributeName === 'Chiều rộng') {
            this.width = attribute.value || '';
          } else if (attribute.attributeName === 'Chiều dài') {
            this.length = attribute.value || '';
          } else if (attribute.attributeName === 'Khu đất') {
            this.quarter = attribute.value || '';
          } else if (attribute.attributeName === 'Lô đất') {
            this.lot = attribute.value || '';
          } else if (attribute.attributeName === 'Loại căn hộ/đất nền') {
            this.type = attribute.value || '';
          }
        });
      }
      this.contract = params.contract;
      // this.bedroom = params.bedroom || 0;
      this.bedroom = params.bedroom || '';
      this.direction = params.direction ? (this.DIRECTION[params.direction] ? this.DIRECTION[params.direction] : params.direction) : '';
      this.priceAbove = params.priceAbove || 0;
      this.priceAboveVat = params.priceAboveVat || 0;
      this.price = params.price || 0;
      this.housePriceVat = params.housePriceVat || 0;
      this.landPriceVat = params.landPriceVat || 0;
      this.housePrice = params.housePrice || 0;
      this.landPrice = params.landPrice || 0;
      this.contractPrice = params.contractPrice || 0;
      this.contractPriceForMaintenanceFee = params.contractPriceForMaintenanceFee || 0;
      this.area = params.area || 0;
      this.outsideArea = params.outsideArea || 0;
      this.category = '';
      this.priorities = [
        { priority: 1, company: null , customerName: '', id: '', employeeName: '', posName: '', code: '', posId: '', extPosName: params.extendPos ? params.extendPos.name : 'Tất cả ĐVBH' } as Priority,
        { priority: 2, company: null , customerName: '', id: '', employeeName: '', posName: '', code: '', posId: '', extPosName: params.extendPos ? params.extendPos.name : 'Tất cả ĐVBH' } as Priority,
        { priority: 3, company: null , customerName: '', id: '', employeeName: '', posName: '', code: '', posId: '', extPosName: params.extendPos ? params.extendPos.name : 'Tất cả ĐVBH' } as Priority
      ];
      this.extendable = params.extendable || false;
      this.extendPos = params.extendPos ? params.extendPos : null;
      this.extendPosId = params.extendPos ? params.extendPos.id : '';
      if (params.priorities && params.priorities.length) {
        for (let i = 0; i < params.priorities.length; i++) {
          if (params.priorities[i].priority > 0) {
            this.priorities[params.priorities[i].priority - 1] = params.priorities[i];
            if (!this.priorities[params.priorities[i].priority - 1].bookingTicketCode) {
              this.priorities[params.priorities[i].priority - 1].bookingTicketCode = params.priorities[i].escrowTicketCode;
            }
            this.hasPriority = true;
            continue;
          } else {
            this.hasPriority = false;
          }
        }
        this.unitConfirmDate = params.priorities[0].unitConfirmDate ? new Date(params.priorities[0].unitConfirmDate) : new Date();
      }
      this.reasonRevoke = params.reasonRevoke || '';
      this.modifiedDate = params.modifiedDate;
      this.modifiedDateFormatted = params.modifiedDate ? DateToFormat.parseDate(params.modifiedDate, 'dd/MM/yyyy hh:mm') : '';
      this.priceVat = params.priceVat || 0;
      this.projectId = params.project ? params.project.id : '';
      this.category = 'CAN_HO';

      if (params.category && params.category.id === CategoryEnum.DAT_NEN) {
        this.category = 'DAT_NEN';
      }
      this.bgColor = this.getColorOfProperty(this.primaryStatus, (this.posId && marketPos && this.posId === marketPos), this.liquidateType, this.contract);
      this.textColor = this.getColorTextProperty(this.hasPriority, this.primaryStatus, this.priorities);

      this.priority0 = params.priorities && params.priorities.length > 0 ? params.priorities.find(x => (x.priority === 0 || !x.priority)) : null;
      if (this.priority0 && !this.priority0.bookingTicketCode) {
        this.priority0.bookingTicketCode = this.priority0.escrowTicketCode;
      }
      this.priority1 = this.priorities ? this.priorities.find(x => x.priority === 1) : null;
      this.priority2 = this.priorities ? this.priorities.find(x => x.priority === 2) : null;
      this.priority3 = this.priorities ? this.priorities.find(x => x.priority === 3) : null;
      this.description = params.description;
      this.isException = params.isException;
      this.exceptionalReason = params.exceptionalReason;

      this.registeredDate = params.registeredDate ? new Date(params.registeredDate) : null;
      this.registeredPos = params.registeredPos ? params.registeredPos : null;
      this.registeredQueue = params.registeredQueue ? params.registeredQueue : null;
      this.isLoading = false;
      this.countPriority = params.priorities ? params.priorities.length : 0;
      this.ownerName = params.owner ? params.owner.name : '';
      this.histories = params.histories || [];
      this.extData = params.extData || {};
      this.colorSyncErp = params.colorSyncErp ? this.getColorSyncErp(params.colorSyncErp) : '#ffffff';
      this.colorHandoverSyncErp = params.colorSyncErp ? this.getColorHandoverSyncErp(params) : '#ffffff';
      this.typeSyncErp = params.colorSyncErp ? params.colorSyncErp : '';
    } else {
      this.setAllNull();
    }
  }

  setAllNull() {
    // set header
    this.stt = 1;
    this.id = '';
    this.projectName = '';
    this.posName = '';
    this.posId = '';
    this.posIsRegister = false;
    this.code = '';
    this.type = '';
    this.block = '';
    this.floor = '';
    this.view1 = '';
    this.view2 = '';
    this.edge = '';
    // this.bedroom = 0;
    this.bedroom = '';
    this.direction = '';
    this.priceAbove = 0;
    this.priceAboveVat = 0;
    this.price = 0;
    this.buildUpArea = '';
    this.outsideArea = 0;
    this.carpetArea = '';
    this.lot = '';
    this.category = '';
    this.primaryStatus = '';
    this.room = '';
    this.priorities = [
      { priority: 1, customerName: '', id: '', employeeName: '', posName: '', code: '' } as Priority,
      { priority: 2, customerName: '', id: '', employeeName: '', posName: '', code: '' } as Priority,
      { priority: 3, customerName: '', id: '', employeeName: '', posName: '', code: '' } as Priority
    ];
    this.priceVat = 0;
    this.projectId = '';
    this.hasPriority = false;
    this.reasonRevoke = '';
    this.extendable = false;
    this.extendPos = '';
    this.isException = false;
    this.exceptionalReason = '';

    this.bgColor = '';
    this.textColor = '';
    this.description = '';
    this.stage = null;
    this.registeredDate = null;
    this.registeredPos = null;
    this.registeredQueue = null;
    this.isLoading = false;
    this.countPriority = 0;
    this.modifiedDate = '';
    this.modifiedDateFormatted = '';
    this.ownerName = '';
    this.histories = [];
    this.contract = null;
    this.liquidateType = '';
    this.extData = {};
  }

  getHeaders() {
    switch (this.category) {
      case 'CAN_HO':
        return [
          { header: 'STT', key: 'stt', width: 10 },
          { header: 'Tên dự án', key: 'projectName', width: 40 },
          { header: 'Mã sản phẩm', key: 'view1', width: 50 },
          { header: 'Số sản phẩm', key: 'code', width: 30 },
          { header: 'Giá bán (chưa VAT)', key: 'price', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Giá bán (có VAT)', key: 'priceVat', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Đơn giá (chưa VAT)', key: 'priceAbove', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Đơn giá (có VAT)', key: 'priceAboveVat', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Tình trạng', key: 'primaryStatusName', width: 12 },
          { header: 'Loại căn hộ', key: 'type', width: 40 },
          // { header: 'Giá sản phẩm', key: 'price', width: 40, style: { numFmt: '#,##0' } },
          { header: 'DT tim tường/DT đất', key: 'buildUpArea', width: 40, style: { numFmt: '#,##0' } },
          { header: 'DT thông thủy/DT sử dụng', key: 'carpetArea', width: 40, style: { numFmt: '#,##0' } },
          { header: 'DT sân vườn/DT xây dựng', key: 'outsideArea', width: 40, style: { numFmt: '#,##0' } },
          { header: 'Block', key: 'block', width: 10 },
          { header: 'Tầng', key: 'floor', width: 10 },
          { header: 'Căn góc', key: 'edge', width: 10 },
          { header: 'Hướng', key: 'direction', width: 10 },
          { header: 'View 2', key: 'view2', width: 10 },
          { header: 'Đơn vị được giao', key: 'posName', width: 50 },
          { header: 'Số lượng ưu tiên', key: 'countPriority', width: 10 },
          { header: 'Lý do thu hồi', key: 'reasonRevoke', width: 40 },
          { header: 'Thời gian cập nhật', key: 'modifiedDateFormatted', width: 40, style: { numFmt: 'DD/MM/YYYY HH:mm' } },
          { header: 'Số phòng ngủ', key: 'bedroom', width: 40 },
          { header: 'Ghi chú', key: 'description', width: 40 },
          { header: 'Nguồn', key: 'ownerName', width: 40 },
        ];
      case 'DAT_NEN':
        return [
          { header: 'STT', key: 'stt', width: 10 },
          { header: 'Tên dự án', key: 'projectName', width: 40 },
          { header: 'Mã sản phẩm', key: 'code', width: 40 },
          { header: 'Giá bán (chưa VAT)', key: 'price', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Giá bán (có VAT)', key: 'priceVat', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Đơn giá (chưa VAT)', key: 'priceAbove', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Đơn giá (có VAT)', key: 'priceAboveVat', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Tình trạng', key: 'primaryStatusName', width: 12 },
          { header: 'Loại đất nền', key: 'type', width: 40 },
          // { header: 'Giá sản phẩm', key: 'price', width: 40, style: { numFmt: '#,##0' } },
          { header: 'Diện tích', key: 'area', width: 40, style: { numFmt: '#,##0' } },
          { header: 'Chiều dài', key: 'length', width: 40, style: { numFmt: '#,##0' } },
          { header: 'Chiều rộng', key: 'with', width: 40, style: { numFmt: '#,##0' } },
          { header: 'Khu đất', key: 'quarter', width: 10 },
          { header: 'Lô đất', key: 'lot', width: 10 },
          { header: 'Căn góc', key: 'edge', width: 10 },
          { header: 'Lý do thu hồi', key: 'reasonRevoke', width: 40 },
          { header: 'Thời gian cập nhật', key: 'modifiedDateFormatted', width: 40, style: { numFmt: 'DD/MM/YYYY HH:mm' } },
        ];
      default:
        return [
          { header: 'STT', key: 'stt', width: 10 },
          { header: 'Tên dự án', key: 'projectName', width: 40 },
          { header: 'Mã sản phẩm', key: 'view1', width: 50 },
          { header: 'Số sản phẩm', key: 'code', width: 30 },
          { header: 'Giá bán (chưa VAT)', key: 'price', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Giá bán (có VAT)', key: 'priceVat', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Đơn giá (chưa VAT)', key: 'priceAbove', width: 40, style: { numFmt: '#,##0' }  },
          { header: 'Đơn giá (có VAT)', key: 'priceAboveVat', width: 40, style: { numFmt: '#,##0' }  },

          { header: 'Khách hàng GD', key: 'customerName', width: 30 },
          { header: 'Đơn vị bán hàng', key: 'posName', width: 30 },
          { header: 'Tình trạng', key: 'primaryStatusName', width: 12 },
          { header: 'Ghi chú', key: 'description', width: 12 },
          { header: 'Thời gian cập nhật', key: 'modifiedDateFormatted', width: 40, style: { numFmt: 'DD/MM/YYYY HH:mm' } },

          { header: 'Khách hàng UT1', key: 'customerName1', width: 30 },
          { header: 'DVBH UT1', key: 'posName1', width: 30 },
          { header: 'Khách hàng UT2', key: 'customerName2', width: 30 },
          { header: 'DVBH UT2', key: 'posName2', width: 30 },
          { header: 'Khách hàng UT3', key: 'customerName3', width: 30 },
          { header: 'DVBH UT3', key: 'posName3', width: 30 },
        ];
    }
  }
  getDmspHeaders() {
    return [
      { header: 'STT', key: 'stt', width: 10 },
      { header: 'Số sản phẩm', key: 'code', width: 30 },
      { header: 'Mã sản phẩm', key: 'view1', width: 50 },
      { header: 'Đơn giá (chưa VAT)', key: 'priceAbove', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Đơn giá (có VAT)', key: 'priceAboveVat', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Giá bán (chưa VAT)', key: 'price', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Giá bán (có VAT)', key: 'priceVat', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Giá trị nhà (có VAT)', key: 'housePriceVat', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Giá trị đất (có VAT)', key: 'landPriceVat', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Giá trị nhà (chưa VAT)', key: 'housePrice', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Giá trị đất (chưa VAT)', key: 'landPrice', width: 40, style: { numFmt: '#,##0' } },
      { header: 'Tình trạng', key: 'primaryStatusName', width: 12 },
      { header: 'Cho công khai giá', key: 'publishPrice', width: 10 },
      { header: 'Tổng giá trị CH sau CK gồm VAT, chưa gồm PBT', key: 'contractPrice', width: 30 },
      { header: 'Giá trị CH tính phí bảo trì', key: 'contractPriceForMaintenanceFee', width: 30 },
    ];
  }

  getColorOfProperty(primaryStatus, isMarketPos, liquidateType, contract) {
    if(isMarketPos){
      return '#FC3F3F';
    }
    switch (primaryStatus) {
      case 'LOCK': // Khóa đầu tư
        return '#FEFEFE';
      case 'CLOSE': // Chưa mở bán
        if(liquidateType === LiquidateType.TRANSFER) {
          return '#D60093';
        }
        return '#CCCCCC';
      case 'COMING': // Đang mở bán
        return '#FFE69B';
      case 'PROCESSING': // Khách hàng xác nhận
        return '#975BF5';
      case 'MCONFIRM': // Đã xác nhận
      case 'CONFIRM': // Đã xác nhận
        return '#8DE0FF';
      case 'UNSUCCESS': // Không thành công
        return '#BF8400';
      case 'LOCK_CONFIRM': // Thành công, chờ bổ sung HS
        return '#FF7600';
      case 'MSUCCESS': // Thành công
      case 'SUCCESS': // Thành công
        if(contract && contract.type !== null) return '#FFCCFF';
        return '#FC3F3F';
      case 'CANCEL': // Thanh lý
        return '#66ff66';
      case 'MOVED': // Chuyển CTBH
          return '#BDD7EE';

      default:
        break;
    }
  }

  getColorSyncErp(data) {
    switch (data) {
      case 'XD00':
        return '#7f7f7f';
      case 'XD01':
        return '#2c71c6';
      case 'XD02':
        return '#fcfb00';
      case 'XD03':
        return '#99cf4e';
      case 'XD04':
        return '#ef1109';
      case 'XD05':
        return '#6c34a3';
      default:
        return '#ffffff';
    }
  }

  getColorHandoverSyncErp(data) {
    switch (data.colorSyncErp) {
      case statusSyncErpDataCode.XD00:
        return '#7f7f7f';
      case statusSyncErpDataCode.XD01:
        return '#2c71c6';
      case statusSyncErpDataCode.XD02:
        return '#fcfb00';
      case statusSyncErpDataCode.XD03:
        // Hợp đồng thu đủ giá trị để bàn giao
        if (data.isEnoughPaymentContract) {
          // Đã xếp lịch bàn giao
          if (data.handoverSchedule && data.handoverSchedule.id) {
            if (data.contract && data.contract.handoverStatus === DeliveryStatus.HANDED) {
              return '#ef1109'; // XD04
            }
            return '#ffe69b'; // XD07
          }
          return '#ff7600' // XD06
        }
        return '#99cf4e';
      case statusSyncErpDataCode.XD04:
        return '#ef1109';
      case statusSyncErpDataCode.XD05:
        return '#6c34a3';
      case statusSyncErpDataCode.XD06:
        if (data.handoverSchedule && data.handoverSchedule.id) {
          if (data.contract && data.contract.handoverStatus === DeliveryStatus.HANDED) {
            return '#ef1109'; // XD04
          }
          return '#ffe69b'; // XD07
        }
        return '#ff7600';
      case statusSyncErpDataCode.XD07:
        if (data.contract && data.contract.handoverStatus === DeliveryStatus.HANDED) {
          return '#ef1109'; // XD04
        }
        return '#ffe69b';
      default:
        return '#ffffff';
    }
  }

  getColorTextProperty(hasPriority, primaryStatus, priorities) {
    if (hasPriority && (primaryStatus === 'COMING' || primaryStatus === 'CLOSE')) {
      const countPriority = priorities.reduce((counter, { bookingTicketCode }) => bookingTicketCode  ? counter += 1 : counter, 0);
      if(countPriority === 1) return '#00BFFF';
      if(countPriority === 2) return '#975bf5';
      return '#FF0000';
    }
  }

  createPropertyTableDownloadObj(item, showfloor?) {
    let obj: any = {

      customerName: '',
      ticketCode: '',
      birthday: '',
      phone: '',
      identity: '',
      identityDate: '',
      identityAddress: '',
      address: '',
      email: '',
      pos: '',
      employee: '',

      customerName2: '',
      ticketCode2: '',
      birthday2: '',
      phone2: '',
      identity2: '',
      identityDate2: '',
      identityAddress2: '',
      address2: '',
      email2: '',
      pos2: '',
      employee2: '',

      customerName3: '',
      ticketCode3: '',
      birthday3: '',
      phone3: '',
      identity3: '',
      identityDate3: '',
      identityAddress3: '',
      address3: '',
      email3: '',
      pos3: '',
      employee3: '',
    };
    obj.stt = item.stt;
    obj.type = item.propertyUnit ? item.propertyUnit.code : '';
    obj.code = item.propertyUnit ? item.propertyUnit.shortCode : '';
    obj.status = item.status && UNIT_STATUS[item.status]  ? UNIT_STATUS[item.status] : '';
    obj.block = '';
    obj.view1 = '';
    obj.view2 = '';
    obj.bedroom = item.propertyUnit ? item.propertyUnit.bedroom : '';
    obj.buildUpArea = '';
    obj.carpetArea = '';
    if (item.propertyUnit && item.propertyUnit.attributes) {
      item.propertyUnit.attributes.forEach((attribute) => {
        if (attribute.id === AttributeEnum.view1) {
          obj.view1 = attribute.value || '';
        } else if (attribute.id === AttributeEnum.view2) {
          obj.view2 = attribute.value || '';
        }
        if (showfloor) {
          if (attribute.id === AttributeEnum.tang) {
            obj.block = attribute.value || '';
          }
        } else {
          if (attribute.id === AttributeEnum.block) {
            obj.block = attribute.value || '';
          }
        }
      });
    }

    const priorityInfo = this.createPriorityInfo(item);

    if (item.priority === 1) {
      obj.customerName = priorityInfo.customerName;
      obj.birthday = priorityInfo.birthday;
      obj.phone = priorityInfo.phone;
      obj.identity = priorityInfo.identity;
      obj.identityDate = priorityInfo.identityDate;
      obj.identityAddress = priorityInfo.identityAddress;
      obj.address = priorityInfo.address;
      obj.rootAddress = priorityInfo.rootAddress;
      obj.email = priorityInfo.email;
      obj.pos = priorityInfo.posName;
      obj.employee = priorityInfo.employeeName;
    } else if (item.priority === 2) {
      obj.customerName2 = priorityInfo.customerName;
      obj.birthday2 = priorityInfo.birthday;
      obj.phone2 = priorityInfo.phone;
      obj.identity2 = priorityInfo.identity;
      obj.identityDate2 = priorityInfo.identityDate;
      obj.identityAddress2 = priorityInfo.identityAddress;
      obj.address2 = priorityInfo.address;
      obj.rootAddress2 = priorityInfo.rootAddress;
      obj.email2 = priorityInfo.email;
      obj.pos2 = priorityInfo.posName;
      obj.employee2 = priorityInfo.employeeName;
    } else if (item.priority === 3) {
      obj.customerName3 = priorityInfo.customerName;
      obj.birthday3 = priorityInfo.birthday;
      obj.phone3 = priorityInfo.phone;
      obj.identity3 = priorityInfo.identity;
      obj.identityDate3 = priorityInfo.identityDate;
      obj.identityAddress3 = priorityInfo.identityAddress;
      obj.address3 = priorityInfo.address;
      obj.rootAddress3 = priorityInfo.rootAddress;
      obj.email3 = priorityInfo.email;
      obj.pos3 = priorityInfo.posName;
      obj.employee3 = priorityInfo.employeeName;
    }
    obj.locked = item.locked;
    obj.extendPos = item.extendPos ? item.extendPos : null;
    return obj;
  }

  getCustomerAddress(address) {
    return typeof address === 'string' ? address : [address.address, address.ward, address.district, address.province].join(', ');
  }

  getHideRows() {
    return null;
  }
  getDmspHideRows() {
    return [{
      stt: 'stt',
      code: 'code',
      view1: 'view1',
      priceAbove: 'priceAbove',
      priceAboveVat: 'priceAboveVat',
      price: 'price',
      priceVat: 'priceVat',
      housePriceVat: 'housePriceVat',
      landPriceVat: 'landPriceVat',
      housePrice: 'housePrice',
      landPrice: 'landPrice',
      publishPrice: 'publishPrice',
      contractPrice: 'contractPrice',
      contractPriceForMaintenanceFee: 'contractPriceForMaintenanceFee',
    }];

  }

  getHeaderBangHang() {
    return [
      { header: 'STT', key: 'stt', width: 5 },
      { header: 'BĐS', key: 'view1', width: 22 },
      { header: 'Số BĐS', key: 'type', width: 12 },
      { header: 'Phân khu', key: 'view2', width: 10 },
      { header: 'Block / Lô', key: 'block', width: 10 },

      { header: 'HỌ VÀ TÊN KHÁCH HÀNG (GHI HOA)', key: 'customerName', width: 22 },
      { header: 'NGÀY THÁNG NĂM SINH', key: 'birthday', width: 16 },
      { header: 'SỐ ĐT', key: 'phone', width: 12 },
      { header: 'CMND/ PASSPORT/ CĂN CƯỚC', key: 'identity', width: 11 },
      { header: 'NGÀY CẤP CMND/…', key: 'identityDate', width: 12 },
      { header: 'NƠI CẤP', key: 'identityAddress', width: 14 },
      { header: 'ĐỊA CHỈ THƯỜNG TRÚ', key: 'rootAddress', width: 26 },
      { header: 'ĐỊA CHỈ LIÊN LẠC', key: 'address', width: 26 },
      { header: 'EMAIL', key: 'email', width: 16 },
      { header: 'ĐVTV', key: 'pos', width: 10 },
      { header: 'NVTV', key: 'employee', width: 18 },
      { header: 'HỌ VÀ TÊN KHÁCH HÀNG (GHI HOA)', key: 'customerName2', width: 22 },
      { header: 'NGÀY THÁNG NĂM SINH', key: 'birthday2', width: 16 },
      { header: 'SỐ ĐT', key: 'phone2', width: 12 },
      { header: 'CMND/ PASSPORT/ CĂN CƯỚC', key: 'identity2', width: 11 },
      { header: 'NGÀY CẤP CMND/…', key: 'identityDate2', width: 12 },
      { header: 'NƠI CẤP', key: 'identityAddress2', width: 14 },
      { header: 'ĐỊA CHỈ THƯỜNG TRÚ', key: 'rootAddress2', width: 26 },
      { header: 'ĐỊA CHỈ LIÊN LẠC', key: 'address2', width: 26 },
      { header: 'EMAIL', key: 'email2', width: 16 },
      { header: 'ĐVTV', key: 'pos2', width: 10 },
      { header: 'NVTV', key: 'employee2', width: 18 },
      { header: 'HỌ VÀ TÊN KHÁCH HÀNG (GHI HOA)', key: 'customerName3', width: 22 },
      { header: 'NGÀY THÁNG NĂM SINH', key: 'birthday3', width: 16 },
      { header: 'SỐ ĐT', key: 'phone3', width: 12 },
      { header: 'CMND/ PASSPORT/ CĂN CƯỚC', key: 'identity3', width: 11 },
      { header: 'NGÀY CẤP CMND/…', key: 'identityDate3', width: 12 },
      { header: 'NƠI CẤP', key: 'identityAddress3', width: 16 },
      { header: 'ĐỊA CHỈ THƯỜNG TRÚ', key: 'rootAddress3', width: 26 },
      { header: 'ĐỊA CHỈ LIÊN LẠC', key: 'address3', width: 26 },
      { header: 'EMAIL', key: 'email3', width: 16 },
      { header: 'ĐVTV', key: 'pos3', width: 10 },
      { header: 'NVTV', key: 'employee3', width: 18 },
    ];
  }

  createPriorityInfo(item) {
    const obj: any = {};
    let birthday = '';

    if (item.customer && item.customer.info) {
      if (item.customer.info.onlyYear) {
        birthday = item.customer.info.birthdayYear;
      } else {
        if (item.customer.info.birthday) {
          birthday = moment(item.customer.info.birthday).format(DateConfigEnum.Slash);
        }
      }
    }

    obj.customerName = item.customer && item.customer.personalInfo && item.customer.personalInfo.name ? item.customer.personalInfo.name.toUpperCase() : '';
    obj.birthday = birthday;
    obj.phone = item.customer && item.customer.personalInfo && item.customer.personalInfo.phone ? item.customer.personalInfo.phone : '';
    obj.identity = item.customer && item.customer.identities && item.customer.identities.length > 0 ? item.customer.identities[0].value : '';
    obj.identityDate = item.customer && item.customer.identities && item.customer.identities.length > 0 && item.customer.identities[0].date ? moment(item.customer.identities[0].date).format(DateConfigEnum.Slash) : '';
    obj.identityAddress = item.customer && item.customer.identities && item.customer.identities.length > 0 ? item.customer.identities[0].place : '';
    obj.rootAddress = item.customer && item.customer.info && item.customer.info.rootAddress ? item.customer.info.rootAddress.fullAddress : '';
    obj.address = item.customer && item.customer.info && item.customer.info.address ? item.customer.info.address.fullAddress : '';
    obj.email = item.customer && item.customer.personalInfo ? item.customer.personalInfo.email : '';
    obj.posName = item.pos ? item.pos.name : '';
    obj.employeeName = item.employee ? item.employee.name : '';

    return obj;
  }


  isConfirmedByPriotiry1() {
    return this.priority1 && (this.priority1.status === 'LOCK_CONFIRM');
  }

  isConfirmedByPriotiry2() {
    return this.priority2 && (this.priority2.status === 'LOCK_CONFIRM');
  }

  isConfirmedByPriotiry3() {
    return this.priority3 && (this.priority3.status === 'LOCK_CONFIRM');
  }

  isConfirmedByPriotiry0() {
    return this.priority0 && (this.priority0.status === 'LOCK_CONFIRM');
  }
  isLockConfirmLockByPriotiry0() {
    return this.priority0 && (this.priority0.status === 'LOCK_CONFIRM_LOCK');
  }
  isSuccessByPriotiry1() {
    return this.priority1 && (this.priority1.status === 'SUCCESS');
  }

  isSuccessByPriotiry2() {
    return this.priority2 && (this.priority2.status === 'SUCCESS');
  }

  isSuccessByPriotiry3() {
    return this.priority3 && (this.priority3.status === 'SUCCESS');
  }

  isSuccessByPriotiry0() {
    return this.priority0 && (this.priority0.status === 'SUCCESS');
  }
  isPosConfirmLockByPriotiry0() {
    return this.priority0 && (this.priority0.status === 'POS_CONFIRM_LOCK');
  }

}
