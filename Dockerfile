
# Stage 1: Compile and Build angular codebase

# Use official node image as the base image
#FROM image-registry.openshift-image-registry.svc:5000/openshift/s2i-nodejs@sha256:0f1f68fc74006d0427e1bac19b3ad5814ac09d5dfb10f1d4f089eb5075a2944e as build
# FROM  registry.apps.xplat.fis.com.vn/library/node:14.21.3

FROM node:12.22.12 as build
# Set the working directory
WORKDIR /usr/local/app

# Add the source code to app
COPY ./ /usr/local/app/

# Install all the dependencies
RUN yarn install

# Generate the build of the application
RUN yarn build

#FROM image-registry.openshift-image-registry.svc:5000/openshift/nginx
FROM nginx:1.24

COPY ./nginx.conf /etc/nginx/
COPY ./default.conf /etc/nginx/conf.d/default.conf


# COPY --from=build ./dist /usr/share/nginx/html
COPY --from=build ./usr/local/app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
