import { Const<PERSON>ermissionProperty, PropertyTicketPermissionConst, PropertyUnitPermissionConst } from './../../../../enums/e-permission.enum';
import { Component, OnInit, OnDestroy, ViewChild, Injector, ElementRef, AfterViewInit, TemplateRef, ViewChildren, QueryList } from '@angular/core';
import { Router, ActivatedRoute, ParamMap } from '@angular/router';
import { BehaviorSubject, forkJoin, Subject } from 'rxjs';

// Shared component
import { BaseComponent } from 'app/shared/components/base.component';
import { PagingComponent } from 'app/shared/components/paging';
import {
    ToastrService
} from 'app/shared/services/common';
import { MatDialog } from '@angular/material';
import { ProjectService } from '../../project.service';
import { SidenavService } from '../../../../shared/services/common/sidenav.service';
import { ProjectSetting } from '../../project-setting.model';
import { FormControl } from '@angular/forms';
import { finalize, takeUntil, map } from 'rxjs/operators';
import { Property } from '../../../property/property.model';
import { PropertyPrimaryService } from '../property-primary.service';
import { PropertyPrimaryExcel } from '../property-primary.excel';
import { CUser } from '../../../../api-models';
import { UserV2Service } from '../../../../shared/services/common/user-v2.service';
import { ExcelService } from '../../../../shared/services/common/excel.service';
import { TicketSale } from '../ticket-sale.model';
import { PopupPropertyDetail } from '../popup-property-detail/popup-property-detail';
import { LiquidateType, PropertyTable } from './../property-table.model';


import { CategoryEnum, Constant } from 'app/shared/constant/constant';
import { CPagination } from 'app/api-models/c-pagination';
import { SaleList } from '../table-deal-panel/sale-list.model';
import { AuthorizeService } from 'app/shared/authenticate';

import { EEventStatusProjet, EEventStatusStageProject, EEventStatusPriorityProject } from '../../event-dashboard-page/e-event-project.enum';
import { StorageService } from 'app/shared/services';
import { ERoleId } from 'app/enums/e-role-id.enum';
import { ProjectStatusEnum } from 'app/shared/enum/project.enum';
import { PopupEmailComponent } from '../popup-email/popup-email.component';
import { DialogMessageNoticeComponent } from '../dialog-message-notice/dialog-message-notice.component';
import { PropertyTicketSubmitDialogComponent } from '../../dialog/property-ticket-submit-dialog.component';
import { ExportTypeEnum } from '../../../../shared/enum/export.enum';
import _ = require('lodash');
import { Ng2SearchPipe } from 'ng2-search-filter';
import { PopupTransferPropertyComponent } from '../popup-transfer-property/popup-transfer-property.component';
import { PopupTransferSalesProgramComponent } from '../popup-transfer-sales-program/popup-transfer-sales-program.component';
import { PopupPublishPriceHistoryComponent } from '../popup-publish-price-history/popup-publish-price-history.component';
import { getSaleItemWithSortValue, getCampaignErp, getCurrentTimeSigned, getPropUnitTartgetTicket, getShortName, sortCode } from 'app/shared/utility/utility';
import { EventSalePopupPropertyTicketComponent } from '../popup-property-ticket/popup-property-ticket.component';
import { ProjectPrimaryTransactionService } from '../../project-primary-transaction.service';
import { isNullOrUndefined } from 'util';
import { PrimaryTransactionService } from '../../primary-transaction.service';
import { PrimaryTransactionAction, PrimaryTransactionStatus, } from 'app/shared/enum/primary-transaction.enum';
import { ErpService } from 'app/shared/services/common/erp.service';
import { ConfirmPopup } from 'app/shared/components/confirm-popup/confirm-popup';
import { PopupComfirmERPComponent } from '../popup-comfirm-erp/popup-comfirm-erp.component';
import { DialogMessageImageComponent } from 'app/shared/components/dialog-message-image/dialog-message-image.component';
import { EventSalePopupPropertyTicketDetailComponent } from '../popup-property-ticket-detail/popup-property-ticket-detail.component';
import { PopupProcessComponent } from '../popup-process/popup-process.component';
import { PopupExtendPriorityComponent } from '../popup-extend-priority/popup-extend-priority.component';
import { ConfirmErpPopup } from 'app/shared/components/confirm-erp-popup/confirm-erp-popup';
import { TXN_STATUS, UNIT_STATUS } from 'app/shared/constant/transaction.constant';
import { PropertyService } from 'app/pages/property';
import { TemplateRendererComponent } from './template-renderer/template-renderer.component';
import { GridOptions, SortChangedEvent } from 'ag-grid-community';
import PerfectScrollbar from "perfect-scrollbar"
import { PropertyUnitStatus } from 'app/shared/enum/property-unit.enum';
import { PopupPublishStatusComponent } from '../popup-publish-status/popup-publish-status.component';
import { SalesProgramService } from '../../sales-program.service';
import { SalesProgramModel } from 'app/shared/models/saleProgram.model';
import { AttributeEnum } from 'app/pages/ticket-common/ticket-common.excel';
import { UnitType } from 'app/consts/pos.const';
import { ISalesProgram } from 'app/shared/interfaces/sales-program.interface';
import { PopupStatisticComponent } from '../popup-statistic/popup-statistic.component';
import { PopupSyncCRMComponent } from '../popup-sync-crm/popup-sync-crm.component';
import * as moment from 'moment';
import { PopupSyncEscrowCRMComponent } from '../popup-sync-escrow-crm/popup-sync-escrow-crm.component';
import { PopupCreateUserCareplusComponent } from '../popup-create-user-careplus/popup-create-user-careplus';
import { DialogConfirmComponent } from 'app/shared/components/dialog-confirm/dialog-confirm.component';
import { PopupComfirmImportComponent } from '../popup-comfirm-import/popup-comfirm-import.component';
import { PopupImagePropertyComponent } from '../popup-image-property/popup-image-property.component';

export enum EActiveStatus {
    DEFAULT = 'default',
    LOCK = 'lock',
    OPEN = 'open'
}
declare var $: any;
const STORAGE_BOOKMARK_PROJECT = 'STORAGE_BOOKMARK_PROJECT';

@Component({
    selector: 'app-event-sale-list',
    templateUrl: './event-sale.list.component.html',
    styleUrls: ['./event-sale.list.component.scss']
})
export class EventSaleListComponent extends BaseComponent implements OnInit, OnDestroy, AfterViewInit {
    private unsubscribe$: Subject<any> = new Subject();
    @ViewChild('paging') paging: PagingComponent;
    @ViewChild('elementTop') elementTop: ElementRef;
    @ViewChild('greetCell') greetCell: TemplateRef<any>;
    @ViewChild('recieptCode') recieptCode: TemplateRef<any>;
    @ViewChild('recieptStatus') recieptStatus: TemplateRef<any>;
    @ViewChild('recieptAmount') recieptAmount: TemplateRef<any>;
    @ViewChild('recieptCodeYCDC') recieptCodeYCDC: TemplateRef<any>;
    @ViewChild('recieptStatusYCDC') recieptStatusYCDC: TemplateRef<any>;
    @ViewChild('ticketDisplayName') ticketDisplayName: TemplateRef<any>;
    @ViewChild('contractCodeRow') contractCodeRow: TemplateRef<any>;
    @ViewChild('ticketSanName') ticketSanName: TemplateRef<any>;
    @ViewChild('ticketDisplayStatus') ticketDisplayStatus: TemplateRef<any>;
    @ViewChild('ticketPropertyUnitCode') ticketPropertyUnitCode: TemplateRef<any>;
    @ViewChild('eventSaleTable1') eventSaleTable1: TemplateRef<any>;
    @ViewChild('eventSaleTable2') eventSaleTable2: TemplateRef<any>;
    @ViewChild('unitTable') unitTable: TemplateRef<any>;
    @ViewChildren("inputSearch") textAreas: QueryList<ElementRef>;
    @ViewChild("erpAmount") erpAmount: TemplateRef<ElementRef>;

    public isLoading$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    public isTableLoading$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    public isDownloadLoadingAll$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    public isDownloadTicketLoadingTemplate$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    public isTicketCreateDocuments$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    public isSendSmsCustomer$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    wsStatus: EActiveStatus = EActiveStatus.DEFAULT;
    pagination: CPagination<TicketSale> = new CPagination<TicketSale>();
    PrimaryTransactionStatus = PrimaryTransactionStatus;
    arr = Array(20).fill(1);

    settingData = new ProjectSetting();
    filteredOptions: any[] = [];
    myControl = new FormControl();
    searchKey = new FormControl();
    searchStatus: string = null;
    options: any = [];
    selectedProject: any;
    selectedTab: number = 0;
    filterBangHang: {
        keySearch: string,
        priority: string,
        primaryStatus: string,
        posId: string,
        liquidateType: string;
        isContract: boolean;
    } = <any>{};
    propertyArrModel: PropertyTable[] = [];
    propertyArrModelFilter: PropertyTable[] = [];
    propertyArrAllowMoveSalesProgram: any[] = [];
    propertyOfPriorityFilter: PropertyTable[] = [];
    isRefeshUnitTable: boolean = false;
    listExtendUnit: PropertyTable[] = [];
    user: CUser = new CUser();
    project;
    f1SanOfUser;
    currentSalesProgram: ISalesProgram = new SalesProgramModel();
    currentTemplateCode: any;
    currentTemplateName: any;
    status: string = null;
    dvbh: string = null;
    statuses: any  = [
        { id: 'LOCK', name: UNIT_STATUS['LOCK'] },
        { id: 'CLOSE', name: UNIT_STATUS['CLOSE'] },
        { id: 'COMING', name: UNIT_STATUS['COMING'] },
        { id: 'PROCESSING', name: UNIT_STATUS['PROCESSING'] },
        { id: 'CONFIRM', name: UNIT_STATUS['CONFIRM'] },
        { id: 'MCONFIRM', name: UNIT_STATUS['MCONFIRM'] },
        { id: 'SUCCESS', name: UNIT_STATUS['SUCCESS'] },
        { id: 'MSUCCESS', name: UNIT_STATUS['MSUCCESS'] },
        { id: 'LOCK_CONFIRM', name: UNIT_STATUS['LOCK_CONFIRM'] },
        { id: 'UNSUCCESS', name: UNIT_STATUS['UNSUCCESS'] },
        { id: 'CANCEL', name: UNIT_STATUS['CANCEL'] },
        { id: 'MOVED', name: UNIT_STATUS['MOVED'] },
    ];;
    statusesDVKH: any = [
        { id: 'PROCESSING', name: 'Chờ xác nhận' },
        { id: 'REJECTED', name: 'Đã từ chối' },
        { id: 'APPROVED', name: 'Đã chấp nhận' }
    ]
    currentFilter: any = {};
    projectStatusFilter = ProjectStatusEnum.COMING_SALE + ';' + ProjectStatusEnum.TRADING;
    extensionPriorityFilter: boolean = false;

    categories = [CategoryEnum.CAN_HO];
    salesUnits = [];

    exchangeId: string = '';
    isViewAllChecked: boolean = false;
    rootPos: string = '';
    marketPos: string = '';
    rowData: any;

    // codding Gđ2
    projects: any = [];
    saleList: PropertyTable[] = [];
    saleList2: PropertyTable[] = [];
    saleListFilter: PropertyTable[] = [];
    saleList2Filter: PropertyTable[] = [];
    changedRowIds1: string[] = [];
    changedRowIds2: string[] = [];
    isSyncERP: boolean;
    isUnitDrawn: boolean;
    gridHeight: string = '300px';
    drawBlocksHeight: string = '300px';
    gridExpandCount = 2;
    grid1Expanded: boolean = true;
    grid2Expanded: boolean = true;
    autoFreshUI: boolean = false;
    exceptionOnlyFilter: boolean = false;
    forceUpdate1: boolean = false;
    forceUpdate2: boolean = false;
    isRefresh: boolean = false;
    statusFilterUnitDSGD: string;
    statusFilterFreeDSGD: string;
    saleUnitLockConfirmable: boolean = false;
    saleProgramDefault: ISalesProgram = new SalesProgramModel();


    countSaleListStatus = {
        close: 0,
        coming: 0,
        processing: 0,
        confirm: 0,
        unsuccess: 0,
        success: 0,
        lockConfirm: 0,
        moved: 0
    };
    countSaleListStatus2 = {
        close: 0,
        coming: 0,
        processing: 0,
        confirm: 0,
        unsuccess: 0,
        success: 0,
        lockConfirm: 0,
        moved: 0
    };

    cbPriorityAll: boolean = false;

    statusMappings: {
        [Key: string]: string;
    };
    statusClassMappings: {
        [Key: string]: string;
    };

    // event project properties
    priority: number = null;
    isStartPriority: boolean = false;
    projectStage: number;
    projectStatus: string;
    projectStageStatus: string;
    startEvent: boolean = false;
    endEvent: boolean = false;
    startStage: boolean = false;
    startPriority: boolean = false;
    templateFileHasStatus: any = [];
    private selectedPriorities: string[] = [];
    public readonly RadioLiquidateList: any[] = [
        {
          key: 'REFUND',
          value: 'Hoàn tiền đặt cọc'
        },
        {
          key: 'CANCEL',
          value: 'Thanh lý không hoàn tiền'
        },
        {
            key: 'TRANSFER',
            value: 'Đổi tên/Chuyển nhượng'
        },
    ];
    pos: any = [];
    posFilter: any = [];
    owners: any = [
        { id: '', name: '' }];
    salesUnit: any = [];
    posBangHang: any = [];
    salesProgram: any = [];
    dropdownStatus = Object.keys(TXN_STATUS).map(txn => {
        return { id: txn, name: TXN_STATUS[txn] };
    });

    public canImportSaleList: boolean = false;
    public canDownloadPrimary: boolean = false;
    public canDownloadDmsp: boolean = false;
    public canDownloadPriorityReport: boolean = false;
    public canDownloadPrioritiesOfPosReport: boolean = false;
    public canDownloadSaleList: boolean = false;
    public canAutoSortDSGD: boolean = false;
    public canSendEmail: boolean = false;
    public canCsSendSmsCusConfirm: boolean = false;
    public canChangeStatusSaleList: boolean = false;
    public canViewEventProject: boolean = false;
    public canEditSetting: boolean = false;
    public canImportPrimary: boolean = false;
    public canUpdatePrimary: boolean = false;
    public canViewListDemand: boolean = false;
    public canDownloadListDemand: boolean = false;
    public canCreateDemand: boolean = false;
    public canCreateTicketForEmp: boolean = false;
    public canUpdatePrice: boolean = false;
    public canPublishStatus: boolean = false;
    public canUpdateProject: boolean = false;
    public canLockSaleList: boolean = false;
    public canRevokeUnit: boolean = false;
    public canRemoveUnit: boolean = false;
    public canMovePosUnit: boolean = false;
    public canLiquidateUnit: boolean = false;
    public canLiquidateUnitAny: boolean = false;
    public canTransferUnit: boolean = false;
    public isRevokeAny: boolean = false;
    public canImportYCDCO: boolean = false;

    public canAdminApprovedTicket: boolean = false;
    public canSaApprovedTicket: boolean = false;
    public canCsApprovedTicket: boolean = false;
    public canAdminApprovedCancelTicket: boolean = false;
    public canCsApprovedCancelTicket: boolean = false;
    public canViewAllTicket: boolean = false;
    public canViewMarketAllTicket: boolean = false;

    public showLockExtendPriority: boolean = false;
    public isHasDVKH: boolean = false;
    public projectId: string = '';
    public bookmarkedProjects: any = {};
    public canMatchUnit: boolean = false;
    public canMatchAllUnit: boolean = false;
    public isCollapse: boolean = false;
    public canImportTransferEscrow: boolean = false;
    public canCreateAccountCustomer: boolean = false;
    public canCreateUserCare: boolean = false;
    public canPropertyUnitGetAll: boolean = false;
    public canViewAllCustomer: boolean = false;
    public canExportDSGD: boolean = false;
    public canSynErpTicket: boolean = false;
    public isSyncCRMInvestor: boolean = false;
    public isSuperAdmin: boolean = false;
    public isPublic: boolean = false;
    public isCreateDrawn: boolean = false;
    public isRollDrawn: boolean = false;
    public canTicketCreateDocuments: boolean = false;
    public isCheckAll: boolean = false;
    public nameSaleProgram: string = 'Chương trình BH';
    public countSalesProgramChoose = 1;
    public lstSaleProgramId: string = '';
    public selectSaleProgram: any[] = [];
    public selectSaleProgramApply: any[] = [];
    public selectPoses: any[] = [];
    public lstView1: any[] = [];
    public registerConsignmentUrl: string = null;
    public documentUrl: string = null;
    public dashboardUrl: string = null;
    public get bookmarkedProjectList() {
        return Object.values(this.bookmarkedProjects);
    }

    public get isActive() {
        switch (this.wsStatus) {
            case EActiveStatus.DEFAULT:
                if (!this.project) { return true; }
                // unset, lock by time
                const isValidTime = !this.project.setting || !this.project.setting.openingTimeSale || !this.isInvalidTime(this.project.setting.openingTimeSale, this.project.setting.endTimeSale);
                return this.currentSalesProgram.lock ? (this.canLockSaleList && isValidTime) : isValidTime;
            case EActiveStatus.OPEN:
                // active for user have permission
                return this.canLockSaleList;
            case EActiveStatus.LOCK:
                // lock all
                return false;
        }
    }

    public get categoryId() {
        return this.categories[this.selectedTab] || '';
    }
    public canHoDraw = [];
    public jsonCanHo = {};
    public jsonDatNen = {};
    public blockFloorRoom = [];
    public countPrimaryStatus = {
        lock: 0,
        close: 0,
        coming: 0,
        processing: 0,
        confirm: 0,
        unsuccess: 0,
        lockConfirm: 0,
        success: 0,
        cancel: 0,
        transfer: 0,
        deposit: 0,
        moved: 0
    };
    public countTicketStatus = {
        bookingApproved: 0,
        priorityUpdated: 0,
        cancelled: 0,
        processing: 0
    };

    public filterPriorityOptions = [
        { id: Constant.PRIORITY_ID.UT1, name: 'Ưu tiên 1', name2: '1' },
        { id: Constant.PRIORITY_ID.UT2, name: 'Ưu tiên 2', name2: '2' },
        { id: Constant.PRIORITY_ID.UT3, name: 'Ưu tiên 3', name2: '3' },
        { id: Constant.PRIORITY_ID.EMPTY, name: 'Trống ưu tiên', name2: 'Trống' },
        { id: Constant.PRIORITY_ID.LOADING, name: 'Đang BSUT', name2: 'Đang BSUT' },
    ];

    public countPriorities = {
        p1: 0,
        p2: 0,
        p3: 0
    };
    public numberPrioritiesOfFloor = {};


    public viewTableModeValue = 1;
    public readonly ViewTableModes = [
        { value: 1, text: 'Sơ đồ' },
        { value: 2, text: 'Ưu tiên' }
    ];
    public filterDSGD = {
        keySearch: ''
    };

    recoveryUnit: string;

    selectedBlock: string;
    selectedFloor: string;
    listFloor: any;
    erpInfo: any;
    campaignErp: string;
    employeeErp: any;
    createdFrom: any;
    createdTo: any;
    startDate: any;
    endDate: any;
    pdfStart: any;
    pdfEnd: any;
    step: any = 'step1';
    defaultGridOptions: GridOptions = {
        defaultColDef: {
            resizable: true,
            autoHeight: true,
            cellClass: 'cell-wrap-text',
            enableCellChangeFlash: false
        },
        autoSizePadding: 0,
        cellFlashDelay: 500,
        suppressRowTransform: true,
        enableCellTextSelection: true,
        suppressDragLeaveHidesColumns: true,
        rowSelection: 'single',
        onSortChanged: (event: SortChangedEvent) => {
            this.getDataSortChange(event.api.getSortModel()[0]);
        },
        onRowDataChanged: (params) => {
            params.columnApi.autoSizeAllColumns();
            params.api.resetRowHeights();
        }
    };
    gridOptions: GridOptions = { ...this.defaultGridOptions };
    gridOptionsYCDCO: GridOptions = { ...this.defaultGridOptions };
    private primaryTransactionProjection = 'surveys,customer2,_id,id,code,ticketType,demandCategory,bookingTicketCode,systemnoErp,reciept.code,reciept.status,reciept.id,reciept.amount,customer.id,customer.info,customer.personalInfo,customer.identities,customer.bankInfo,customer.taxCode,customer.codeDx,customer.code,employee.name,pos.name,pos.id,employee.pos.name,createdDate,status,employee.id,escrowTicketCode,contract.id,contract.code,contract.type,propertyUnit.id,propertyUnit.code,project.id,project.name,erp';
    private gridApi;
    private rowSelection;
    private rowModelType;
    private getRowNodeId;
    private components;
    // public isShowPopupUpdateFeature: boolean;
    selectPos: string[] = [];
    selectPosApply: string[] = [];
    selectPosSearch: string[] = [];
    selectPosSearchApply: string[] = [];
    selectStatus: string[] = [];
    selectStatusApply: string[] = [];
    isBtnSearch = false;
    isEnabledEdit = false;
    isHasPermisstionEdit = false;
    isEditCustomerInfo = false;
    canUpdateCustomerTicket = false;
    canGoContract = false;
    isOrderEditableUsed = false;
    eventProjectId = '';
    currentSalesProgramId = '';
    createdDateStart = '';
    createdDateEnd = '';
    modifiedDateStart = '';
    modifiedDateEnd = '';
    enableRecount: boolean = false;
    constructor(
        injector: Injector,
        public toastr: ToastrService,
        public router: Router,
        public route: ActivatedRoute,
        public projectService: ProjectService,
        private sidenav: SidenavService,
        private dialog: MatDialog,
        private storage: StorageService,
        private userV2Service: UserV2Service,
        private authorizeService: AuthorizeService,
        private ng2SearchPipe: Ng2SearchPipe,
        private excelService: ExcelService,
        private projectPrimaryTransactionService: ProjectPrimaryTransactionService,
        private primaryTransactionService: PrimaryTransactionService,
        private propertyService: PropertyService,
        private propertyPrimaryService: PropertyPrimaryService,
        private erpService: ErpService,
        private elementRef: ElementRef,
        private salesProgramService: SalesProgramService,
    ) {
        super(injector.get(PropertyPrimaryService), Property, injector);
        this.bookmarkedProjects = this.storage.retrieve(STORAGE_BOOKMARK_PROJECT) || {};
        // this.isShowPopupUpdateFeature = this.storage.retrieve(STORAGE_UPDATE_FEATURE) || false;
        this.route.paramMap.subscribe((paramMap: ParamMap) => {
            this.projectId = paramMap.get('id');
            this.myControl.setValue(this.projectId);
        });
        this.rowSelection = 'multiple';
        this.rowModelType = 'infinite';
        this.getRowNodeId = function (data) {
            return data.id; // YCDCH realtime không trả về code
        };
        this.components = {
            rowIdRenderer: function (params) {
                return 1 + params.rowIndex;
            },
        };
    }

    ngAfterViewInit() {
        const htmlDivElement: HTMLElement = this.elementTop.nativeElement;
        let el: any = document.getElementById("tabGroup");
        let elh = el.children
            .item(0)
            .children.item(1)
            .children.item(0)
            .children.item(0).children;
        elh.item(1).setAttribute("style", "display: none;");

        this.gridOptions.api.setColumnDefs([
            {
                headerName: 'STT',
                cellRenderer: 'rowIdRenderer',
                width: 50,
                pinned: 'left',
            },
            {
                headerName: 'Mã',
                colId: 'bookingTicketCode',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.ticketDisplayName
                },
                sortable: true,
                unSortIcon: true,
                width: 260,
                pinned: 'left',
            },
            {
                headerName: 'Mã ERP',
                field: 'systemnoErp',
                colId: 'systemnoErp',
                width: this.isManager() ? 90 : 110,
                sortable: true,
                unSortIcon: true,
            },
            {
                headerName: 'Mã PT',
                colId: 'recieptCode',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.recieptCode
                },
                width: this.isManager() ? 80 : 100,
            },
            {
                headerName: 'Trạng thái PT',
                colId: 'recieptStatus',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.recieptStatus
                },
                width: this.isManager() ? 100 : 120,
            },
            {
                headerName: 'Số tiền',
                colId: 'recieptAmount',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.recieptAmount
                },
                width: this.isManager() ? 100 : 120,
            },
            {
                headerName: 'Khách hàng',
                field: 'customerName',
                colId: 'customer.personalInfo.name',
                width: this.isManager() ? 140 : null,
                sortable: true,
                unSortIcon: true,
            },
            {
                headerName: 'Số điện thoại',
                field: 'phone',
                width: this.isManager() ? 110 : 120,
            },
            {
                headerName: 'CMND/HC',
                field: 'identity',
                width: this.isManager() ? 110 : 120,
            },
            {
                headerName: 'Tư vấn viên',
                field: 'employeeTakeCareName',
                colId: 'employee.name',
                sortable: true,
                unSortIcon: true,
                hide: !this.isManager(),
            },
            {
                headerName: 'Sàn',
                suppressSizeToFit: true,
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.ticketSanName
                },
            },
            {
                headerName: 'ĐVBH',
                field: 'posName',
                hide: !this.isManager(),
            },
            {
                headerName: 'Thời gian',
                field: 'createdAt',
                colId: 'createdDate',
                sortable: true,
                unSortIcon: true,
                width: this.isManager() ? 120 : null
            },
            {
                headerName: 'Trạng thái',
                colId: 'status',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.ticketDisplayStatus
                },
                sortable: true,
                unSortIcon: true,
                width: this.isManager() ? 180 : null
            },
            {
                headerName: 'Hành động',
                colId: 'icon',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.greetCell
                },
                width: this.isManager() ? 130 : null
            },
        ]);

        this.gridOptionsYCDCO.api.setColumnDefs([
            {
                headerName: 'STT',
                cellRenderer: 'rowIdRenderer',
                width: 50,
                pinned: 'left',
            },
            {
                headerName: 'Mã',
                colId: 'escrowTicketCode',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.ticketDisplayName
                },
                sortable: true,
                unSortIcon: true,
                width: 300,
                pinned: 'left',
            },
            {
                headerName: 'Số HĐ',
                colId: 'contractCode',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.contractCodeRow
                },
                width: 50,
                pinned: 'left',
            },
            {
                headerName: 'Sản phẩm',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.ticketPropertyUnitCode
                },
                width: this.isManager() ? 140 : 160,
            },

            {
                headerName: 'Mã PT',
                colId: 'recieptCode',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.recieptCodeYCDC
                },
                width: this.isManager() ? 80 : 100,
            },
            {
                headerName: 'Trạng thái PT',
                colId: 'recieptStatus',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.recieptStatusYCDC
                },
                width: this.isManager() ? 100 : 120,
            },
            {
                headerName: 'Số tiền',
                colId: 'recieptAmount',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.recieptAmount
                },
                width: this.isManager() ? 100 : 120,
            },
            {
                headerName: 'Khách hàng',
                field: 'customerName',
                colId: 'customer.personalInfo.name',
                width: this.isManager() ? 220 : null,
                sortable: true,
                unSortIcon: true,
            },
            {
                headerName: 'Số điện thoại',
                field: 'phone',
                width: 130,
            },
            {
                headerName: 'Tư vấn viên',
                field: 'employeeTakeCareName',
                colId: 'employee.name',
                sortable: true,
                unSortIcon: true,
                hide: !this.isManager(),
            },
            {
                headerName: 'Sàn',
                suppressSizeToFit: true,
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.ticketSanName
                },
            },
            {
                headerName: 'ĐVBH',
                field: 'posName',
                hide: !this.isManager(),
            },
            {
                headerName: 'Thời gian',
                field: 'createdAt',
                colId: 'createdDate',
                sortable: true,
                unSortIcon: true,
                width: this.isManager() ? 140 : null
            },
            {
                headerName: 'Trạng thái',
                colId: 'status',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.ticketDisplayStatus
                },
                sortable: true,
                unSortIcon: true,
                width: this.isManager() ? 200 : null
            },

            {
                headerName: 'Số tiền ERP',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.erpAmount
                },
            },
            {
                headerName: 'Hành động',
                colId: 'icon',
                cellRendererFramework: TemplateRendererComponent,
                cellRendererParams: {
                    ngTemplate: this.greetCell
                },
                width: this.isManager() ? 150 : null
            },
        ]);

        // if(!this.isShowPopupUpdateFeature) {
        //   this.openPopupUpdateFeature();
        // }
        const ticketId = this.route.queryParams['_value']['ticketId'];
        if(ticketId) {
            this.primaryTransactionService.getTicketDetailById(ticketId).pipe(
                finalize(() => this.isLoading$.next(false)),
                takeUntil(this.unsubscribe$)
              ).subscribe((res: any) => {
                this.openTicketNav(res);
              })
        }
    }
    ngOnInit(): void {
        this.primaryTransactionService.event$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(res => {
                if (res) {
                    let {ticket, action, status, isRequireReason, unit} = res;
                    let isReason = true;
                    let isSyncCancel = res.isCancel;
                    switch (action) {
                        case 'ADMIN_APPROVED_TICKET':
                            this.onYCDCHAdminApproveReject(ticket, unit ? unit.id : '', status, isReason, isRequireReason);
                            break;
                        case 'CS_APPROVED_TICKET':
                            this.onChangeBookingTicketStatus(ticket, status, isReason, isRequireReason);
                            break;
                        case 'ADMIN_APPROVED_CANCEL_REQUESTED':
                            this.onAdminApproveCancelRequestYCDCH(ticket, isReason, isRequireReason);
                            break;
                        case 'ADMIN_CANCEL_REQUESTED':
                            this.onAdminCancelRequestYCDCH(ticket, isReason, isRequireReason);
                            break;
                        case 'CS_APPROVED_CANCEL_REQUESTED':
                            this.onDVKHApproveCancelRequestYCDCH(ticket, isReason, isRequireReason);
                            break;
                        case 'CS_CANCEL_REQUESTED':
                            this.onDVKHCancelRequestYCDCH(ticket, isReason, isRequireReason);
                            break;
                        case 'CANCEL_REQUESTED':
                            this.onSuggestCancelYCDCH(ticket, isReason, isRequireReason);
                            break;
                        case 'TransferEscrow':
                            this.openCreateTicket(ticket, false, true);
                            break;
                        case 'SA_CONFIRM':
                            this.onSaApproveReject(ticket, status, true, false);
                            break;
                        case 'SYNC_BOOKING_CRM':
                            this.onSyncBookingTicket(ticket, isSyncCancel);
                            break;
                        case PrimaryTransactionAction.EXTEND_TIME_BOOKING:
                            this.extendBookingTime(ticket);
                            break;
                        case 'SYNC_ESCROW_CRM':
                            this.onSyncEscrowTicket(ticket, isSyncCancel);
                            break;
                        default:
                            break;
                    }
                }
            })
        this.scrollView();

        this.userV2Service.user$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe((user: CUser) => {

                if (user && user.id) {

                    this.user = user;
                    this.isHasDVKH = this.user.isRoleDVKH();

                    if (this.isHasDVKH) {
                        this.projectService.getAllByDVKH().subscribe(r => {
                            this.projects = this.filteredOptions = r.rows || [];

                        });
                    } else {

                        this.projectService.getProjectListByName('', this.projectStatusFilter).subscribe(r => {
                            if (r && r.code === 1) {
                                this.projects = this.filteredOptions = r.data || [];
                            } else {
                                this.filteredOptions = [];
                            }
                        });

                    }

                    const paramMap = this.route.snapshot.paramMap;
                    this.projectId = paramMap.get('id');
                    this.myControl.setValue(this.projectId || null, { emitEvent: false });

                    if (!this.projectId) {

                        this.selectedProject = null;
                        this.settingData = new ProjectSetting();

                    } else {
                        this.search();
                    }
                }

            });

        this.currentSort = {
            propertyName: '-createdDate', // property Name
            sortOrder: false, //  false Asc, true Desc
        };

        this.onSorted.subscribe(() => {
            this.getData(this.dvbh);
        });

        this.onSorted.subscribe((resAbc) => {
            this.currentSort = resAbc;
        });

        this.reCalculateGridHeight();
        // this.autoFreshUI = !this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicket);

        this.authorizeService.hasAuthority(['EVENT', 'PROJECT', 'ORDER', 'EDIT']).then(isHasRole => this.isHasPermisstionEdit = isHasRole);
        this.authorizeService.hasAuthority(['PROPERTY', 'TICKET', 'CUSTOMER', 'UPDATE']).then(isHasRole => {
            this.canUpdateCustomerTicket = isHasRole;
        });
        this.authorizeService.hasAuthority(['PRIMARY', 'CONTRACT', 'GET', 'ID']).then(isHasRole => {
            this.canGoContract = isHasRole;
        });


    }

    onFirstDataRendered(params) {
        setTimeout(() => {
            const agBodyViewport: HTMLElement = this.elementRef.nativeElement.querySelector('#grid-ycdch .ag-body-viewport');
            const agBodyHorizontalViewport: HTMLElement = this.elementRef.nativeElement.querySelector('#grid-ycdch .ag-body-horizontal-scroll-viewport');
            if (agBodyViewport) {
                const ps = new PerfectScrollbar(agBodyViewport);
                ps.update();
            }
            if (agBodyHorizontalViewport) {
                const ps = new PerfectScrollbar(agBodyHorizontalViewport);
                ps.update();
            }
            $("#grid-ycdch .ag-body-viewport").css({ "position": "relative !important", "-webkit-overflow-scrolling": "touch" });
            $("#grid-ycdch .ag-body-horizontal-scroll-viewport").css({ "position": "relative !important", "-webkit-overflow-scrolling": "touch" });
        }, 1000);
    }

    onFirstDataRenderedYCDCO(params) {
        setTimeout(() => {
            const agBodyViewport: HTMLElement = this.elementRef.nativeElement.querySelector('#grid-ycdco .ag-body-viewport');
            const agBodyHorizontalViewport: HTMLElement = this.elementRef.nativeElement.querySelector('#grid-ycdco .ag-body-horizontal-scroll-viewport');
            if (agBodyViewport) {
                const ps = new PerfectScrollbar(agBodyViewport);
                ps.update();
            }
            if (agBodyHorizontalViewport) {
                const ps = new PerfectScrollbar(agBodyHorizontalViewport);
                ps.update();
            }
            $("#grid-ycdco .ag-body-viewport").css({ "position": "relative !important", "-webkit-overflow-scrolling": "touch" });
            $("#grid-ycdco .ag-body-horizontal-scroll-viewport").css({ "position": "relative !important", "-webkit-overflow-scrolling": "touch" });
        }, 1000);
    }

    getDataSortChange(data) {
        const ticketType = this.selectedTab === 2 ? 'YCDCH' : 'YCDC';
        let sort = '';
        if (!data) {
            return;
        }
        if (data && data.sort === 'asc') {
            sort = data.colId;
        } else {
            sort = `-${data.colId}`;
        }
        if (sort && sort.length > 0) {
            let params = {
                exchangeId: this.exchangeId,
                ticketType: ticketType,
                keywords: this.searchKey.value ? this.searchKey.value.trim() : '',
                status: this.searchStatus,
                sort: sort,
                page: 1,
                pageSize: this.pagination.pageSize,
                _fields: this.primaryTransactionProjection
            }
            this.primaryTransactionService.getTicket(this.myControl.value, params)
                .pipe(
                    finalize(() => this.isTableLoading$.next(false)),
                    takeUntil(this.unsubscribe$)
                )
                .subscribe(res => {
                    this.summaryData(res);
                })
        }
    }


    onModalChange($event) {
        if ($event && $event.term && typeof $event.term === 'string') {
            $event.term = $event.term.trim();
            if (!this.isHasDVKH) {
                this.projectService.getProjectListByName($event.term, this.projectStatusFilter).subscribe(r => {
                    this.projects = this.filteredOptions = r.data || [];
                });
            }
        }
    }

    searchCode() {
        let ticketType = '';
        switch (this.selectedTab) {
            case 1:
                ticketType = 'YCTV';
                break;
            case 2:
                ticketType = 'YCDCH';
                break;
            case 3:
                ticketType = 'YCDC';
                break;
            default:
                ticketType = 'YCTV';
                break;
        }

        let keyword = '';
        if (this.searchKey.value) {
            keyword = this.searchKey.value.trim();
        }

        let p: any = {
            ticketType: ticketType,
            exchangeId: this.dvbh,
            keywords: keyword,
            status: this.searchStatus
        }
        if (this.modifiedDateStart) {
            p.modifiedDateStart = moment(this.modifiedDateStart).startOf('date').toISOString();
        }
        if (this.modifiedDateEnd) {
            p.modifiedDateEnd = moment(this.modifiedDateEnd).endOf('date').toISOString();
        }
        if (this.createdDateStart) {
            p.createdDateStart = moment(this.createdDateStart).startOf('date').toISOString();
        }
        if (this.createdDateEnd) {
            p.createdDateEnd = moment(this.createdDateEnd).endOf('date').toISOString();
        }
        this.getTicket(p);

    }

    ngOnDestroy() {
        this.primaryTransactionService.unSubscribeEvent();
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
        this.unsubscribe();
        this.eventSubscribe && this.eventSubscribe.unsubscribe();
    }


    displayFn(item?: any): string | undefined {
        return item ? item.name : undefined;
    }

    openSetting() {
        if (!this.settingData.id) {
            return;
        }

        this.sidenav.toggle();
    }

    _updateRoute(tabIndex) {
        this.selectedTab = tabIndex;
        const projectId = this.selectedProject ? this.selectedProject.id : this.projectId;
        if (projectId) {
            const query = {...this.route.snapshot.queryParams};
            this.updateQueryParams(query, 'tab', tabIndex);
            // this.router.navigate(['project/event-sale', projectId], { queryParams: { tab: tabIndex } });
        }
    }

    search() {
        this.settingData = this.selectedProject && this.selectedProject.setting ? new ProjectSetting(this.selectedProject.setting) : new ProjectSetting();
        this.settingData.id = this.selectedProject && this.selectedProject.id ? this.selectedProject.id : this.projectId;
        let { spid, tpl } = this.route.snapshot.queryParams;

        this.projectService.getOne(this.settingData.id).pipe(
            takeUntil(this.unsubscribe$)
        ).subscribe(async res => {

            if (!this.filteredOptions.some(el => el.id === res.id)) {
                this.filteredOptions = [res, ...this.filteredOptions];
            }

            this.projectId = res.id;
            this.registerConsignmentUrl = res.registerConsignmentUrl;
            this.documentUrl = res.documentUrl;
            this.dashboardUrl = res.dashboardUrl;
            this.isPublic = res.isPublic;
            this.selectedProject = {
                id: res.id,
                name: res.name,
                code: res.code,
                setting: res.setting,
                surveys: res.surveys,
                finaUrl: res.finaUrl,
                isRegisterFina: res.isRegisterFina,
                dashboardUrl: res.dashboardUrl
            };
            if (res.setting.templateFileHasStatus) {
                this.templateFileHasStatus = res.setting.templateFileHasStatus.filter(i => !i.stage || (i.stage && i.stage.includes(0)));
            }
            this.isSyncERP = this.selectedProject.setting.salesUnit.some(su => su.erpAccount && su.erpAccount.trim() !== '' && su.erpAccount.trim().toUpperCase() !== 'Z');

            if (res && res.setting && res.setting.rootPos) {
                this.rootPos = res.setting.rootPos.id;
            }
            if (res && res.txnStatusConfig) {
                Object.keys(Object.assign(TXN_STATUS, res.txnStatusConfig));
            }
            if (res && res.unitStatusConfig) {
                Object.keys(Object.assign(UNIT_STATUS, res.unitStatusConfig));
            }


            this.recoveryUnit = res.setting && res.setting.rootPos ? res.setting.rootPos.id : '';
            this.salesUnit = res && res.setting && res.setting.salesUnit ? res.setting.salesUnit : [];
            this.pos = this.salesUnit.filter(x => x.unitType === UnitType.F1);
            this.posBangHang = [{
                name: 'Rổ chung',
                id: 'null'
            }, ...this.pos];
            this.project = res;
            this.user.f1San = await this.projectService.getF1San({ projectId: this.project.id }).toPromise();
            await this.setupPermission();
            this.getEventProject();
            this.currentFilter = {};
            this.status = null;

            let paramTab = this.route.snapshot.queryParams['tab'];
            paramTab = paramTab ? (paramTab = +paramTab) : paramTab || 0;

            if (this.canViewMarketAllTicket && res && res.setting && res.setting.marketPos) {
                this.marketPos = res.setting.marketPos.id;
            }
            if (res && res.setting && res.setting.isSyncCRMInvestor) {
                this.isSyncCRMInvestor = res.setting.isSyncCRMInvestor;
            }
            let el: any = document.getElementById("tabGroup");
            let elh = el.children
                .item(0)
                .children.item(1)
                .children.item(0)
                .children.item(0).children;
            if(this.canViewMarketAllTicket){
                elh.item(2).setAttribute("style", "display: none;");
                elh.item(3).setAttribute("style", "display: none;");
                elh.item(4).setAttribute("style", "display: none;");
                elh.item(5).setAttribute("style", "display: none;");
            }
            if(this.documentUrl){
                elh.item(5).setAttribute("style", "display: none;");
            }
            this.selectedTab = (!this.canDownloadPrimary && paramTab < this.categories.length) ? (this.categories.length + 1) : (paramTab || 0);
            this.salesProgramService.getSalesProgramByProjectId(res.id).subscribe(res => {
                const salesProgram = [];
                if (res && res.length > 0) {
                    res.forEach(e => {
                        salesProgram.push({ id: e.id, name: e.name , checked: true });
                    });
                    this.salesProgram = salesProgram;
                    this.isCheckAll = true;
                    const filter = this.salesProgram.map(element => {
                        return element.id
                    });
                    this.onChangeFilterBangHang(
                        spid
                            ? { id: spid }
                            : { id: filter }
                    );
                    let salesProgramId = this.route.snapshot.queryParams['salesProgramId'];
                    if (!spid && salesProgramId) {
                        spid = salesProgramId;
                    }
                    const currentSalesProgram = res.find(e => e.id === spid);
                    this.currentSalesProgram = currentSalesProgram ? new SalesProgramModel(currentSalesProgram) : new SalesProgramModel(res[0]);
                    this.currentTemplateCode = tpl || this.currentSalesProgram.unitTableTemplateUrl && this.currentSalesProgram.unitTableTemplateUrl[0] && this.currentSalesProgram.unitTableTemplateUrl[0].templateCode;
                    this.currentTemplateName = tpl || this.currentSalesProgram.unitTableTemplateUrl && this.currentSalesProgram.unitTableTemplateUrl[0] && this.currentSalesProgram.unitTableTemplateUrl[0].templateName;
                    this.owners = this.currentSalesProgram.owners;
                    this.blockFloorRoom = this.currentSalesProgram.blocks && this.currentSalesProgram.blocks.map((el: any, index: number) => {
                        return {
                            isDisplay: true,
                            block: el.block,
                            floors: el.floors.split(',').reverse(),
                            rooms: el.rooms.split(',')
                        };
                    });

                    this.canHoDraw = this.convertBlockToView();
                    this.getEventProject();
                    if (this.currentSalesProgramId) this.currentSalesProgram.id = this.currentSalesProgramId;
                    this.saleUnitLockConfirmable = this.currentSalesProgram.saleUnitLockConfirmable;
                }
                this.changeTab(this.selectedTab);
            });

            this.infoMessagingPattern = [this.messagingPattern + 'msx-adsg.event-project.' + this.project.id];
            this.subscribe();
            this.eventSubscribe = this.eventChangeService.emitChangeSource.subscribe((data) => {
                try {
                    if (data.broadcast && data.message && data.message.data) {
                        const dataCheck = data.message.data;
                        if (!dataCheck || (dataCheck.salesProgramId && this.selectSaleProgramApply.length && !this.selectSaleProgramApply.includes(dataCheck.salesProgramId))) {
                            return;
                        }
                    }
                    switch (data.broadcast) {
                        case 'primaryTransUpdatedAdmin':
                            this._wsHandlePrimaryTransUpdatedAdmin(data.message.data);
                            break;
                        case 'nextStage':
                            // bắt đầu 1 giai đoạn, khóa all
                            this.wsStatus = EActiveStatus.LOCK;
                            this.projectStage = data.message.data ? data.message.data.stage : 1;
                            this.startStage = true;
                            this.refresh();
                            break;
                        case 'nextPriority':
                            // Bắt đầu 1 Ưu tiên, mở cho user có quyền
                            this.priority++;
                            this.startPriority = true;
                            this.refresh();
                            break;
                        case 'solveUnitNotSucc':
                            // Chuyển SP chưa thành công về rổ chung
                            this.refresh();
                            this.startPriority = false;
                            break;
                        case 'endPriority':
                            // kết thúc 1 Ưu tiên, mở cho user có quyền
                            this.startPriority = false;
                            this.refresh();
                            break;
                        case 'endStage':
                            // kết thúc 1 giai đoạn, mở cho user có quyền
                            this.wsStatus = EActiveStatus.OPEN;
                            this.startStage = false;
                            this.refresh();
                            break;
                        case 'endEvent':
                            // kết thúc sự kiện , cho về default
                            this.wsStatus = EActiveStatus.DEFAULT;
                            this.startEvent = false;
                            this.endEvent = true;
                            this.refresh();
                            break;
                        case 'startEvent':
                            // bắt đầu sự kiện, khóa all
                            this.wsStatus = EActiveStatus.LOCK;
                            this.startEvent = true;
                            this.refresh();
                            this.getEventProject();
                            break;
                        case 'broadcastMessage':
                            let message = data.message.data.message ? data.message.data.message : data.message.data;
                            let time = data.message.data.time ? data.message.data.time : '';
                            this.toastr.success(`
                <img src='./assets/img/icon/bell.svg'></img>
                <span> ${time ? moment(time).format('DD-MM-YYYY HH:mm:ss') + ': ' : ''} <b>${message}</b></span>`, 'Thông báo',
                                {
                                    enableHtml: true,
                                    disableTimeOut: true,
                                    closeButton: true,
                                    positionClass: 'toast-top-right',
                                    messageClass: 'message-toast',
                                    titleClass: 'message-toast-title'
                                }
                            );
                            break;
                        case 'importCompleted':
                            const model: any = data.message.data;
                            switch (this.selectedTab) {
                                // case 0: // Dat nen
                                case 0: // Can ho
                                    if (model.type === 'PRIMARY') {
                                        this.getTableData();
                                    }
                                    break;
                                case 2: // Danh sach giao dich
                                    if (model.type === 'PRIMARY-TRANSACTION') {
                                        this.getSaleListData();
                                    }
                                    break;
                                case 3: // Danh sach cọc
                                    if (model.type === 'PRIMARY-TRANSACTION-IMPORT') {
                                        this.getData();
                                    }
                                    break;
                                default:
                                    break;
                            }
                            break;
                        case 'transferCompleted':
                            this.refresh();
                            break;
                        case 'revokeUnit':
                            this._wsHanleRevokeUnit(data.message.data);
                            break;
                        case 'transferUnit':
                            this._wsHanletTransferUnit(data.message.data);
                            break;
                        case 'removeUnit':
                            this._wsHandleRemoveUnit(data.message.data);
                            break;
                        case 'projectLockPriority':
                            if (this.projectId === data.message.data.projectId && this.currentSalesProgram.id === data.message.data.salesProgramId) {
                                this.currentSalesProgram.lock = data.message.data.lock;
                                this.currentSalesProgram.lockedPoint++;
                            }
                            break;
                        case 'projectUnitExtendPos':
                            this._wsHandleProjectUnitExtendPos(data);
                            break;
                        case 'projectUnitLockExtendPos':
                            this._wsHandleProjectUnitLockExtendPos(data);
                            break;
                        case 'projectUnitUpdatePriority':
                            this._wsHandleProjectUnitUpdatePriority(data);
                            break;
                        case 'startEditOrder':
                            this.toastr.success('Đã mở khóa sửa cọc', 'Sửa cọc! ');
                            this.search();
                            break;
                        case 'endEditOrder':
                            this.toastr.error('Thời gian sửa cọc đã kết thúc !');
                            this.isEnabledEdit = false;
                            this.isOrderEditableUsed = false;
                            this.isEditCustomerInfo = false;
                            break;
                        default:
                            break;
                    }
                } catch (error) {
                    console.log('eventSubscribe', error);
                }
            });

        });
    }

    onAfterCollapse(params) {
        this.gridExpandCount--;
        this.reCalculateGridHeight();
    }

    onAfterExpand(params) {
        this.gridExpandCount++;
        this.reCalculateGridHeight();
    }

    reCalculateGridHeight() {
        if (this.gridExpandCount < 1) { return '0px'; }
        let remainingHeight = window.innerHeight
            - 42 // breadcrumb
            - 12 // padding
            - 64 - 12 // filter & controls + padding
            - 25 // Tabs list
            - 40; // action panels
        if (this.isCollapse) {
            remainingHeight += 72;
        }
        this.drawBlocksHeight = remainingHeight + 'px';
        this.gridHeight = (remainingHeight / this.gridExpandCount) // Average height for each grid container
            - 30 // Expansion panel header
            - 32 * (2 - this.gridExpandCount) // Table header
            + 'px';
    }

    private setupPermission() {
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'IMPORT'])
            .then(isHasRole => {
                this.canImportSaleList = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PRIMARY', 'TRANSACTION', 'MATCH', 'PRIORITY'])
            .then(isHasRole => {
                this.canMatchUnit = (isHasRole && this.isF1San()) || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PRIMARY', 'TRANSACTION', 'MATCH', 'PRIORITY', 'ALL'])
            .then(isHasRole => {
                this.canMatchAllUnit = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'GET'])
            .then(isHasRole => {
                this.canDownloadPrimary = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'DOWNLOAD', 'DMSP'])
            .then(isHasRole => {
                this.canDownloadDmsp = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'GET'])
            .then(isHasRole => {
                this.canDownloadSaleList = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'GET', 'ALL'])
            .then(isHasRole => {
                this.canAutoSortDSGD = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'SEND', 'EMAIL'])
            .then(isHasRole => {
                this.canSendEmail = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['CS', 'SEND', 'SMS', 'CUSTOMER' , 'CONFIRM'])
            .then(isHasRole => {
                this.canCsSendSmsCusConfirm = isHasRole;
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'UPDATE'])
            .then(isHasRole => {
                this.canChangeStatusSaleList = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['EVENT', 'PROJECT', 'ACTION'])
            .then(isHasRole => {
                this.canViewEventProject = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'SETTING'])
            .then(isHasRole => {
                this.canEditSetting = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'IMPORT'])
            .then(isHasRole => {
                this.canImportPrimary = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'UPDATE', 'UNIT'])
            .then(isHasRole => {
                this.canUpdatePrimary = isHasRole;
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'UNIT', 'TRANSFER'])
            .then(isHasRole => {
                this.canTransferUnit = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'TICKET', 'GET'])
            .then(isHasRole => {
                this.canViewListDemand = isHasRole || this.isInSaleList() || this.isProjectLeader();
                this.canDownloadListDemand = ((isHasRole || this.isInSaleList()) && this.canImportSaleList) || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'TICKET', 'CREATE'])
            .then(isHasRole => {
                this.canCreateDemand = (isHasRole && this.isInSaleList());
            });
        this.authorizeService.hasAuthority(['TICKET', 'CREATE', 'FOR', 'EMPLOYEE'])
            .then(isHasRole => {
                this.canCreateTicketForEmp = (isHasRole && this.isInSaleList());
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PROJECT', 'UPDATE']).then(isHasRole => {
            this.canUpdateProject = (isHasRole || this.isProjectLeader());
        });
        this.authorizeService.hasAuthority(['SALES', 'PROGRAM', 'LOCK']).then(isHasRole => {
            this.canLockSaleList = (isHasRole || this.isProjectLeader());
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'UPDATE', 'PRICE']).then(isHasRole => {
            this.canUpdatePrice = (isHasRole || this.isProjectLeader());
        });
        this.authorizeService.hasAuthority(['PRIMARY', 'PUBLISH', 'STATUS', 'AS']).then(isHasRole => {
            this.canPublishStatus = (isHasRole || this.isProjectLeader());
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'REVOKE']).then(isHasRole => {
            this.canRevokeUnit = (isHasRole || this.isProjectLeader());
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'REMOVE' , 'UNIT']).then(isHasRole => {
            this.canRemoveUnit = isHasRole;
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'MOVE']).then(isHasRole => {
            this.canMovePosUnit = (isHasRole || this.isProjectLeader());
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'LIQUIDATE']).then(isHasRole => {
            this.canLiquidateUnit = isHasRole;
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'LIQUIDATE', 'ANY']).then(isHasRole => {
            this.canLiquidateUnitAny = isHasRole;
        });
        this.authorizeService.hasAuthority(['ADMIN', 'APPROVED', 'TICKET']).then(isHasRole => {
            this.canAdminApprovedTicket = isHasRole;
        });
        this.authorizeService.hasAuthority(['SA', 'APPROVED', 'TICKET']).then(isHasRole => {
            this.canSaApprovedTicket = isHasRole;
        });
        this.authorizeService.hasAuthority(['CS', 'APPROVED', 'TICKET']).then(isHasRole => {
            this.canCsApprovedTicket = isHasRole;
        });
        this.authorizeService.hasAuthority(['ADMIN', 'APPROVED', 'CANCEL', 'TICKET']).then(isHasRole => {
            this.canAdminApprovedCancelTicket = isHasRole;
        });
        this.authorizeService.hasAuthority(['CS', 'APPROVED', 'CANCEL', 'TICKET']).then(isHasRole => {
            this.canCsApprovedCancelTicket = isHasRole;
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'UNIT', 'REPORT', 'MATCHING', 'PRIORITY'])
            .then(isHasRole => {
                this.canDownloadPriorityReport = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'UNIT', 'REPORT', 'POS', 'PRIORITY'])
            .then(isHasRole => {
                this.canDownloadPrioritiesOfPosReport = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'TICKET', 'IMPORT', 'TRANSFER', 'ESCROW'])
            .then(isHasRole => {
                this.canImportTransferEscrow = isHasRole;
            });
        this.authorizeService.hasAuthority(['CREATE', 'ACCOUNT', 'CUSTOMER', 'EVENT'])
            .then(isHasRole => {
                this.canCreateAccountCustomer = isHasRole;
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'USER', 'CARE', 'HISTORY'])
            .then(isHasRole => {
                this.canCreateUserCare = isHasRole;
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'GET', 'ALL'])
            .then(isHasRole => {
                this.canViewAllTicket = isHasRole || this.isProjectLeader();
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'VIEW', 'MARKET', 'ALL'])
        .then(isHasRole => {
            this.canViewMarketAllTicket = isHasRole;
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'REVOKE', 'ANY']).then(isHasRole => {
            this.isRevokeAny = isHasRole;
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'GET', 'ALL']).then(isHasRole => {
            this.canPropertyUnitGetAll = isHasRole;
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'GET', 'ALL', 'CUSTOMER']).then(isHasRole => {
            this.canViewAllCustomer = isHasRole || this.isProjectLeader();
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'PRIMARY', 'TRANSACTION', 'EXPORT', 'DSGD']).then(isHasRole => {
            this.canExportDSGD = isHasRole || this.isProjectLeader();
        });
        this.authorizeService.hasAuthority(['SYNC', 'ERP', 'TICKET']).then(isHasRole => {
            this.canSynErpTicket = isHasRole;
          });
        this.authorizeService.hasAuthority(['SUPER', 'ADMIN', 'UPDATE', 'TICKET']).then(isHasRole => {
            this.isSuperAdmin = isHasRole;
          });

        this.authorizeService.hasAuthority(['PROPERTY', 'DRAWN', 'UNITS', 'CREATE'])
            .then(isHasRole => {
                this.isCreateDrawn = isHasRole;
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'DRAWN', 'UNITS', 'ROLL'])
            .then(isHasRole => {
                this.isRollDrawn = isHasRole;
            });
        this.authorizeService.hasAuthority(['PROPERTY', 'TICKET', 'CREATE', 'DOCUMENTS'])
        .then(isHasRole => {
            this.canTicketCreateDocuments = isHasRole;
        });
        this.authorizeService.hasAuthority(['PROPERTY', 'TICKET', 'IMPORT' , 'ESCROW']).then(isHasRole => {
            this.canImportYCDCO = isHasRole;
        });

    }

    onOptionSelectedTab(event) {
        if (event) {
            this.selectedProject = { ...event };
            this.search();
        }
    }

    public isBookmark(value: any): boolean {
        return this.bookmarkedProjects[value.id];
    }

    public onBookmark(event: any, value: any) {
        event.stopPropagation();
        if (this.bookmarkedProjects[value.id]) {
            delete this.bookmarkedProjects[value.id];
        } else {
            this.bookmarkedProjects[value.id] = value;
        }
        this.storage.store(STORAGE_BOOKMARK_PROJECT, this.bookmarkedProjects);
    }

    public removeBoomark(value: any) {
        delete this.bookmarkedProjects[value.id];
        this.storage.store(STORAGE_BOOKMARK_PROJECT, this.bookmarkedProjects);
    }

    public changeProject(event: any, project: any) {
        this.selectedProject = project;
        this.onOptionSelectedTab(null);
    }

    private convertBlockToView() {
        return this.blockFloorRoom && this.blockFloorRoom.map((itemCanHo: any) => {
            const block = {
                floors: [],
                maxRoomsOneFloor: 0
            };
            // let arrBlock = [];
            let arrFloor = [];
            let arrPhong = [];
            arrFloor = itemCanHo.floors.map((floorName: string) => {
                block.maxRoomsOneFloor = itemCanHo.rooms.length > block.maxRoomsOneFloor ? itemCanHo.rooms.length : block.maxRoomsOneFloor;
                arrPhong = itemCanHo.rooms.map((phongName: string) => {
                    return { queryJson: `${itemCanHo.block}${floorName}${phongName}`, block: itemCanHo.block, floor: floorName, room: phongName };
                });
                return arrFloor.concat(arrPhong);
            });
            block.floors = block.floors.concat(arrFloor);
            return block;
        });
    }

    recount() {
        if (this.enableRecount) {
            this.replaceMappingTableCanHo();
            this.replaceMappingDatNen();
            this.enableRecount = false;
        }
    }

    async replaceMappingTableCanHo() {

        let lock = 0;
        let close = 0;
        let coming = 0;
        let processing = 0;
        let confirm = 0;
        let unsuccess = 0;
        let lockConfirm = 0;
        let success = 0;
        let cancel = 0;
        let transfer = 0;
        let deposit = 0;
        let moved = 0;

        this.jsonCanHo = {};

        this.propertyArrAllowMoveSalesProgram = [];
        this.lstView1 = await this.getLstView1(this.propertyArrModelFilter);
        this.propertyArrModelFilter.map(el => {
            if (this.currentTemplateCode) {
                if (this.lstView1.includes(el.view1)) {
                    if (el.primaryStatus && (this.currentTemplateCode ? this.checkCurrentTemplateCode(this.currentTemplateCode, el.block)  : true)) {
                        switch (el.primaryStatus) {
                            case 'LOCK': // Khóa đầu tư
                                lock++;
                                this.propertyArrAllowMoveSalesProgram.push(el);
                                break;
                            case 'CLOSE': // Chưa mở bán
                                if(el.liquidateType === 'TRANSFER') {
                                    transfer++
                                } else {
                                    close++;
                                }
                                this.propertyArrAllowMoveSalesProgram.push(el);
                                break;
                            case 'COMING': // Sản phẩm đang mở bán
                                coming++;
                                break;
                            case 'PROCESSING':
                                processing++;
                                break;
                            case 'CONFIRM':
                                confirm++;
                                break;
                            case 'UNSUCCESS': // Không thành công
                                unsuccess++;
                                break;
                            case 'LOCK_CONFIRM': // Thành công
                                lockConfirm++;
                                break;
                            case 'SUCCESS': // Thành công
                                if(el && el.contract && el.contract.type !== null) deposit ++;
                                else success++;
                                break;
                            case 'CANCEL': // Thanh lý
                                cancel++;
                                this.propertyArrAllowMoveSalesProgram.push(el);
                                break;
                            case 'MOVED': // Chuyển CTBH
                                moved++;
                                break;
                            default:
                                break;
                        }
                    }
                }

            } else {
                if (el.primaryStatus && (this.currentTemplateCode ? this.checkCurrentTemplateCode(this.currentTemplateCode, el.block)  : true)) {
                    switch (el.primaryStatus) {
                        case 'LOCK': // Khóa đầu tư
                            lock++;
                            this.propertyArrAllowMoveSalesProgram.push(el);
                            break;
                        case 'CLOSE': // Chưa mở bán
                            if(el.liquidateType === 'TRANSFER') {
                                transfer++
                            } else {
                                close++;
                            }
                            this.propertyArrAllowMoveSalesProgram.push(el);
                            break;
                        case 'COMING': // Sản phẩm đang mở bán
                            coming++;
                            break;
                        case 'PROCESSING':
                            processing++;
                            break;
                        case 'CONFIRM':
                            confirm++;
                            break;
                        case 'UNSUCCESS': // Không thành công
                            unsuccess++;
                            break;
                        case 'LOCK_CONFIRM': // Thành công
                            lockConfirm++;
                            break;
                        case 'SUCCESS': // Thành công
                            if(el && el.contract && el.contract.type !== null) deposit ++;
                            else success++;
                            break;
                        case 'CANCEL': // Thanh lý
                            cancel++;
                            this.propertyArrAllowMoveSalesProgram.push(el);
                            break;
                        case 'MOVED': // Chuyển CTBH
                            moved++;
                            break;
                        default:
                            break;
                    }
                }
            }
            this.jsonCanHo[`${el.block}${el.floor}${el.room}`] = el;
            return el;

        });
        this.countPrimaryStatus.lock = lock;
        this.countPrimaryStatus.close = close;
        this.countPrimaryStatus.coming = coming;
        this.countPrimaryStatus.processing = processing;
        this.countPrimaryStatus.confirm = confirm;
        this.countPrimaryStatus.unsuccess = unsuccess;
        this.countPrimaryStatus.lockConfirm = lockConfirm;
        this.countPrimaryStatus.success = success;
        this.countPrimaryStatus.cancel = cancel;
        this.countPrimaryStatus.transfer = transfer;
        this.countPrimaryStatus.deposit = deposit;
        this.countPrimaryStatus.moved = moved;

    }
    private checkCurrentTemplateCode(currentTemplateCode, block) {
        if (!currentTemplateCode) {
            return false;
        }
        return currentTemplateCode.split(`;`).includes(block);
    }
    private replaceMappingDatNen() {

        let lock = 0;
        let close = 0;
        let coming = 0;
        let processing = 0;
        let confirm = 0;
        let unsuccess = 0;
        let success = 0;
        let cancel = 0;
        let transfer = 0;
        let deposit = 0;
        let moved = 0;

        this.jsonDatNen = {};
        this.lstView1 = this.getLstView1(this.propertyArrModel);
        this.propertyArrModel.map(el => {
            if (this.currentTemplateCode) {
                if (this.lstView1.includes(el.view1)) {
                    if (el.primaryStatus && (this.currentTemplateCode ? this.checkCurrentTemplateCode(this.currentTemplateCode, el.block) : true)) {
                        switch (el.primaryStatus) {

                            case 'LOCK': // Khóa đầu tư
                                lock++;
                                break;
                            case 'CLOSE': // Sản phẩm import
                                if(el.liquidateType === 'TRANSFER') {
                                    transfer++
                                } else {
                                    close++;
                                }
                                break;
                            case 'COMING': // Sản phẩm đang mở bán
                                coming++;
                                break;
                            case 'PROCESSING': // Chờ xác nhận
                                processing++;
                                break;
                            case 'CONFIRM': // Chờ xác nhận
                                confirm++;
                                break;
                            case 'UNSUCCESS': // Không thành công
                                unsuccess++;
                                break;
                            case 'SUCCESS': // Thành công
                                if(el && el.contract && el.contract.type === 'deposit') deposit ++;
                                else success++;
                                break;
                            case 'CANCEL': // Thanh lý
                                cancel++;
                                break;
                            case 'MOVED': // Chuyển CTBH
                                moved++;
                                break;
                            default:
                                break;
                        }
                    }
                }
            } else {
                if (el.primaryStatus && (this.currentTemplateCode ? this.checkCurrentTemplateCode(this.currentTemplateCode, el.block) : true)) {
                    switch (el.primaryStatus) {

                        case 'LOCK': // Khóa đầu tư
                            lock++;
                            break;
                        case 'CLOSE': // Sản phẩm import
                            if(el.liquidateType === 'TRANSFER') {
                                transfer++
                            } else {
                                close++;
                            }
                            break;
                        case 'COMING': // Sản phẩm đang mở bán
                            coming++;
                            break;
                        case 'PROCESSING': // Chờ xác nhận
                            processing++;
                            break;
                        case 'CONFIRM': // Chờ xác nhận
                            confirm++;
                            break;
                        case 'UNSUCCESS': // Không thành công
                            unsuccess++;
                            break;
                        case 'SUCCESS': // Thành công
                            if(el && el.contract && el.contract.type === 'deposit') deposit ++;
                            else success++;
                            break;
                        case 'CANCEL': // Thanh lý
                            cancel++;
                            break;
                        case 'MOVED': // Chuyển CTBH
                            moved++;
                            break;
                        default:
                            break;
                    }
                }
            }


            this.jsonDatNen[`${el.quarter}${el.lot}${el.room}`] = el;

            return el;
        });
        this.countPrimaryStatus.lock = lock;
        this.countPrimaryStatus.close = close;
        this.countPrimaryStatus.coming = coming;
        this.countPrimaryStatus.processing = processing;
        this.countPrimaryStatus.confirm = confirm;
        this.countPrimaryStatus.unsuccess = unsuccess;
        this.countPrimaryStatus.success = success;
        this.countPrimaryStatus.cancel = cancel;
        this.countPrimaryStatus.transfer = transfer;
        this.countPrimaryStatus.deposit = deposit;
        this.countPrimaryStatus.moved = moved;


    }

    async getTableData(isChangePos = false, params = {}) {
        if (!this.currentSalesProgram.id) {
            return;
        }
        this.isTableLoading$.next(true);
        this.propertyArrModel = [];
        this.propertyArrModelFilter = [];
        this.propertyOfPriorityFilter = [];
        this.listExtendUnit = [];
        params = Object.assign({
            categoryId: this.categoryId,
            projectId: this.selectedProject ? this.selectedProject.id : this.projectId,
            priority: this.filterBangHang.priority,
            primaryStatus: this.filterBangHang.primaryStatus,
            liquidateType: this.filterBangHang.liquidateType,
            isContract: this.filterBangHang.isContract,
            posId: isChangePos ? this.selectPos.toString() || '' : this.filterBangHang.posId,
            _fields: `id,project.name,project.id,pos.name,pos.id,code,apartmentType,areaWide,areaLong,primaryStatus,liquidateType,contract,contractType,shortCode,attributes.attributeName,block,floor,
                attributes.value,bedroom,direction,priceAbove,price,housePriceVat,landPriceVat,housePrice,landPrice,area,priorities,extendable,extendPos,reasonRevoke,modifiedDate,priceVat,category.id,
                stage,lstImage`
        }, params);
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            params["salesProgramIds"] = this.lstSaleProgramId;
        } else {
            params["salesProgramId"] = this.currentSalesProgram.id;
        }
        if (this.selectPoses && this.selectPoses.length > 0) {
            if (!params["poses"]) {
                params["poses"] = this.selectPoses;
            }
        }

        await this.projectService.getTable(params).pipe(
            finalize(() => this.isTableLoading$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
            if (this.unitTable) {
                this.unitTable['refeshUI'] = true;
                this.isRefeshUnitTable = true;
            }
            this.propertyArrModel = res.map(el => new PropertyTable(el, this.marketPos));
            if(!isChangePos) {
                let data = this.propertyArrModel;
                let dictionary = Object.assign({}, ...data.map((item) => ({[item.posId]: item.posId})));
                if(this.canPropertyUnitGetAll){
                    this.posFilter = this.pos;
                } else {
                    this.posFilter = this.pos.filter(item => dictionary.hasOwnProperty(item.id));
                }

            }
            if (this.propertyArrModel.some(x => x.extendable)) {
                this.showLockExtendPriority = true;
                this.viewTableModeValue = 2;
            } else {
                this.showLockExtendPriority = false;
            }
            this.onChangeKeySearchBH(this.filterBangHang.keySearch);
            if(this.canViewMarketAllTicket){
                this.viewTableModeValue = 1;
            }

            if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
                this.nameSaleProgram = 'Bảng hàng tổng';
                if (this.selectSaleProgramApply && this.selectSaleProgramApply.length > 0) {
                    this.selectSaleProgramApply.forEach((item: any) => {
                        this.salesProgram.forEach((saleProgram: any) => {
                            if (item === saleProgram.id) {
                                saleProgram.checked = true;
                                return saleProgram;
                            }
                        })
                    })
                    this.countSalesProgramChoose = this.selectSaleProgramApply.length;
                }
            } else if (this.currentSalesProgram) {
                this.nameSaleProgram = this.currentSalesProgram.name;
            }
            if (!this.saleProgramDefault.id) {
                this.saleProgramDefault = this.currentSalesProgram;
            }
            // check lst sale programs
            if (this.salesProgram && this.salesProgram.length > 0) {
                const checkItemChecked = (i: any) => i.checked && i.checked === true;
                if (!this.salesProgram.some(checkItemChecked)) {
                    if (this.currentSalesProgram) {
                        this.salesProgram.map((e: any) => {
                            if (e.id === this.currentSalesProgram.id) {
                                e.checked = true;
                            } else {
                                e.checked = false;
                            }
                        });
                    } else {
                        this.salesProgram[0].checked = true;
                    }
                    this.countSalesProgramChoose = 1;
                }
            }
        });
    }

    getLstTableData(lstId: any[], isChangePos = false, params = {}) {
        if (!this.currentSalesProgram.id) {
            return;
        }
        let ids: any = '';
        ids = this.convertSalesprogram(lstId);

        this.isTableLoading$.next(true);
        this.propertyArrModel = [];
        this.propertyArrModelFilter = [];
        this.propertyOfPriorityFilter = [];
        this.listExtendUnit = [];

        params = Object.assign({
            salesProgramIds: ids,
            categoryId: this.categoryId,
            projectId: this.selectedProject ? this.selectedProject.id : this.projectId,
            priority: this.filterBangHang.priority,
            primaryStatus: this.filterBangHang.primaryStatus,
            liquidateType: this.filterBangHang.liquidateType,
            isContract: this.filterBangHang.isContract,
            posId: isChangePos ? this.selectPos.toString() || '' : this.filterBangHang.posId,
            _fields: `id,project.name,project.id,pos.name,pos.id,code,apartmentType,areaWide,areaLong,primaryStatus,liquidateType,contract,contractType,shortCode,attributes.attributeName,block,floor,
                attributes.value,bedroom,direction,priceAbove,price,housePriceVat,landPriceVat,housePrice,landPrice,area,priorities,extendable,extendPos,reasonRevoke,modifiedDate,priceVat,category.id,
                stage,lstImage`
        }, params);
        this.projectService.getTable(params).pipe(
            finalize(() => this.isTableLoading$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
            if (this.unitTable) {
                this.unitTable['refeshUI'] = true;
                this.isRefeshUnitTable = true;
            }
            this.propertyArrModel = res.map(el => new PropertyTable(el, this.marketPos));
            if(!isChangePos) {
                let data = this.propertyArrModel;
                let dictionary = Object.assign({}, ...data.map((item) => ({[item.posId]: item.posId})));
                if(this.canPropertyUnitGetAll){
                    this.posFilter = this.pos;
                } else {
                    this.posFilter = this.pos.filter(item => dictionary.hasOwnProperty(item.id));
                }
            }
            if (this.propertyArrModel.some(x => x.extendable)) {
                this.showLockExtendPriority = true;
                this.viewTableModeValue = 2;
            } else {
                this.showLockExtendPriority = false;
            }
            this.onChangeKeySearchBH(this.filterBangHang.keySearch);
            if(this.canViewMarketAllTicket){
                this.viewTableModeValue = 1;
            }
        });
    }

    getLstView1(res: any) {
        this.lstView1 = [];
        res.forEach((item: any) => {
            if (item.view1) {
                let htmlItem: any = document.getElementById(`${item.view1}`);
                if (htmlItem) {
                    let data = htmlItem.getAttribute('data-id');
                    if (data && !this.lstView1.includes(item.view1)) {
                        this.lstView1.push(item.view1);
                    }
                }
            }
        });
        return this.lstView1;
    }

    convertSalesprogram(lstId: any[]) {
        let ids: any = '';
        ids = lstId.join();
        if (lstId.length > 1) {
            this.lstSaleProgramId = ids;
            this.nameSaleProgram = 'Bảng hàng tổng'
        } else {
            this.lstSaleProgramId = '';
        }

        return ids;
    }

    setupMappingTable(propertyArrModel: PropertyTable[]) {
        propertyArrModel.sort((a, b) => {
            let codeA = a.code.toUpperCase();
            let codeB = b.code.toUpperCase();
            if (codeA < codeB) {
                return -1;
            } else {
                return 1;
            }
        });
        if (this.extensionPriorityFilter) {
            this.propertyArrModelFilter = [...propertyArrModel.filter(x => x.extendable)];
        } else {
            this.propertyArrModelFilter = [...propertyArrModel];
        }
        if (this.selectedPriorities.length > 0) {
            this.selectedPriorities.forEach(pr => {
                const priority = parseInt(pr) - 1;
                if (priority > -1) {
                    this.propertyArrModelFilter = this.propertyArrModelFilter.filter(x => x.priorities[priority].customerName !== '');
                } else {
                    this.propertyArrModelFilter = this.propertyArrModelFilter.filter(x => !x.priorities.some(p => p.customerName !== ''));
                }
            });
        }
        this.propertyOfPriorityFilter = this.propertyArrModelFilter.filter(x => x.posId !== this.rootPos && ( this.currentTemplateCode ? x.block === this.currentTemplateCode : true ));

        if (this.unitTable && this.isRefeshUnitTable) {
            this.isRefeshUnitTable = false;
            this.unitTable['changedRowCodes'] = this.propertyArrModel.map(el => el.view1);
        }
        this._countPriorities();
        if (this.categoryId === CategoryEnum.CAN_HO) {
            this.replaceMappingTableCanHo();
        } else if (this.categoryId === CategoryEnum.DAT_NEN) {
            this.replaceMappingDatNen();
        }

    }

    getTableDataHistory() {
        this.isTableLoading$.next(true);
        this.saleList = [];
        this.saleList2 = [];
        const params = {
            projectId: this.selectedProject.id,
        };

        forkJoin(
            this.projectPrimaryTransactionService.getTableHistory({ ...params, stage: 1 }),
            this.projectPrimaryTransactionService.getTableHistory({ ...params, stage: 2 })
        ).pipe(
            finalize(() => this.isTableLoading$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
            this.saleList = res[0].map(i => new PropertyTable(i));
            this.saleList2 = res[1].map(i => new PropertyTable(i));
            this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);
        });
    }

    getSaleListData() {
        if (!this.currentSalesProgram.id && this.selectSaleProgramApply.length <= 1) {
            return;
        }
        this.isTableLoading$.next(true);
        this.saleList = [];
        this.saleList2 = [];
        const params = this.currentFilter;
        if (this.selectSaleProgramApply.length <= 1) {
            params.salesProgramId = this.currentSalesProgram.id;
        }
        else {
            params.salesProgramId = this.selectSaleProgramApply;
        }
        params.projectId = this.selectedProject.id;
        params.sort = this.transformCurrentSort();
        params._fields = `id,apartmentType,areaWide,areaLong,primaryStatus,bedroom,direction,priceAbove,price,area,extendable,code,block,floor,attributes.attributeName,attributes.id,attributes.value,
    project.name,project.id,priceVat,description,registeredDate,pos.name,pos.id,extendPos,priorities,isException,exceptionalReason,registeredQueue,registeredPos,stage,modifiedDate,salesProgram.id,proposalApproved`;
        params._fields_attributes = AttributeEnum.view1 + ',' + AttributeEnum.block + "," + AttributeEnum.tang + "," + AttributeEnum.loai_can_ho + ","
        AttributeEnum.lo_dat + "," + AttributeEnum.khu_dat;
        forkJoin(
            this.propertyService.getUnitTransaction({ ...params, stage: 1 }),
            this.propertyService.getUnitTransaction({ ...params, stage: 2 })
        ).pipe(
            finalize(() => this.isTableLoading$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
            this.saleList = res[0].map(i => getSaleItemWithSortValue(new PropertyTable(i), this.user, this.priority));
            this.saleList = this.saleList.sort(sortCode);
            this.saleList2 = res[1].map(i => getSaleItemWithSortValue(new PropertyTable(i), this.user));
            this.saleList2 = this.saleList2.sort(sortCode);
            this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);

        });

    }

    private _countStatusSaleList(saleList: PropertyTable[]) {
        let lock = 0;
        let close = 0;
        let coming = 0;
        let processing = 0;
        let confirm = 0;
        let unsuccess = 0;
        let success = 0;
        let lockConfirm = 0;
        let cancel = 0;
        let deposit = 0;
        let transfer = 0;
        let moved = 0;
        const countAllSuccess = this.isProjectLeader() || this.user.hasPermission(PropertyUnitPermissionConst.GetAllPropertyUnit) ||
            this.user.hasPermission(PropertyUnitPermissionConst.GetAllSaleList) || this.user.hasPermission(PropertyUnitPermissionConst.PropertyPrimaryGetByPos);
        saleList.map((el: PropertyTable) => {
            if (el.primaryStatus) {
                switch (el.primaryStatus) {
                    case PropertyUnitStatus.LOCK: // Sản phẩm đang mở bán
                        lock++;
                        break;
                    case PropertyUnitStatus.CLOSE: // Sản phẩm đang mở bán
                        if(el.liquidateType === 'TRANSFER') {
                            transfer++
                        } else {
                            close++;
                        }
                        break;
                    case PropertyUnitStatus.COMING: // Sản phẩm đang mở bán
                        coming++;
                        break;
                    case PropertyUnitStatus.PROCESSING:
                        processing++;
                        break;
                    case PropertyUnitStatus.CONFIRM:
                        confirm++;
                        break;
                    case PropertyUnitStatus.UNSUCCESS: // Không thành công
                        unsuccess++;
                        break;
                    case PropertyUnitStatus.LOCK_CONFIRM: {// Thành công, chờ bổ sung HS
                        if (countAllSuccess || this.user.isPOSMatched(getPropUnitTartgetTicket(el, -1).posId)) {
                            lockConfirm++;
                        }
                    }
                        break;
                    case PropertyUnitStatus.SUCCESS: { // Thành công
                        if (countAllSuccess || this.user.isPOSMatched(getPropUnitTartgetTicket(el, -1).posId)) {
                            if(el && el.contract && el.contract.type === 'deposit') deposit ++;
                            else success++;
                        }
                    }
                        break;
                    case PropertyUnitStatus.MOVED: // Chuyển CTBH
                        moved++;
                        break;
                    case PropertyUnitStatus.CANCEL: // Chuyển CTBH
                        cancel++;
                        break;
                    default:
                        break;
                }
            }
        });


        return { lock, close, transfer, coming, processing, confirm, unsuccess, lockConfirm, success, deposit , cancel, moved };
    }

    getArea(area) {
        return area ? Math.round(area * 10) / 10 : 0;
    }
    getPrice(price) {
        return price || price > 0 ? Math.round((price / 1000000)) : '';
    }

    getOpacity(item) {
        if (item && ((item.liquidateType === LiquidateType.TRANSFER && item.primaryStatus === PropertyUnitStatus.CLOSE) ||
         [PropertyUnitStatus.CANCEL, PropertyUnitStatus.MOVED].includes(item.primaryStatus) )) {
            return 1;
        }
        if (item && item.posId && item.posId == this.recoveryUnit) {
            return '0.5';
        }
        return 1;
    }

    getTicket(params?) {
        this.isTableLoading$.next(true);
        let p = params ? params : {};
        p.page = this.pagination.page;
        p.pageSize = this.pagination.pageSize;
        p.sort = this.transformCurrentSort();
        if (!p._fields) {
            p._fields = this.primaryTransactionProjection;
        }
        if (this.selectedProject) {
            this.primaryTransactionService.getTicket(this.selectedProject.id, p).pipe(
                finalize(() => this.isTableLoading$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe((res: any) => {
                this.summaryData(res);
                if (res.sumStatus) {
                    this._countTicketByStatus(res.sumStatus);
                }
            });
        }
    }
    private _countTicketByStatus(sumStatus) {
        let bookingApproved = 0;
        let priorityUpdated = 0;
        let cancelled = 0;
        let processing = 0;
        sumStatus.map((el: any) => {
            if (el._id) {
                switch (el._id) {
                    case PrimaryTransactionStatus.BOOKING_APPROVED:
                        bookingApproved += el.count;
                        break;
                    case PrimaryTransactionStatus.PRIORITY_UPDATED:
                        priorityUpdated += el.count;
                        break;
                    // case PrimaryTransactionStatus.TICKET_CANCELLED:
                    // case PrimaryTransactionStatus.CS_APPROVED_CANCEL_REQUESTED:
                    //   cancelled += el.count;
                    //   break;
                    default:
                        processing += el.count;
                        break;
                }
            }
        });
        this.countTicketStatus = { bookingApproved, priorityUpdated, cancelled, processing };
    }
    async openGridPopup(arr) {
        this.isTableLoading$.next(true);
        const resData: any = await PropertyPrimaryExcel.getData(this.service, arr, this.selectedProject.id, this.selectedProject.name, this.categories[this.selectedTab]);
        if (!resData.properties) {
            this.toastr.error('Lỗi!', resData.message);
            this.isTableLoading$.next(false);
            return;
        }

        const result = await PropertyPrimaryExcel.importProperties(this.service, resData);
        if (result.code !== 0) {
            this.getTableData();
            this.toastr.error('Lỗi!', result.message);
            this.isTableLoading$.next(false);
        } else {
            this.getTableData();
            this.toastr.success('Tải tất cả sản phẩm thành công', 'Thành công!');
            this.isTableLoading$.next(false);
        }
    }

    changeTab(event) {
        this.isEditCustomerInfo = (event === 4 && this.isEnabledEdit);
        this.onFirstDataRendered(event);
        this.onFirstDataRenderedYCDCO(event);
        this.selectedTab = (!this.canDownloadPrimary && event < this.categories.length) ? (this.categories.length + 1) : event;

        this._updateRoute(event);

        this.filterBangHang = <any>{};
        this.filterDSGD = <any>{};

        // reset sort
        this.currentSort = {
            propertyName: 'createdDate', // property Name
            sortOrder: true, //  false Asc, true Desc
        };
        // reset page
        this.pagination.page = 1;

        //this.uncheckFilter();

        if (event === 0) {
            this.getTableData();
        } else if (event === 1) {
            this.getTicket({ ticketType: 'YCTV' });
        } else if (event === 2) {
            this.getTicket({ ticketType: 'YCDCH' });
            this.dvbh = null;
            this.searchStatus = null;
        } else if (event === 3) {
            this.getTicket({ ticketType: 'YCDC' });
            this.dvbh = null;
            this.searchStatus = null;
        } else if (event === 4) {
            this.forceUpdate1 = true;
            this.forceUpdate2 = true;
            const unitCode = this.route.queryParams['_value']['unitCode'];
            if(unitCode) {
                this.filterDSGD.keySearch = unitCode;
            }
            this.getDataTable();
        } else if (event === 6) {
            this.isRefresh = !this.isRefresh;
        }

        this.closeDialog();
    }

    getDataTable() {
        if (this.isViewAllChecked) {
            this.getTableDataHistory();
        } else {
            this.getEventProject();
            this.getSaleListData();
        }
    }

    closeDialog() {
        this.sidenav.close();
        this.sidenav.closeTicketDetail();
        if (this.project) {
            this.sidenav.closePropertyTicket();
        }
    }

    onTicketCreated() {
        if (this.project) {
            this.sidenav.closePropertyTicket();
        }
        switch (this.selectedTab) {
            case 1:
                this.getTicket({ ticketType: 'YCTV' });
                break;
            case 2:
                this.getTicket({ ticketType: 'YCDCH' });
                break;
            case 3:
                this.getTicket({ ticketType: 'YCDC' });
                break;
            default:
                this.getTicket({ ticketType: 'YCTV' });
                break;
        }
    }

    public getTicketQuery() {
        const ticketType = this.selectedTab === 2 ? 'YCDCH' : 'YCDC';
        this.pagination.page = 1;
        return {
            exchangeId: this.exchangeId,
            ticketType: ticketType,
            keywords: this.searchKey.value ? this.searchKey.value.trim() : '',
            status: this.searchStatus,
        }
    }
    onApplyYCDC($event) {
        this.exchangeId = this.dvbh;
        this.getTicket(this.getTicketQuery());
    }
    onApplyStatus($event) {
        if ($event) {
            this.searchStatus = $event.id
        }
        else {
            this.searchStatus = '';
        }
        this.getTicket(this.getTicketQuery());
    }

    isProjectLeader() {
        return (this.project && this.project.projectLeader && this.project.projectLeader.id === this.user.id) || false;
    }
    isF1San() {
        return (this.user.f1San && (this.user.f1San.id === this.user.pos.id || this.user.f1San.id === this.user.pos.parentId)) || false;
    }

    isInSaleList() {
        const setting = this.project ? this.project.setting : null;
        const saleList = setting ? setting.salesUnit : [];
        if (saleList.length > 0) {
            return saleList.some(item => item.id === this.user.pos.id || item.id === this.user.pos.parentId ||
                (this.user.f1San && item.id === this.user.f1San.id)
            );
        }
        return false;
    }
    downloadBKCT() {
        let ticketType = '';
        switch (this.selectedTab) {
            case 1:
                ticketType = 'YCTV';
                break;
            case 2:
                ticketType = 'YCDCH';
                break;
            case 3:
                ticketType = 'YCDC';
                break;
            default:
                break;
        }
        const url = this.getTemplateFileByType(`BANG_KE_CHUYEN_TIEN_${ticketType}`);
        const url2 = this.getTemplateFileByType(`BANG_KE_CHUYEN_TIEN_${ticketType}_DXS`);
        if (url || url2) {
            this.isDownloadLoadingAll$.next(true);
            const body = {
                projectId: this.selectedProject.id,
                download: 1,
                ticketType,
                exchangeId: this.exchangeId,
                templateFileName: `BANG_KE_CHUYEN_TIEN_${ticketType}`,
                status: [PrimaryTransactionStatus.ADMIN_APPROVED_TICKET, PrimaryTransactionStatus.POS_CONFIRM_LOCK]
            }
            // const fileName = 'Bang_ke_chuyen_tien_' + ticketType + '_' + getCurrentTimeSigned();
            this.primaryTransactionService.getTicketDowload(body).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
            });
        } else {
            this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
        }
    }
    downloadRefund() {
        let ticketType = '';
        switch (this.selectedTab) {
            case 1:
                ticketType = 'YCTV';
                break;
            case 2:
                ticketType = 'YCDCH';
                break;
            case 3:
                ticketType = 'YCDC';
                break;
            default:
                break;
        }
        const url = this.getTemplateFileByType(`BANG_KE_HOAN_TIEN_${ticketType}`);
        const url2 = this.getTemplateFileByType(`BANG_KE_HOAN_TIEN_${ticketType}_DXS`);
        if (url || url2) {
            this.isDownloadLoadingAll$.next(true);
            const body = {
                projectId: this.selectedProject.id,
                download: 1,
                ticketType,
                exchangeId: this.exchangeId,
                templateFileName: `BANG_KE_HOAN_TIEN_${ticketType}`,
                status: [PrimaryTransactionStatus.ADMIN_APPROVED_CANCEL_REQUESTED]
            }
            // const fileName = 'Bang_ke_hoan_tien_' + ticketType + '_' + getCurrentTimeSigned();
            this.primaryTransactionService.getTicketDowload(body).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
            });
        } else {
            this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
        }
    }
    downloadTTTV() {
        const url = this.getTemplateFileByType('BANG_KE_TTTV');
        const url2 = this.getTemplateFileByType('BANG_KE_TTTV_DXS');
        const projectRVN = this.selectedProject.code === 'RVN' ? '-rvn' : '';
        if (url || url2) {
            this.isDownloadLoadingAll$.next(true);
            let salesProgramId: any;
            if (this.selectSaleProgramApply.length > 1) {
                salesProgramId = this.selectSaleProgramApply;
            }
            else {
                salesProgramId = this.currentSalesProgram.id;
            }
            const body = {
                projectId: this.selectedProject.id,
                download: 1,
                salesProgramId: salesProgramId,
                exchangeId: this.exchangeId,
                url,
                status: [PrimaryTransactionStatus.SUCCESS]
            }
            // const fileName = 'Bang_ke_thoa_thuan_TV_' + '_' + getCurrentTimeSigned();
            this.primaryTransactionService.getTicketDowloadTTTV(body, null, projectRVN).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
            });
        } else {
            this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
        }
    }
    downloadTTTVCam() {
        const url = this.getTemplateFileByType('BANG_KE_TTTV');
        const url2 = this.getTemplateFileByType('BANG_KE_TTTV_DXS');
        const projectRVN = this.selectedProject.code === 'RVN' ? '-rvn' : '';
        if (url || url2) {
            this.isDownloadLoadingAll$.next(true);
            let salesProgramId: any;
            if (this.selectSaleProgramApply.length > 1) {
                salesProgramId = this.selectSaleProgramApply;
            }
            else {
                salesProgramId = this.currentSalesProgram.id;
            }
            const body = {
                projectId: this.selectedProject.id,
                download: 1,
                salesProgramId: salesProgramId,
                exchangeId: this.exchangeId,
                url,
                status: [PrimaryTransactionStatus.LOCK_CONFIRM]
            }
            // const fileName = 'Bang_ke_thoa_thuan_DB_' + '_' + getCurrentTimeSigned();
            this.primaryTransactionService.getTicketDowloadTTTV(body, null, projectRVN).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
            });
        } else {
            this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
        }
    }
    downloadChuyenCocERP() {
        const url = this.getTemplateFileByType('BANG_KE_CHUYEN_COC_ERP');
        if (url) {
            this.isDownloadLoadingAll$.next(true);
            let salesProgramId: any;
            if (this.selectSaleProgramApply.length > 1) {
                salesProgramId = this.selectSaleProgramApply;
            }
            else {
                salesProgramId = this.currentSalesProgram.id;
            }
            const body = {
                projectId: this.selectedProject.id,
                download: 1,
                salesProgramId: salesProgramId,
                url,
                status: [PrimaryTransactionStatus.SUCCESS, PrimaryTransactionStatus.LOCK_CONFIRM]
            }
            // const fileName = 'Bang_ke_chuyen_coc_' + getCurrentTimeSigned();
            this.primaryTransactionService.getTicketDowloadChuyenCoc(body).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
            });
        } else {
            this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
        }
    }

    downloadAll() {
        let ticketSale = new TicketSale();
        let sheets = [];
        let ticketType = '';
        switch (this.selectedTab) {
            case 1:
                ticketType = 'YCTV';
                break;
            case 2:
                ticketType = 'YCDCH';
                break;
            case 3:
                ticketType = 'YCDC';
                break;
            default:
                break;
        }
        const fileName = 'Danh_sach_' + ticketType + '_' + new Date().getTime();

        this.isDownloadLoadingAll$.next(true);
        const url = this.getTemplateFileByType(`BANG_KE_${ticketType}`);
        if (url) {
            this.primaryTransactionService
            .downloadYC(
                this.selectedProject.id,
                ticketType,
                this.exchangeId
            )
            .pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            )
            .subscribe((res) => {
                console.log("success");
            });
        } else {
            this.primaryTransactionService.getTicketDowloadAll(this.selectedProject.id, ticketType, this.exchangeId).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
                sheets.push({
                    headers: ticketSale.getHeaders(),
                    hideRows: ticketSale.getHideRows(),
                    data: res.map((item: any, idx) => {
                        item.stt = (idx + 1);
                        const obj: any = new TicketSale(item);
                        let surveyString = '';
                        if (this.selectedProject.surveys) {
                            const surveys = this.selectedProject.surveys.filter(i => obj.surveys && obj.surveys.includes(i.code));
                            surveys.forEach(s => {
                                surveyString = surveyString ? surveyString + '\n' + `${s.name}-${s.value}` : `${s.name}-${s.value}`;
                            });
                        }
                        obj.surveyString = surveyString;
                        obj.note = obj.note || '';

                        return obj;
                    }),
                    sheetName: 'Danh sách ' + ticketType
                });
                if (ticketType === 'YCDCH') {
                    const data = res.map((item: any, idx) => {
                        let obj: any = {};
                        obj.stt = (idx + 1);
                        obj = new TicketSale(item);
                        obj.amount1 = obj.amount;
                        obj.amount2 = 0; // hard code
                        obj.total = obj.amount1 + obj.amount2;
                        return obj;
                    });
                    sheets.push({
                        headers: [
                            { header: 'STT', key: 'stt', width: 14 },
                            { header: 'TÊN KH', key: 'customerName', width: 24 },
                            { header: 'CMND', key: 'identity', width: 16 },
                            { header: 'NGÀY CẤP', key: 'identityIssueDate', width: 16 },
                            { header: 'NƠI CẤP', key: 'identityIssueLocation', width: 18 },
                            { header: 'DỰ ÁN', key: 'projectCode', width: 18 },
                            { header: 'SỐ PHIẾU THU', key: 'recieptCode', width: 17 },
                            { header: 'MÃ PHIẾU', key: 'bookingTicketCode', width: 17 },
                            { header: 'SỐ TIỀN ĐĂNG KÝ NGUYỆN VỌNG', key: 'amount1', width: 18, style: { numFmt: '#,##0' } },
                            { header: 'SỐ TIỀN ĐĂNG KÝ NGUYỆN VỌNG', key: 'amount2', width: 18, style: { numFmt: '#,##0' } },
                            { header: 'TỔNG CỘNG', key: 'total', width: 20, style: { numFmt: '#,##0' } },
                            { header: 'ĐƠN VỊ TƯ VẤN', key: 'posName', width: 19 },
                            { header: 'HỌ TÊN NHÂN VIÊN TƯ VẤN', key: 'employeeTakeCareName', width: 24 },
                        ],
                        data: data,
                        mergeCells: ['A7:A8', 'B7:B8', 'C7:C8', 'D7:D8', 'E7:E8', 'F7:F8', 'G7:G8', 'H7:H8', 'K7:K8', 'L7:L8', 'M7:M8', 'I7:J7'],
                        numberHeader: 8,
                        sheetName: 'BẢNG KÊ',
                        total1: data.reduce(function (a, b) {
                            return a + b.amount1;
                        }, 0),
                        total2: data.reduce(function (a, b) {
                            return a + b.amount2;
                        }, 0)
                    });
                }
                this.excelService.exportAsExcelFileCustom(sheets, fileName, ExportTypeEnum.BANG_KE);
            });
        }
    }

    downloadTemplate(url, outFile) {
        this.isTableLoading$.next(true);
        this.isDownloadLoadingAll$.next(true);
        this.projectService.settingSalesUnit(this.selectedProject.id, { onlySanF1: true })
            .pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false))
            )
            .subscribe(res => {
                const posNames = res.map(pos => pos.name);
                const ownerNames = this.owners.map(owner => owner.name) || [''];
                const sheetNames = ['Template', 'Config'];
                let dataValidations = [];
                let collections = [];
                switch (this.categories[this.selectedTab]) {
                    case CategoryEnum.CAN_HO:
                        dataValidations = [
                            { cell: 'G3', formula: ['Config!$B$3:$B$4'] }, // tinh trang
                            { cell: 'J3', formula: ['Config!$A$3:$A$10'] }, // huong
                            { cell: 'L3', formula: ['Config!$D$3:$D$4'] }, // goc
                            { cell: 'V3', formula: ['Config!$F$3:$F$' + (3 + posNames.length).toString()] }, // posname
                            { cell: 'X3', formula: ['Config!$G$3:$G$' + (3 + ownerNames.length).toString()] }, // owners
                            { cell: 'AC3', formula: ['Config!$H$3:$H$10'] },
                        ];
                        collections = [
                            { cell: 'F3', list: posNames }, // DVBH
                            { cell: 'G3', list: ownerNames }, // Nguồn
                            { cell: 'E3', list: [CategoryEnum.CAN_HO] }, // categoryId
                        ];
                        this.excelService.downloadTemplate(url, outFile, sheetNames, dataValidations, collections)
                            .then((res) => {
                                this.isTableLoading$.next(false);
                            });
                        break;
                    case CategoryEnum.DAT_NEN:
                        dataValidations = [
                            { cell: 'G3', formula: ['Config!$B$3:$B$4'] }, // tinh trang
                            { cell: 'H3', formula: ['Config!$A$3:$A$10'] }, // huong
                            { cell: 'K3', formula: ['Config!$D$3:$D$4'] }, // goc
                            { cell: 'U3', formula: ['Config!$F$3:$F$' + (3 + posNames.length).toString()] }, // posname
                            { cell: 'W3', formula: ['Config!$G$3:$G$10'] },
                        ];
                        collections = [
                            { cell: 'F3', list: posNames },// posName
                            { cell: 'E3', list: [CategoryEnum.DAT_NEN] },// categoryId
                        ];
                        this.excelService.downloadTemplate(url, outFile, sheetNames, dataValidations, collections).then(res => {
                            this.isTableLoading$.next(false);
                        });
                        break;
                    default:
                        break;
                }
            });
    }

    downloadTransferTemplate(url, outFile) {
        this.isTableLoading$.next(true);
        this.isDownloadLoadingAll$.next(true);
        this.projectService.settingSalesUnit(this.selectedProject.id, { onlySanF1: true })
            .pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false))
            )
            .subscribe(res => {
                const posNames = res.map(pos => pos.name);
                const sheetNames = ['Template', 'Config'];
                const dataValidations = [
                    { cell: 'C3', formula: ['Config!$F$3:$F$' + (3 + posNames.length).toString()] }, // posname
                    { cell: 'D3', formula: ['Config!$B$3:$B$6'] }, // primaryStatus
                ];
                const collections = [
                    { cell: 'F3', list: posNames },// posName
                    { cell: 'E3', list: [this.categories[this.selectedTab]] },// categoryId
                ];
                this.excelService.downloadTemplate(url, outFile, sheetNames, dataValidations, collections)
                    .then(res => {
                        this.isTableLoading$.next(false);
                    });
            });
    }

    openPopup(prop: PropertyTable, hidePriorities = false, item = null) {
        if (prop) {
            let isRevoke;
            if (this.rootPos == prop.posId) {
                isRevoke = true;
            }
            else {
                isRevoke = false;
            }
            const pro = prop instanceof PropertyTable ? prop : new PropertyTable(prop);
            const createGroupPopup = this.dialog.open(PopupPropertyDetail, {
                width: '500px',
                data: {
                    projectId: this.projectId,
                    unitId: prop.id,
                    projectLocked: this.currentSalesProgram.lock,
                    projectLockedPoint: this.currentSalesProgram.lockedPoint,
                    projectStage: this.projectStage,
                    isPriority: this.canImportSaleList,
                    isDVKH: this.isHasDVKH,
                    hasPriority: pro.hasPriority,
                    canMatchUnit: this.canMatchUnit,
                    hidePriorities: hidePriorities,
                    item: item,
                    isProjectLeader: this.isProjectLeader(),
                    user: this.user,
                    isRevoke: isRevoke,
                    isImportPrimary: this.canImportPrimary,
                    canRevokeUnit: this.canRevokeUnit,
                    canMovePosUnit: this.canMovePosUnit,
                    canLiquidateUnit: this.canLiquidateUnit,
                    canLiquidateUnitAny: this.canLiquidateUnitAny,
                    canMatchAllUnit: this.canMatchAllUnit,
                    priority: this.priority,
                    canPropertyUnitGetAll: this.canPropertyUnitGetAll,
                    unitConfig: this.project.unitConfig || []
                },
                disableClose: true,
                panelClass: 'no-padding-dialog-container'
            });
            createGroupPopup.afterClosed().subscribe((result: any) => {
                if (result && result.code) {
                    if (result.movePos) {
                        this.openTransferDialog(false, true, false, false , result.code);
                    } else if(result.revoke) {
                        this.openTransferDialog(true, false, false, false, result.code);
                    } else if(result.liquidate) {
                        this.popupLiquidate(prop);
                    } else if (result.moveSalesProgram) {
                        this.openTransferSaleProgramDialog(result.code);
                    }
                }
            });
        }
    }
    popupLiquidate(unit) {

        const dialogConfirmRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: 'THANH LÝ',
                isReason: true,
                isRequireReason: true,
                textCancel: 'Hủy',
                textOk: 'Thanh lý',
                message: `Bạn có chắc chắn muốn thanh lý sản phẩm <b>${unit.code}</b>?`,
                listRadio: this.RadioLiquidateList
            }
        });
        dialogConfirmRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                const body = {
                    projectId: unit.projectId,
                    propertyId: unit.id,
                    liquidateType: result.radioSelect,
                    reason: result.reason
                }
                this.projectPrimaryTransactionService.liquidateProperty(body).subscribe(res => {
                    this.toastr.success('Cập nhật thanh lý thành công', 'Thành công');
                });
            }
        });
    }
    onPopupDSGD(event: SaleList) {
        this.openPopup(event.propertyUnit, true, event);
    }

    openCreateTicket(ticket = null, isCopy = false, isTransferEscrow = false, isSuperAdmin = false) {
        let ticketType: string;
        if (isTransferEscrow) {
            ticketType = 'YCDC';
        } else {
            switch (this.selectedTab) {
                case 1:
                    ticketType = 'YCTV';
                    break;
                case 2:
                    ticketType = 'YCDCH';
                    break;
                case 3:
                    ticketType = 'YCDC';
                    break;
                case 4:
                    ticketType = 'YCDC';
                    break;
                default:
                    ticketType = 'YCTV';
                    break;
            }
        }
        const copyTicket = { ...ticket };
        if (isCopy) {
            delete copyTicket.bookingTicketCode;
            delete copyTicket.escrowTicketCode;
        }
        const createGroupPopup = this.dialog.open(EventSalePopupPropertyTicketComponent, {
            width: '900px',
            data: {
                ticket: isCopy ? copyTicket : ticket,
                isCopy: isCopy,
                project: this.project,
                projectStage: this.projectStage,
                ticketType: ticketType,
                isTransferEscrow,
                isSuperAdmin
            },
            disableClose: true,
            panelClass: 'no-padding-dialog-container'
        });
        createGroupPopup.afterClosed().subscribe((result: boolean) => {
            if (result) {
                this.getTicket(this.getTicketQuery());
            }
        });
    }
    openEditTicket(ticket: any) {
        this.openCreateTicket(ticket, false);
    }
    openEditTicketBySuperAdmin(ticket: any) {
        this.openCreateTicket(ticket, false, false, true);
    }

    onChangePaging(event: CPagination<TicketSale>) {
        this.pagination = event;
        this.getData();
    }

    getData(exchangeId?) {
        let params = {
            exchangeId: exchangeId || this.dvbh,
            keywords: this.searchKey.value ? this.searchKey.value.trim() : '',
            status: this.searchStatus,
        }
        switch (this.selectedTab) {
            case 1:
                this.getTicket({ ticketType: 'YCTV' });
                break;
            case 2:
                this.getTicket({ ticketType: 'YCDCH', ...params });
                break;
            case 3:
                this.getTicket({ ticketType: 'YCDC', ...params });
                break;
            default:
                break;
        }
    }
    openHDC(contract: any){
        if (contract.isTransferred) {
            this.router.navigate([`contract/transfer/${contract.id}`]);
        } else if (contract.type === 'deposit') {
            this.router.navigate([`contract/deposit/${contract.id}`]);
        } else {
            this.router.navigate([`contract/purchase/${contract.id}`]);
        }
    }
    summaryData(data: CPagination<TicketSale>) {
        this.pagination = data;
        this.rowData = this.pagination.items;
        this.pagination.total = data.total ? data.total : 0;
        this.pagination.totalPage = data.totalPage ? data.totalPage : 0;
        this.pagination.page = data.page;
    }

    checkFileExtension(file: File): boolean {
        if (!file) {
            return false;
        }
        return ['xls', 'xlsx'].includes(file.name.split('.').pop().toLocaleLowerCase());
    }

    openDialogUpdatePrice(oEvent) {
        this.isTableLoading$.next(true);
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        let body: any = {
            projectId: this.selectedProject.id,
            projectName: this.selectedProject.name,
            categoryId: this.categories[this.selectedTab],
            transactionType: 'SELL',
        };
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            body.salesProgramIds = this.lstSaleProgramId;
        } else {
            body.salesProgramId = this.currentSalesProgram.id;
        }
        return this.service.importUpdatePrice(selectedFile, body).subscribe((res: any) => {
            if (res.status < 400) {
                const result = res._body;
                if (result.errors) {
                    this.toastr.error(result.errors.message);
                    this.isTableLoading$.next(false);
                    return;
                }
                this.toastr.success('Đang tải lên', 'Cập nhật giá bảng hàng! ');
            } else {
                this.toastr.error('Lỗi');
                this.isTableLoading$.next(false);
            }
        });
    }

    openDialogPropertyPrimary(oEvent) {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        this.isTableLoading$.next(true);
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        const body = {
            projectId: this.selectedProject.id,
            projectName: this.selectedProject.name,
            categoryId: this.categories[this.selectedTab],
            transactionType: 'SELL',
            salesProgramId: this.currentSalesProgram.id
        }
        return this.service.importFile(selectedFile, body).subscribe((res: any) => {
            if (res.status < 400) {
                const result = res._body;
                if (result.errors) {
                    this.toastr.error(result.errors.message);
                    return;
                }
                this.toastr.success('Đang tải lên', 'Quản lý bán hàng! ');
                let data = JSON.parse(result);
                data.historyModel.currentSalesProgram = this.currentSalesProgram;
                const comfirmDialog = this.dialog.open(PopupComfirmImportComponent, {
                    width: '900px',
                    data: data.historyModel
                });
                comfirmDialog.afterClosed().subscribe((result: boolean) => {
                    this.isTableLoading$.next(false);
                    this.getTableData();
                });
            } else {
                this.toastr.error('Lỗi');
            }
        });
    }

    openDialogImportYCDCO(oEvent) {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        this.isTableLoading$.next(true);
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        const body = {
            projectId: this.selectedProject.id,
            saleProgramId: this.currentSalesProgram.id
        }
        return this.primaryTransactionService.importYCDCO(selectedFile, body)
        .pipe(
            finalize(() => this.isTableLoading$.next(false)),
            takeUntil(this.unsubscribe$))
        .subscribe((res: any) => {
            this.toastr.success('Đã tải lên', 'Yêu cầu đặt cọc! ');
        }, (error: any) => {
            this.toastr.error(error.error.errors[Object.keys(error.error.errors)[0]], "Thất bại");
        });
    }

    openDialogTransferUnit(oEvent) {
        this.isTableLoading$.next(true);
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        const body = {
            projectId: this.selectedProject.id,
            projectName: this.selectedProject.name,
            // salesProgramId: this.currentSalesProgram.id
        };
        return this.propertyService.importTransferFile(selectedFile, body).subscribe((res: any) => {
            if (res.status < 400) {
                const result = res._body;
                if (result.errors) {
                    this.toastr.error(result.errors.message);
                    return;
                }
                this.toastr.success('Đang cập nhật đơn vị', 'Quản lý bán hàng! ');
            } else {
                this.toastr.error('Lỗi');
            }
        });
    }

    openDialogUpdatePropertyPrimary(oEvent) {
        this.isTableLoading$.next(true);
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        let body: any = {
            projectId: this.selectedProject.id,
            projectName: this.selectedProject.name,
            categoryId: this.categories[this.selectedTab],
            transactionType: 'SELL',
        };
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            body.salesProgramIds = this.lstSaleProgramId;
        } else {
            body.salesProgramId = this.currentSalesProgram.id;
        }
        return this.service.importUpdateFile(selectedFile, body).subscribe((res: any) => {
            if (res.status < 400) {
                const result = res._body;
                if (result.errors) {
                    this.toastr.error(result.errors.message);
                    return;
                }
                this.toastr.success('Đang tải lên', 'Cập nhật bảng hàng!');
            } else {
                this.toastr.error('Lỗi');
            }
        });
    }

    openDialogUpdateImagePropertyUnit(oEvent) {
        this.isTableLoading$.next(true);
        let body: any = {
            projectId: this.selectedProject.id,
            projectName: this.selectedProject.name,
            categoryId: this.categories[this.selectedTab],
            transactionType: 'SELL',
        };
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            body.salesProgramIds = this.lstSaleProgramId;
        } else {
            body.salesProgramId = this.currentSalesProgram.id;
        }
        return this.propertyPrimaryService
            .importPropertyImages(oEvent.target.files, body)
            .pipe(
                finalize(() => this.isTableLoading$.next(false)),
                takeUntil(this.unsubscribe$)
            )
                .subscribe((res: any) => {
                if (res.status < 400) {
                    const result = res._body;
                    if (result.errors) {
                        this.toastr.error(result.errors.message);
                        return;
                    }
                    this.toastr.success('Đang tải lên', 'Cập nhật bảng hàng!');
                } else {
                    this.toastr.error('Lỗi');
                }
        });
    }

    handleClickInputFile(fileInput) {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        fileInput.value = '';
        fileInput.click();
    }
    downloadSaleList() {
        const fileName = 'Danh_sach_giao_dich_' + getCurrentTimeSigned();
        let sheets = [];
        let saleList = new PropertyTable();
        sheets.push({
            headers: saleList.getHeaders(),
            data: this.saleListFilter.map((item, idx) => {
                const copyItem = { ...item }
                copyItem.stt = (idx + 1);
                const priority = item.priorities.find(i => i.status === PropertyUnitStatus.SUCCESS);
                copyItem['customerName'] = priority ? priority.customerName : '';
                copyItem['posName'] = priority ? priority.posName : '';
                copyItem['customerName1'] = item.priority1 ? item.priority1.customerName : '';
                copyItem['posName1'] = item.priority1 ? item.priority1.posName : '';
                copyItem['customerName2'] = item.priority2 ? item.priority2.customerName : '';
                copyItem['posName2'] = item.priority2 ? item.priority2.posName : '';
                copyItem['customerName3'] = item.priority3 ? item.priority3.customerName : '';
                copyItem['posName3'] = item.priority3 ? item.priority3.posName : '';
                copyItem['primaryStatusName'] = copyItem.primaryStatus && UNIT_STATUS[copyItem.primaryStatus] ? UNIT_STATUS[copyItem.primaryStatus] : '';
                return copyItem;
            }),
            sheetName: 'DS SP giao cho đơn vị'
        });
        sheets.push({
            headers: saleList.getHeaders(),
            data: this.saleList2Filter.map((item, idx) => {
                const copyItem = { ...item }
                copyItem.stt = (idx + 1);
                const priority = item.priority0 && item.priority0.status === PropertyUnitStatus.SUCCESS ? item.priority0 : null;
                copyItem['customerName'] = priority ? priority.customerName : '';
                copyItem['posName'] = priority ? priority.posName : '';
                copyItem['customerName1'] = item.priority0 ? item.priority0.customerName : '';
                copyItem['posName1'] = item.priority0 ? item.priority0.posName : '';
                copyItem['customerName2'] = '';
                copyItem['posName2'] = '';
                copyItem['customerName3'] = '';
                copyItem['posName3'] = '';
                copyItem['primaryStatusName'] = copyItem.primaryStatus && UNIT_STATUS[copyItem.primaryStatus] ? UNIT_STATUS[copyItem.primaryStatus] : '';
                return copyItem;
            }),
            sheetName: 'DS SP tự do'
        });
        this.excelService.exportAsExcelFile(sheets, fileName);

    }

    onChangeStatus(status) {
        this.currentFilter.status = status ? status.id : '';
        this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);
    }

    downloadPropertyPrimary() {
        const fileName = 'Bang_hang_' + this.selectedProject.name + '_' + getCurrentTimeSigned();
        const propertyTable = new PropertyTable({
            category: { id: this.categories[this.selectedTab] },
        });
        let sheets = [];
        let params: any = {
            categoryId: this.categoryId,
            projectId: this.selectedProject.id,
            _fields: `id,project.name,project.id,pos.name,pos.id,code,apartmentType,areaWide,areaLong,primaryStatus,shortCode,attributes.attributeName,block,floor,outsideArea,
                attributes.value,bedroom,direction,priceAbove,price,area,priorities,extendable,extendPos,reasonRevoke,modifiedDate,priceVat,category.id,description,salesProgram.id,owner,extData`
        };
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            params.salesProgramIds = this.lstSaleProgramId;
        } else {
            params.salesProgramId = this.currentSalesProgram.id;
        }
        let saleListParams: any = {
            projectId: this.selectedProject.id,
            stage: 1,
            status: PrimaryTransactionStatus.PRIORITY_UPDATED,
            ticketType: 'YCDCH',
            download: 1,
            _fields: `id,customer,employee.name,employee.pos.name,propertyUnit.code,propertyUnit.shortCode,propertyUnit.bedroom,propertyUnit.attributes.id,block,floor,
                propertyUnit.attributes.value,status,priority,project.name,project.code,pos.name`
        };
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            saleListParams.salesProgramIds = this.lstSaleProgramId;
        } else {
            saleListParams.salesProgramId = this.currentSalesProgram.id;
        }

        this.isDownloadLoadingAll$.next(true);

        forkJoin(
            this.projectService.getTable(params),
            this.projectPrimaryTransactionService.getTickets(saleListParams),
        ).pipe(
            finalize(() => this.isDownloadLoadingAll$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
            let data = res[0].map((item: any, idx) => {
                item.stt = (idx + 1);
                return new PropertyTable(item);
            })
            const priorities = [];
            if(this.createdFrom){
                data = data.filter(item => {
                    let check = item.priorities.filter(i => i.modifiedDate && (new Date(i.modifiedDate)) >= (new Date(this.createdFrom)));
                    if (check && check.length) {
                      priorities.push(...check.map(i => (i.id)));
                      return true;
                    }
                    return false;
                });
            };
            if (this.createdTo) {
                data = data.filter(item => {
                    let check = item.priorities.filter(i => i.modifiedDate && (new Date(i.modifiedDate)) <= (new Date(this.createdTo)));
                    if (check && check.length) {
                      priorities.push(...check.map(i => (i.id)));
                      return true;
                    }
                    return false;
                });
            }

            let data2 = res[1].rows;
            if(data2.length && priorities.length){
                data2 = data2.filter( i => priorities.includes(i.id))
            }
            let extDataArr = [];
            let extDataHeaders = [];
            data = data.map((item: any, idx) => {
                item.stt = (idx + 1);
                if (!_.isEmpty(item.extData)) {
                    if (!extDataArr.length) {
                        extDataArr = (this.objectPaths(item.extData) || []);
                        extDataHeaders = extDataArr.map((key, index) => ({ header: `extData.${key}`, key: `i_${index}`, width: 40 }));
                    }
                    extDataArr.forEach((pr, index) => {
                        item[`i_${index}`] = item.extData[pr];
                    });
                }

                return item;
            });
            const headers = [...propertyTable.getHeaders(), ...extDataHeaders];
            // sheet 1
            const sheet1 = {
                headers: headers,
                hideRows: propertyTable.getHideRows(),
                data,
                sheetName: 'Quản lý bán hàng'
            };
            sheets.push(sheet1);

            let sheet2Data = [];
            if (data2.length) {
                _.each(data2, (item, idx) => {
                    const findIndex = sheet2Data.findIndex(i => item.propertyUnit && i.type === item.propertyUnit.code);
                    if (findIndex !== -1) {
                        const priorityInfo = propertyTable.createPriorityInfo(item);

                        if (item.priority === 1) {
                            sheet2Data[findIndex].customerName = priorityInfo.customerName;
                            sheet2Data[findIndex].birthday = priorityInfo.birthday;
                            sheet2Data[findIndex].phone = priorityInfo.phone;
                            sheet2Data[findIndex].identity = priorityInfo.identity;
                            sheet2Data[findIndex].identityDate = priorityInfo.identityDate;
                            sheet2Data[findIndex].identityAddress = priorityInfo.identityAddress;
                            sheet2Data[findIndex].address = priorityInfo.address;
                            sheet2Data[findIndex].rootAddress = priorityInfo.rootAddress;
                            sheet2Data[findIndex].email = priorityInfo.email;
                            sheet2Data[findIndex].pos = priorityInfo.posName;
                            sheet2Data[findIndex].employee = priorityInfo.employeeName;
                        } else if (item.priority === 2) {
                            sheet2Data[findIndex].customerName2 = priorityInfo.customerName;
                            sheet2Data[findIndex].birthday2 = priorityInfo.birthday;
                            sheet2Data[findIndex].phone2 = priorityInfo.phone;
                            sheet2Data[findIndex].identity2 = priorityInfo.identity;
                            sheet2Data[findIndex].identityDate2 = priorityInfo.identityDate;
                            sheet2Data[findIndex].identityAddress2 = priorityInfo.identityAddress;
                            sheet2Data[findIndex].address2 = priorityInfo.address;
                            sheet2Data[findIndex].rootAddress2 = priorityInfo.rootAddress;
                            sheet2Data[findIndex].email2 = priorityInfo.email;
                            sheet2Data[findIndex].pos2 = priorityInfo.posName;
                            sheet2Data[findIndex].employee2 = priorityInfo.employeeName;
                        } else if (item.priority === 3) {
                            sheet2Data[findIndex].customerName3 = priorityInfo.customerName;
                            sheet2Data[findIndex].birthday3 = priorityInfo.birthday;
                            sheet2Data[findIndex].phone3 = priorityInfo.phone;
                            sheet2Data[findIndex].identity3 = priorityInfo.identity;
                            sheet2Data[findIndex].identityDate3 = priorityInfo.identityDate;
                            sheet2Data[findIndex].identityAddress3 = priorityInfo.identityAddress;
                            sheet2Data[findIndex].address3 = priorityInfo.address;
                            sheet2Data[findIndex].rootAddress3 = priorityInfo.rootAddress;
                            sheet2Data[findIndex].email3 = priorityInfo.email;
                            sheet2Data[findIndex].pos3 = priorityInfo.posName;
                            sheet2Data[findIndex].employee3 = priorityInfo.employeeName;
                        }

                    } else {
                        item.stt = (sheet2Data.length + 1);
                        const showfloor = item.project.code === "GSW";
                        const obj = propertyTable.createPropertyTableDownloadObj(item, showfloor);
                        sheet2Data.push(obj);
                    }
                });
            }

            sheet2Data = sheet2Data.sort((a, b) => {
                const sortA = a.type.split('-');
                const sortB = b.type.split('-');
                for (let idx = 0; idx < sortA.length; idx++) {
                    if (idx + 1 <= sortB.length) {
                        if (sortA[idx] !== sortB[idx]) {
                            if (!isNaN(sortA[idx]) && !isNaN(sortB[idx])) {
                                if (parseInt(sortA[idx]) > parseInt(sortB[idx])) {
                                    return 1;
                                }
                                if (parseInt(sortA[idx]) < parseInt(sortB[idx])) {
                                    return -1;
                                }
                            } else {
                                return sortA[idx].localeCompare(sortB[idx])
                            }
                        }
                    } else {
                        return -1;
                    }
                }
                return a.type.localeCompare(b.type)
            });
            sheet2Data = sheet2Data.map((item: any, idx) => {
                item.stt = (idx + 1);
                return item;
            });
            sheets.push({
                headers: propertyTable.getHeaderBangHang(),
                data: sheet2Data,
                mergeCells: ['A1:K1', 'A2:C2', 'A3:C3'],
                projectName: this.project.name.toUpperCase(),
                numberHeader: 6,
                sheetName: 'Ráp sản phẩm'
            });

            this.excelService.exportAsExcelFileCustom(sheets, fileName, ExportTypeEnum.RAP_SAN_PHAM);
        });
    }

    rKeys(o, path = "") {
        if (!o || typeof o !== "object") return path;
        return Object.keys(o).map(key => this.rKeys(o[key], path ? [path, key].join(".") : key))
    }
    objectPaths(o) {
        return this.rKeys(o).toString().split(",")
    }

    getCustomerAddress(address) {
        return typeof address === 'string' ? address : [address.address, address.ward, address.district, address.province].join(', ');
    }

    openEventDashboard(projectId: string) {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        window.open(`project/event-dashboard/${projectId}/${this.currentSalesProgram.id}`, '_blank');
    }
    isInvalidTime(openTime, closeTime) {
        if (!openTime) return false;
        // khóa các chức năng nếu thời gian hiện tại < so với thời gian bắt đầu, và thời gian hiện tại nhỏ hơn thời gian kết thúc
        // nếu là các role có permission sẽ ko giới hạn về thời gian
        const invalidTime = moment().isAfter(moment(openTime));
        return closeTime ? (!moment().isAfter(moment(closeTime)) && invalidTime) : invalidTime;
    }

    public openTicketNav(ticket: any) {
        let ticketType: string;
        switch (this.selectedTab) {
            case 1:
                ticketType = 'YCTV';
                break;
            case 2:
                ticketType = 'YCDCH';
                break;
            case 3:
            case 4:
                ticketType = 'YCDC';
                break;
            default:
                ticketType = 'YCTV';
                break;
            }
        const createGroupPopup = this.dialog.open(EventSalePopupPropertyTicketDetailComponent, {
            width: '580px',
            data: {
                ticket: ticket,
                ticketType: ticketType,
                project: this.project,
            },
            disableClose: true,
            panelClass: 'no-padding-dialog-container'
        });
        createGroupPopup.afterClosed().subscribe((result: boolean) => {
            if (result) {
            }
        });
    }

    handleLockStatus() {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        const lockStatus = this.currentSalesProgram.lock || false;
        this.salesProgramService.changeLockStatus({ id: this.currentSalesProgram.id, lock: !lockStatus, projectId: this.projectId }).subscribe(res => {
            this.currentSalesProgram.lock = !lockStatus;
        });
    }
    handleLockEditPriorities() {
        this.salesProgramService.changeLockStatus({ id: this.currentSalesProgram.id, lock: true }).subscribe(res => {
            this.dialog.open(DialogMessageNoticeComponent, {
                width: '562px',
                height: '142px',
                panelClass: 'app-dialog',
                data: {
                    message: 'Khóa Chỉnh sửa ưu tiên thành công'
                }
            });
        });
    }
    handleLockExtendPos() {
        const extendableUnits = this.propertyArrModel.filter(x => x.extendable === true);
        const popupRef = this.dialog.open(PopupExtendPriorityComponent, {
            width: '600px',
            panelClass: 'app-dialog',
            disableClose: true,
            data: {
                projectId: this.projectId,
                listUnit: extendableUnits,
                salesUnit: this.pos,
                locked: true,
                salesProgramId: this.currentSalesProgram.id
            }
        });
        popupRef.afterClosed().subscribe((result: any) => {
            if (result) {
                this.dialog.open(DialogMessageNoticeComponent, {
                    width: '562px',
                    height: '142px',
                    panelClass: 'app-dialog',
                    data: {
                        message: 'Khóa Bổ sung ưu tiên thành công'
                    }
                });
            }
        });
    }

    public isManager() {
        return (this.storageService.retrieve('roleId') !== ERoleId.NVKD);
    }

    public isHideInfoTicket(ticket) {
        return ticket && ticket.masked;
    }

    public isTicketCancelable(ticket) {
        return (ticket && ticket.status === PrimaryTransactionStatus.CLOSE ||
            ticket && ticket.status === PrimaryTransactionStatus.ADMIN_APPROVED_TICKET ||
            ticket && ticket.status === PrimaryTransactionStatus.DEPOSIT_REJECTED) &&
            this.isTicketOwner(ticket);
    }
    public isTicketCancelRequestAvailable(ticket) {
        return (ticket && ticket.status === PrimaryTransactionStatus.BOOKING_APPROVED ||
            ticket && ticket.status === PrimaryTransactionStatus.CS_REJECTED_ESCROW ||
            ticket && ticket.status === PrimaryTransactionStatus.UNSUCCESS) &&
            this.isTicketOwner(ticket);
    }
    public isTransferToEscrowAvailable(ticket) {
        return (ticket && ticket.status === PrimaryTransactionStatus.BOOKING_APPROVED ||
            ticket && ticket.status === PrimaryTransactionStatus.PRIORITY_UPDATED) &&
            this.user.hasPermission(PropertyTicketPermissionConst.Others.TransferEscrow) &&
            this.isTicketOwner(ticket);
    }

    public isTicketEditable(ticket) {
        return (
            ticket && ticket.status === PrimaryTransactionStatus.CLOSE ||
            ticket && ticket.status === PrimaryTransactionStatus.DEPOSIT_REJECTED ||
            ticket && ticket.status === PrimaryTransactionStatus.CS_REJECTED_TICKET ||
            ticket && ticket.status === PrimaryTransactionStatus.ADMIN_APPROVED_TICKET ||
            ticket && ticket.status === PrimaryTransactionStatus.ADMIN_REJECTED_TICKET || (
                ticket && ticket.ticketType === 'YCDC' && (
                    ticket && ticket.status === PrimaryTransactionStatus.PROCESSING_UNPAID ||
                    ticket && ticket.status === PrimaryTransactionStatus.POS_REJECT_UNPAID ||
                    ticket && ticket.status === PrimaryTransactionStatus.SA_REJECT_UNPAID
                ))
        ) && !this.isHideInfoTicket(ticket);
    }

    public isTicketApprovableByAdmin(ticket) {
        return (ticket && ticket.status === PrimaryTransactionStatus.CLOSE ||
            ticket && ticket.status === PrimaryTransactionStatus.COMING ||
            ticket && ticket.status === PrimaryTransactionStatus.PROCESSING) &&
            this.user.hasPermission(PropertyTicketPermissionConst.Admin.ApproveTicket);
    }

    public isTicketApprovableByCS(ticket) {
        return (ticket && ticket.status === PrimaryTransactionStatus.ADMIN_APPROVED_TICKET ||
            ticket && ticket.status === PrimaryTransactionStatus.DEPOSIT_APPROVED ||
            ticket && ticket.status === PrimaryTransactionStatus.POS_CONFIRM ||
            ticket && ticket.status === PrimaryTransactionStatus.LOCK_CONFIRM) &&
            this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicket);
    }

    public isTicketCancelRequestApprovableByAdmin(ticket: TicketSale): boolean {
        return ticket && ticket.status === PrimaryTransactionStatus.CANCEL_REQUESTED &&
            this.user.hasPermission(PropertyTicketPermissionConst.Admin.ApproveTicketCancelRequest);
    }

    public isTicketCancelRequestApprovableByCS(ticket: TicketSale): boolean {
        return ticket && ticket.status === PrimaryTransactionStatus.ADMIN_APPROVED_CANCEL_REQUESTED &&
            this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicketCancelRequest);
    }

    public isTicketOwner(ticket) {
        return ticket && ticket.employee && ticket.employee.id === this.user.id;
    }

    onFilterPriority(priority: string) {
        this.filterBangHang.priority = priority;
        this.getTableData(true);
    }


    openEmailDialogSalesUnit() {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        const popupEmailRef = this.dialog.open(PopupEmailComponent, {
            width: '787px',
            height: '614px',
            panelClass: 'app-dialog',
            disableClose: true,
            autoFocus: false,
            data: {
                projectId: this.projectId,
                stage: this.projectStage,
                salesProgramId: this.currentSalesProgram.id
            }
        });

        popupEmailRef.afterClosed().subscribe(res => {
            if (res && res.isSuccess) {
                this.projectService.sendEmailComfirmSalesUnit(res.params)
                    .pipe(takeUntil(this.unsubscribe$))
                    .subscribe(res => {
                        if (res && res.success) {
                            this.dialog.open(DialogMessageNoticeComponent, {
                                width: '562px',
                                height: '142px',
                                panelClass: 'app-dialog',
                                disableClose: true,
                                data: {
                                    message: "Gửi email xác nhận thành công"
                                }
                            });
                        } else {
                            this.dialog.open(DialogMessageNoticeComponent, {
                                width: '562px',
                                height: '142px',
                                panelClass: 'app-dialog',
                                disableClose: true,
                                data: {
                                    message: "Có lỗi vui lòng thao tác lại"
                                }
                            });
                        }
                    }), error => {
                        this.dialog.open(DialogMessageNoticeComponent, {
                            width: '562px',
                            height: '142px',
                            panelClass: 'app-dialog',
                            disableClose: true,
                            data: {
                                message: "Có lỗi vui lòng thao tác lại"
                            }
                        });
                    };
            }
        });
    }


    openSendSmsCustomerDialog() {
        this.isSendSmsCustomer$.next(true);
        const dialogConfirmRef = this.dialog.open(DialogConfirmComponent, {
            width: '500px',
            data: {
              message: 'Bạn có chắc muốn gửi SMS cho khách hàng không?',
              textConfirm: 'Xác nhận',
              textCancel: 'Hủy bỏ'
            },
            autoFocus: false
          });
        dialogConfirmRef.afterClosed().subscribe((result: boolean) => {
            if (result) {
                this.projectPrimaryTransactionService.sendSmsCustomer({
                    salesProgramId: this.currentSalesProgram.id,
                    projectId: this.projectId,
                }).pipe(
                    finalize(() => this.isSendSmsCustomer$.next(false)),
                    takeUntil(this.unsubscribe$)
                ).subscribe(res => {
                    this.isSendSmsCustomer$.next(false);
                    this.toastr.success('Gửi SMS thành công', 'Thành công');
                }, (err) => {
                    this.isSendSmsCustomer$.next(false);
                    this.toastr.error(err.error ? err.error.message : err.message, 'Không thành công');
                });
            } else {
                this.isSendSmsCustomer$.next(false);
            }
        });
    }

    openTransferDialog(isRevoke: boolean, isMove: boolean = false, isRemove: boolean = false, isPublishPrice = false, selectedCode?) {
        if (this.countSalesProgramChoose !== 1 && isPublishPrice) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này')
            return;
        }
        let listUnit = [];
        if (isMove) {
            // Chuyển / Thu hồi :Remove SUCCESS/LOCK_CONFIRM
            if(selectedCode) {
                listUnit = this.propertyArrModelFilter
                .filter(x => (x.primaryStatus !== PropertyUnitStatus.SUCCESS && x.primaryStatus !== PropertyUnitStatus.LOCK_CONFIRM && x.primaryStatus !== PropertyUnitStatus.MOVED) || (x.code === selectedCode && x.primaryStatus !== PropertyUnitStatus.MOVED));
            } else {
                listUnit = this.propertyArrModelFilter
                .filter(x => x.primaryStatus !== PropertyUnitStatus.SUCCESS && x.primaryStatus !== PropertyUnitStatus.LOCK_CONFIRM && x.primaryStatus !== PropertyUnitStatus.MOVED);
            }
        } else if (isRevoke) {
            if(selectedCode) {
                listUnit = this.propertyArrModelFilter
                .filter(x => (x.primaryStatus !== PropertyUnitStatus.SUCCESS && x.primaryStatus !== PropertyUnitStatus.LOCK_CONFIRM) || (x.code === selectedCode));
            } else {
                listUnit = this.propertyArrModelFilter
                .filter(x => x.primaryStatus !== PropertyUnitStatus.SUCCESS && x.primaryStatus !== PropertyUnitStatus.LOCK_CONFIRM);
            }
        } else {
            listUnit = this.propertyArrModelFilter;
        }

        const popupRef = this.dialog.open(PopupTransferPropertyComponent, {
            width: '600px',
            panelClass: 'app-dialog',
            disableClose: true,
            data: {
                projectId: this.projectId,
                listUnit: listUnit,
                settingData: this.selectedProject.setting,
                isRevoke: isRevoke,
                isRemove: isRemove,
                isMove: isMove,
                isPublishPrice: isPublishPrice,
                salesUnit: this.pos,
                selectedCode: selectedCode ? selectedCode : null,
                isRevokeAny: this.isRevokeAny,
                inEvent: this.startEvent || this.endEvent,
                blockFloorRoom: this.blockFloorRoom
            }
        });

        popupRef.afterClosed().subscribe(res => {
            if (res && res.isSuccess) {
                this.dialog.open(DialogMessageNoticeComponent, {
                    width: '562px',
                    height: '142px',
                    panelClass: 'app-dialog',
                    data: {
                        message: isRemove ? 'Xóa sản phẩm' : isPublishPrice ? 'Công bố giá thành công' : (isRevoke ? 'Thu hồi thành công' : 'Chuyển thành công')
                    }
                });
                if (!isPublishPrice) {
                    this.getTableData();
                } else {
                    this.isTableLoading$.next(true);
                }
            }
        });
    }

    openImageDialog() {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        let listUnit = [];
        listUnit = this.propertyArrModelFilter;

        const popupRef = this.dialog.open(PopupImagePropertyComponent, {
            width: '600px',
            panelClass: 'app-dialog',
            disableClose: true,
            data: {
                projectId: this.projectId,
                listUnit: listUnit,
                salesProgramId: this.currentSalesProgram.id,
                blockFloorRoom: this.blockFloorRoom
            }
        });
        popupRef.afterClosed().subscribe(response => {
            this.getTableData();
        });
    }

    openTransferSaleProgramDialog(selectedCode?) {
        const popupRef = this.dialog.open(PopupTransferSalesProgramComponent, {
            width: '600px',
            panelClass: 'app-dialog',
            disableClose: true,
            data: {
                salesProgramId: this.currentSalesProgram.id,
                salesProgram: this.countSalesProgramChoose === 1 ? this.salesProgram.filter(e => e.id !== this.currentSalesProgram.id) :  this.salesProgram,
                projectId: this.projectId,
                listUnit: this.propertyArrAllowMoveSalesProgram,
                blockFloorRoom: this.blockFloorRoom,
                selectedCode: selectedCode ? selectedCode : null
            }
        });

        popupRef.afterClosed().subscribe(res => {
            if (res && res.isSuccess) {
                this.dialog.open(DialogMessageNoticeComponent, {
                    width: '562px',
                    height: '142px',
                    panelClass: 'app-dialog',
                    data: {
                        message: 'Chuyển chương trình bán hàng thành công'
                    }
                });
                this.getTableData();
            }
        });
    }
    openPublishPriceHistory() {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này')
            return;
        }
        const popupRef = this.dialog.open(PopupPublishPriceHistoryComponent, {
            width: '600px',
            panelClass: 'app-dialog',
            disableClose: true,
            data: {
                salesProgramId: this.currentSalesProgram.id,
                projectId: this.projectId,
                settingData: this.selectedProject.setting,
                salesUnit: this.pos,
                listUnit: this.propertyArrModelFilter,
                blockFloorRoom: this.blockFloorRoom
            }
        });

        // popupRef.afterClosed().subscribe(res => {
        //     if (res && res.isSuccess) {
        //         this.dialog.open(DialogMessageNoticeComponent, {
        //             width: '562px',
        //             height: '142px',
        //             panelClass: 'app-dialog',
        //             data: {
        //                 message: 'Chuyển chương trình bán hàng thành công'
        //             }
        //         });
        //         this.getTableData();
        //     }
        // });
    }

    openPublishDialog() {
        const popupRef = this.dialog.open(PopupPublishStatusComponent, {
            width: '600px',
            panelClass: 'app-dialog',
            disableClose: true,
            data: {
                projectId: this.projectId,
                listUnit: this.propertyArrModelFilter.filter(x => x.primaryStatus !== PropertyUnitStatus.MOVED),
                settingData: this.selectedProject.setting,
            }
        });

        popupRef.afterClosed().subscribe(res => {
            if (res && res.isSuccess) {
                this.dialog.open(DialogMessageNoticeComponent, {
                    width: '562px',
                    height: '142px',
                    panelClass: 'app-dialog',
                    data: {
                        message: 'Điều chỉnh trạng thái sản phẩm thành công'
                    }
                });
                this.getTableData();
            }
        });
    }
    onChangeFilterBangHang(event: any, updateSalesProgram = false, setQuery = false) {
        if (setQuery) {
            const query = {...this.route.snapshot.queryParams};
            this.updateQueryParams(query, "spid", event ? event.id : "");
            this.updateQueryParams(query, "tpl");
        }
        if (event && event.id) {
            if (updateSalesProgram) {
                let p = {
                    projectId: this.projectId
                }
                this.salesProgramService.getSalesProgramByIdByProjectId(event.id, p).subscribe(res => {
                    this.currentSalesProgram = new SalesProgramModel(res);
                    this.saleUnitLockConfirmable = this.currentSalesProgram.saleUnitLockConfirmable;
                    this.owners = this.currentSalesProgram.owners;
                    this.currentTemplateCode = this.route.snapshot.queryParams.tpl || this.currentSalesProgram.unitTableTemplateUrl && this.currentSalesProgram.unitTableTemplateUrl[0] && this.currentSalesProgram.unitTableTemplateUrl[0].templateCode;
                    this.currentTemplateName = this.route.snapshot.queryParams.tpl || this.currentSalesProgram.unitTableTemplateUrl && this.currentSalesProgram.unitTableTemplateUrl[0] && this.currentSalesProgram.unitTableTemplateUrl[0].templateName;
                    this.blockFloorRoom = this.currentSalesProgram.blocks && this.currentSalesProgram.blocks.map((el: any, index: number) => {
                        return {
                            isDisplay: true,
                            block: el.block,
                            floors: el.floors.split(',').reverse(),
                            rooms: el.rooms.split(',')
                        };
                    });

                    this.canHoDraw = this.convertBlockToView();
                    if (!this.currentSalesProgram.lock || this.showLockExtendPriority) {
                        this.viewTableModeValue = 2;
                    } else {
                        this.viewTableModeValue = 1;
                    }
                    if (this.unitTable) {
                        this.unitTable['excelTemplate'] = null;
                    }
                    this.getTableData();
                    this.getEventProject();
                });
            } else {
                this.getAllLstBangHang();
            }
        } else {
            this.getTableData();
            this.getEventProject();
        }
    }

    getAllLstBangHang() {
        // reset tpl query url
        if(this.route.snapshot.queryParams.tpl) {
            const params = { ...this.route.snapshot.queryParams };
            delete params.tpl
            this.router.navigate([], { queryParams: params });
        }
        let lstSalesProgramChoose: any[] = [];
        let p = {
            projectId: this.projectId
        }
        lstSalesProgramChoose = this.salesProgram.filter((item: any) => {
            return item.checked === true;
        }).map(function(item) {
            return item.id;
        });
        this.selectSaleProgramApply = lstSalesProgramChoose;
        this.countSalesProgramChoose = lstSalesProgramChoose.length;
        if (this.countSalesProgramChoose < 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        if (lstSalesProgramChoose.length > 0) {
            this.salesProgramService.getSalesProgramByLstIdByProjectId(lstSalesProgramChoose, p).subscribe(res => {
                this.currentSalesProgram = new SalesProgramModel(res[0]);
                this.nameSaleProgram = this.currentSalesProgram.name;
                this.saleUnitLockConfirmable = this.currentSalesProgram.saleUnitLockConfirmable;
                this.owners = this.currentSalesProgram.owners;
                this.currentTemplateCode = this.currentSalesProgram.unitTableTemplateUrl && this.currentSalesProgram.unitTableTemplateUrl[0] && this.currentSalesProgram.unitTableTemplateUrl[0].templateCode;
                this.currentTemplateName = this.currentSalesProgram.unitTableTemplateUrl && this.currentSalesProgram.unitTableTemplateUrl[0] && this.currentSalesProgram.unitTableTemplateUrl[0].templateName;
                this.blockFloorRoom = this.currentSalesProgram.blocks && this.currentSalesProgram.blocks.map((el: any, index: number) => {
                    return {
                        isDisplay: true,
                        block: el.block,
                        floors: el.floors.split(',').reverse(),
                        rooms: el.rooms.split(',')
                    };
                });

                this.canHoDraw = this.convertBlockToView();
                this.viewTableModeValue = 1;
                if (this.unitTable) {
                    this.unitTable['excelTemplate'] = null;
                }
                this.getLstTableData(lstSalesProgramChoose);
                this.getEventProject();
            });
        } else {
            this.getTableData();
            this.getEventProject();
        }
    }

    getAllLstGiaoDich() {
        // reset tpl query url
        if(this.route.snapshot.queryParams.tpl) {
            const params = { ...this.route.snapshot.queryParams };
            delete params.tpl
            this.router.navigate([], { queryParams: params });
        }
        let lstSalesProgramChoose: any[] = [];
        let p = {
            projectId: this.projectId
        }
        lstSalesProgramChoose = this.salesProgram.filter((item: any) => {
            return item.checked === true;
        }).map(function(item) {
            return item.id;
        });
        this.selectSaleProgramApply = lstSalesProgramChoose;
        this.countSalesProgramChoose = lstSalesProgramChoose.length;
        if (this.countSalesProgramChoose < 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        if (lstSalesProgramChoose.length > 0) {
            this.salesProgramService.getSalesProgramByLstIdByProjectId(lstSalesProgramChoose, p).subscribe(res => {
                this.currentSalesProgram = new SalesProgramModel(res[0]);
                this.nameSaleProgram = this.currentSalesProgram.name;
                this.saleUnitLockConfirmable = this.currentSalesProgram.saleUnitLockConfirmable;
                this.onChangeViewAll();
                //this.getAllLstBangHang();
                this.onFilterFreeDSGD();
                //this.onChangeKeySearchDSGD()
            });
        } else {
            this.onChangeViewAll();
            this.getTableData();
        }
        this.getEventProject();
    }

    onClearCheckboxSaleProgram() {
        this.lstSaleProgramId = '';
        this.selectSaleProgramApply = [];
        this.selectSaleProgram = [];
        this.salesProgram.map((e: any) => e.checked = false);
        this.currentSalesProgram = this.saleProgramDefault;
        if(this.route.snapshot.queryParams.tpl) {
            const params = { ...this.route.snapshot.queryParams };
            delete params.tpl
            this.router.navigate([], { queryParams: params });
        }
        this.getTableData(true);
    }

    // onChangeFilterBlock($event?: any) {
    //   if($event) {
    //     this.listFloor = $event.floors;
    //     this.selectedBlock = $event.block;
    //   } else {
    //     this.listFloor = [];
    //   }
    // }

    // onChangeFilterFloor($event?: string) {

    // }

    private _countPriorities() {
        let p1 = 0;
        let p2 = 0;
        let p3 = 0;
        this.numberPrioritiesOfFloor = {};

        this.propertyArrModelFilter.forEach((el) => {
            const priorities = el.priorities;
            const numberPriority1 = priorities[0] && priorities[0].id && priorities[0].customerName && (!this.selectPos.length || this.selectPos.includes(priorities[0].posId)) ? 1 : 0;
            const numberPriority2 = priorities[1] && priorities[1].id && priorities[1].customerName && (!this.selectPos.length || this.selectPos.includes(priorities[1].posId)) ? 1 : 0;
            const numberPriority3 = priorities[2] && priorities[2].id && priorities[2].customerName && (!this.selectPos.length || this.selectPos.includes(priorities[2].posId)) ? 1 : 0;

            p1 += numberPriority1;
            p2 += numberPriority2;
            p3 += numberPriority3;

            if (this.numberPrioritiesOfFloor[`${el.block}-${el.floor}`]) {
                this.numberPrioritiesOfFloor[`${el.block}-${el.floor}`] += numberPriority1 + numberPriority2 + numberPriority3;
            } else {
                this.numberPrioritiesOfFloor[`${el.block}-${el.floor}`] = numberPriority1 + numberPriority2 + numberPriority3;
            }
        });

        this.countPriorities.p1 = p1;
        this.countPriorities.p2 = p2;
        this.countPriorities.p3 = p3;
    }

    sendEmail(item) {
        this.primaryTransactionService.sendEmail(item.id, 'YCDCH').subscribe((res: any) => {
            // Show success dialog
            const dialogRef = this.dialog.open(PropertyTicketSubmitDialogComponent, {
                width: '400px',
                panelClass: 'app-dialog',
                data: {
                    title: 'Yêu cầu gửi email của bạn đã được gửi đi.'
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
            });
        },
            (error: any) => {
                // Show error
                if (error.error && error.error.errors && error.error.errors['undefined.existed.error']) {
                }
            });
    }

    onFilterStatusBangHang(primaryStatus = '', liquidateType = null, isContract = false) {
        this.filterBangHang.primaryStatus = primaryStatus;
        this.filterBangHang.liquidateType = liquidateType;
        this.filterBangHang.isContract = isContract;
        this.getTableData();
    }

    scrollView() {
        $("#main-content").scroll(function () {
            var scroll = $("#main-content").scrollTop();
            if (scroll >= 200) {
                $("#table-header").addClass("table-unit-header-fixed");
            }
            else {
                $("#table-header").removeClass("table-unit-header-fixed");
            }
        });
    }

    onChangeKeySearchDSGD(keySearch: string = '', status = this.status, isRefresh = false) {
        keySearch = keySearch ? keySearch.trim() : '';

        let tmpList1 = this.ng2SearchPipe.transform(this.saleList, keySearch);
        let tmpList2 = this.ng2SearchPipe.transform(this.saleList2, keySearch);
        // tmpList1 = _.uniq(tmpList1, x => x.id);
        // tmpList2 = _.uniq(tmpList2, x => x.id);

        this.countSaleListStatus = this._countStatusSaleList(tmpList1);
        this.countSaleListStatus2 = this._countStatusSaleList(tmpList2);

        if (status) {
            tmpList1 = tmpList1.filter(el => status.split(',').map(s => s.trim()).includes(el.primaryStatus));
            tmpList2 = tmpList2.filter(el => status.split(',').map(s => s.trim()).includes(el.primaryStatus));
        }

        if (this.statusFilterUnitDSGD) {
            tmpList1 = tmpList1.filter(el => this.statusFilterUnitDSGD.split(',').map(s => s.trim()).includes(el.primaryStatus));
        } else {
            tmpList1 = _.cloneDeep(tmpList1);
        }

        if (this.statusFilterFreeDSGD) {
            tmpList2 = tmpList2.filter(el => this.statusFilterFreeDSGD.split(',').map(s => s.trim()).includes(el.primaryStatus));
        } else {
            tmpList2 = _.cloneDeep(tmpList2);
        }

        if (this.exceptionOnlyFilter) {
            tmpList1 = this.saleList.filter(x => x.isException);
            tmpList2 = this.saleList2.filter(x => x.isException);
        }

        if (this.forceUpdate1) {
            this.eventSaleTable1['rowsData'] = [];
            this.forceUpdate1 = false;
        }

        if (this.forceUpdate2) {
            this.eventSaleTable2['rowsData'] = [];
            this.forceUpdate2 = false;
        }

        this.saleListFilter = tmpList1;
        this.saleList2Filter = tmpList2;
        this.grid2Expanded = tmpList2.length > 0;
        if (isRefresh) {
            this.refresh();
        }
    }

    cbPendingApprovalChange(checked) {
        this.status = '';
        if (checked) {
            if (this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicket)) {
                this.status = `${PropertyUnitStatus.CONFIRM}, ${PropertyUnitStatus.LOCK_CONFIRM}`;
            } else if (this.user.hasPermission(PropertyTicketPermissionConst.Admin.ApproveTicket)) {
                this.status = `${PropertyUnitStatus.CONFIRM}, ${PropertyUnitStatus.PROCESSING}, ${PropertyUnitStatus.COMING}`;
            }
        }
        this.onChangeKeySearchDSGD();
    }
    async refresh(event?) {
        this.forceUpdate1 = true;
        this.forceUpdate2 = true;
        this.lstSaleProgramId = '';
        this.selectSaleProgramApply = [];
        this.selectSaleProgram = [];
        if (event && event.id) {
            let p = {
                projectId: this.projectId
            }
            this.salesProgram.map((e: any) => {
                if (e.id === event.id) {
                    e.checked = true;
                } else {
                    e.checked = false;
                }
            });
            await this.salesProgramService.getSalesProgramByIdByProjectId(event.id, p).subscribe(res => {
                this.currentSalesProgram = new SalesProgramModel(res);
                this.saleUnitLockConfirmable = this.currentSalesProgram.saleUnitLockConfirmable;
                this.onChangeViewAll();
                this.getAllLstBangHang();
            });
        } else {
            this.onChangeViewAll();
            this.getTableData();
        }
        this.getEventProject();
    }

    refreshUnitDrawn(event?) {
        if (event && event.id) {
            this.salesProgramService.getSalesProgramById(event.id).subscribe(res => {
                this.currentSalesProgram = new SalesProgramModel(res);
            });
        }
    }

    onChangeViewAll() {
        if (this.isViewAllChecked) {
            this.getTableDataHistory();
        } else {
            this.getEventProject();
            this.getSaleListData();
        }
    }


    onCopyYCDCH(ticket) {
        this.openCreateTicket(ticket, true);
    }


    getEventProject() {
        if (!isNullOrUndefined(this.selectedProject.id)) {
            this.projectService.getEventStatus(this.selectedProject.id, this.currentSalesProgram.id).subscribe((p: any) => {
                this.isEnabledEdit = p.orderEditable ? p.orderEditable : false;
                this.isOrderEditableUsed = p.isOrderEditableUsed ? p.isOrderEditableUsed : false;
                this.isEditCustomerInfo = (this.selectedTab === 4 && this.isEnabledEdit);
                this.eventProjectId = p.id ? p.id : '';
                this.projectStage = p.stage;
                this.projectStatus = p.status;
                this.projectStageStatus = p.stageStatus;
                const { status, stageStatus } = p;
                if (status === EEventStatusProjet.STREAMING) {
                    // Sự kiện đang diễn ra
                    if (stageStatus === EEventStatusStageProject.IN_PROGRESS) {
                        // đang trong giai đoạn, lock
                        this.wsStatus = EActiveStatus.LOCK;
                    } else {
                        // nghỉ giữa 2 giai đoạn, open cho user có quyền
                        this.wsStatus = EActiveStatus.OPEN;
                    }
                } else {
                    // Sự kiện chưa bắt đầu hoặc đã kết thúc, set default
                    this.wsStatus = EActiveStatus.DEFAULT;
                }
                if (p.status === EEventStatusProjet.STREAMING) {
                    this.startEvent = true;
                    if (p.stageStatus === EEventStatusStageProject.IN_PROGRESS) {
                        this.startStage = true;
                        if (p.priorityStatus === EEventStatusPriorityProject.IN_PROGRESS) {
                            this.startPriority = true;
                        } else {
                            this.startPriority = false;
                        }
                    } else {
                        this.startStage = false;
                    }

                    if (this.projectStage === 1) {
                        this.grid1Expanded = true;
                    } else {
                        this.grid1Expanded = false;
                    }

                } else {
                    this.startEvent = false;
                    this.grid1Expanded = this.saleListFilter.length > 0;
                    this.grid2Expanded = this.saleList2Filter.length > 0;
                }
                if (p.status === EEventStatusProjet.FINISHED) {
                    this.endEvent = true;
                } else {
                    this.endEvent = false;
                }
                this.priority = p.priority;
            });
        }
    }

    onChangeKeySearchBH(keySearch: string = '') {
        keySearch = keySearch ? keySearch.trim() : '';
        const propertyArrModelFilter = this.ng2SearchPipe.transform(this.propertyArrModel, keySearch);

        this.setupMappingTable(propertyArrModelFilter);
    }

    onDisplayBlockBH(block: { isDisplay: boolean }) {
        block.isDisplay = !block.isDisplay;
    }
    getTicketDisplayName(ticket: TicketSale) {
        let displayName = '';
        if (ticket && ticket.code) {
            displayName = ticket.code; // Default Value
        }
        if (ticket && ticket.ticketType === 'YCDCH') {
            displayName = ticket.bookingTicketCode;
            if (ticket.demandCategory) {
                displayName = ticket.bookingTicketCode + ' (' + ticket.demandCategory + ')';
            }
        } else if (ticket && ticket.ticketType === 'YCDC') {
            displayName = ticket.escrowTicketCode;
            if (ticket.bookingTicketCode) {
                displayName = ticket.escrowTicketCode + ' (' + ticket.bookingTicketCode + ')';
            }
        }


        return displayName;
    }

    async onChangeBookingTicketStatus(ticket: any, status: string, isReason = true, isRequireReason = false) {

        if (status === 'CS_REJECTED_TICKET') {
            const dialogRef = this.dialog.open(ConfirmPopup, {
                width: '650px',
                data: {
                    title: 'Bạn có muốn từ chối yêu cầu này không?',
                    isReason,
                    isRequireReason,
                    textCancel: 'Hủy',
                    textOk: 'Đồng ý',
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
                if (result && result.execute) {
                    this.csApproveRejectTicket(ticket, status, null, null, result.reason);
                } else {
                    ticket.isDisable = false;
                    return false;
                }
            });
        } else if (status === 'CS_APPROVED_TICKET') {
            const posId = ticket.pos.id;
            const salesUnit = ticket.salesUnit.find(item => item.id == posId);
            const empEalesUnit = salesUnit && salesUnit.erpAccount ? salesUnit.erpAccount : '';
            ticket.isDisable = true;
            if (empEalesUnit && !this.isSyncCRMInvestor) {
                await this.getEmployeeErp(ticket, empEalesUnit);
                this.erpService.getUserLogin().subscribe(res => {
                    res = JSON.parse(res)
                    const dialogRef = this.dialog.open(ConfirmErpPopup, {
                        width: '650px',
                        data: {
                            title: 'Bạn đang đăng nhập ERP với tài khoản ' + res.id,
                            textCancel: 'Dùng Tài Khoản Khác',
                            textOk: 'Tiếp tục',
                        }
                    });
                    dialogRef.afterClosed().subscribe((result: any) => {
                        if (result && result.execute) {
                            if (result.isChecked) {
                                this.processSyncERP(ticket, status);
                            } else {
                                this.csApproveRejectTicket(ticket, status, null, null, result.reason);
                            }
                        } else if (result && !result.execute) {
                            this.erpService.logout().then(() => {
                                this.erpService.getAccessToken().then(res => {
                                    return true;
                                })
                            });
                        } else {
                            ticket.isDisable = false;
                            return false;
                        }
                    });
                });
            } else {
                const dialogRef = this.dialog.open(ConfirmPopup, {
                    width: '650px',
                    data: {
                        title: isRequireReason ? 'Bạn có muốn từ chối yêu cầu này không?' : 'Bạn có muốn duyệt yêu cầu này không?',
                        isReason, isRequireReason,
                        textCancel: 'Hủy',
                        textOk: 'Đồng ý',
                    }
                });
                dialogRef.afterClosed().subscribe((result: any) => {
                    if (result && result.execute) {
                        this.csApproveRejectTicket(ticket, status, null, null, result.reason);
                    } else {
                        ticket.isDisable = false;
                        return false;
                    }
                });
            }

        } else if (status === PrimaryTransactionStatus.SUCCESS || status === PrimaryTransactionStatus.CS_REJECTED_ESCROW) {
            const dialogRef = this.dialog.open(ConfirmPopup, {
                width: '650px',
                data: {
                    title: isRequireReason ? 'Bạn có muốn từ chối yêu cầu này không?' : 'Bạn có muốn duyệt yêu cầu này không?',
                    isReason, isRequireReason,
                    textCancel: 'Hủy',
                    textOk: 'Đồng ý',
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
                if (result && result.execute) {
                    this.projectPrimaryTransactionService.updateStatusSaleListDVKH({ id: ticket.id, status, reason: result.reason }).subscribe(res => { })
                } else {
                    ticket.isDisable = false;
                    return false;
                }
            });
        }
    }
    processSyncERP(ticket, status) {
        const body = {
            "action": "FrmFdProcess",
            "method": "getValidation",
            "data": [
                "PRS",
                "C",
                [
                    this.erpService.mapCustomerErpFromTicket(ticket, this.employeeErp.employee)
                ]
            ],
            "type": "rpc",
            "tid": 65
        }
        this.erpService.post(body).subscribe(res => {
            if (res.result.success) {
                // Tạo khách hàng tiềm năng trên ERP
                const body = {
                    "action": "FrmCrProspect",
                    "method": "add",
                    "data": [
                        [
                            this.erpService.mapCustomerErpFromTicket(ticket, this.employeeErp.employee)
                        ]
                    ],
                    "type": "rpc",
                    "tid": 69
                }
                this.erpService.post(body).subscribe(res => {
                    const prospect = res.result.data[0].prospect;
                    this.getSyncErpService(ticket, status, prospect, prospect, 'createCustomer');
                })
            } else {

                // Update KH tiềm năng ERP
                const index = res.result.message.match(/mã khách hàng: (P\d*)/i);
                const maKH = index ? index[1] : null;
                const errMess = res.result.message;
                if (!maKH) {
                    const dialogRef = this.dialog.open(DialogMessageImageComponent, {
                        width: '550px',
                        data: {
                            imageUrl: 'assets/img/dxres/warning.svg',
                            message: res.result.message,
                            title: 'Lỗi ERP'
                        }
                    });
                    dialogRef.afterClosed().subscribe((result: any) => {
                    });
                } else {
                    this.popupCustomerInfo(maKH, ticket, this.employeeErp, status, errMess);
                }
            }
        })
    }

    // Popup KHTN
    popupCustomerInfo(maKH: string, ticket: any, employeeErp, status: string, errMess: string, isReason = true, isRequireReason = false) {
        const employeeCode = this.employeeErp.employee;
        this.erpService.getCustomerByCode(maKH)
            .subscribe(res => {
                this.erpInfo = res.result.data[0];
                const createGroupPopup = this.dialog.open(PopupComfirmERPComponent, {
                    width: '900px',
                    data: {
                        o2oCustomer: ticket,
                        erpInfo: this.erpInfo,
                        errMess: errMess,
                    },
                    disableClose: true,
                    panelClass: 'no-padding-dialog-container'
                });
                createGroupPopup.afterClosed().subscribe((resultPopup: any) => {
                    if (resultPopup) {
                        if (resultPopup.skipCustomer) {
                            this.getSyncErpService(ticket, status, maKH, maKH, 'skipCustomer');
                        } else {
                            // Create /Update KHTN
                            const body = {
                                "action": "FrmFdProcess",
                                "method": "getValidation",
                                "data": [
                                    "PRS",
                                    "C",
                                    [
                                        this.erpService.mapCustomerCheckUpdateForm(ticket, resultPopup.isCreate ? '' : maKH)
                                    ]
                                ],
                                "type": "rpc",
                                "tid": 65
                            }
                            this.erpService.post(body).subscribe(res => {
                                if (res.result.success) {
                                    const body = {
                                        "action": "FrmCrProspect",
                                        "method": resultPopup.isCreate ? "add" : "upd",
                                        "data": [
                                            [
                                                this.erpService.mapCustomerUpdateForm(ticket, resultPopup.isCreate ? '' : maKH, employeeCode)
                                            ]
                                        ],
                                        "type": "rpc",
                                        "tid": 69
                                    }
                                    this.erpService.post(body).subscribe(res => {
                                        const prospect = res.result.data[0].prospect;
                                        maKH = res.result.data[0].prospect;
                                        this.getSyncErpService(ticket, status, prospect, maKH, resultPopup.isCreate ? 'createCustomer' : 'updateCustomer');
                                    })
                                } else if (!res.result.success && res.result.errorType === 'C') {
                                    const dialogRef = this.dialog.open(ConfirmPopup, {
                                        width: '650px',
                                        data: {
                                            title: 'Xác nhận',
                                            isReason: false,
                                            message: res.result.message,
                                            textCancel: 'Hủy',
                                            textOk: 'Xác nhận',
                                        }
                                    });
                                    dialogRef.afterClosed().subscribe((res: any) => {
                                        if (res && res.execute) {
                                            const body = {
                                                "action": "FrmCrProspect",
                                                "method": resultPopup.isCreate ? "add" : "upd",
                                                "data": [
                                                    [
                                                        this.erpService.mapCustomerUpdateForm(ticket, resultPopup.isCreate ? '' : maKH, employeeCode)
                                                    ]
                                                ],
                                                "type": "rpc",
                                                "tid": 69
                                            }
                                            this.erpService.post(body).subscribe(res => {
                                                const prospect = res.result.data[0].prospect;
                                                maKH = res.result.data[0].prospect;
                                                this.getSyncErpService(ticket, status, prospect, maKH, resultPopup.isCreate ? 'createCustomer' : 'updateCustomer');
                                            })
                                        } else {
                                            ticket.isDisable = false;
                                        }
                                    });

                                } else {
                                    const dialogRef = this.dialog.open(DialogMessageImageComponent, {
                                        width: '550px',
                                        data: {
                                            imageUrl: 'assets/img/dxres/warning.svg',
                                            message: `${res.result.message}`,
                                        }
                                    });
                                    dialogRef.afterClosed().subscribe((result: any) => {
                                        if (!result) {
                                            ticket.isDisable = false;
                                        }
                                    });
                                }
                            })
                        }
                    } else {
                        ticket.isDisable = false;
                    }
                });
            })
    }

    async getEmployeeErp(ticket, empEalesUnit) {
        let param = ticket.employee.code ? ticket.employee.code : empEalesUnit;
        if(this.user.pos.erpConfig && ticket.employee.erp &&
         this.user.pos.erpConfig.url === ticket.employee.erp.url) {
            param = ticket.employee.erp.code;
        }
        const data = this.erpService.getDataEmployeeErp(param);
        this.erpService.post(data).subscribe(res => {
            if (res.result.data && res.result.data.length > 0) {
                this.employeeErp = res.result.data[0];
                return true;
            } else {
                const emloyeeId = this.erpService.getDataEmployeeErp(empEalesUnit)
                this.erpService.post(emloyeeId).subscribe(res => {
                    if (res.result.data && res.result.data.length > 0) {
                        this.employeeErp = res.result.data[0];
                        return true;
                    } else {
                        const dialogRef = this.dialog.open(DialogMessageImageComponent, {
                            width: '550px',
                            height: '230px',
                            data: {
                                imageUrl: 'assets/img/dxres/warning.svg',
                                message: `Tư vấn viên này không tồn tại trên hệ thống ERP, vui lòng kiểm tra lại.`
                            }
                        });
                        dialogRef.afterClosed().subscribe((result: any) => {
                            ticket.isDisable = false;
                            return false;
                        });
                    }
                })
            }
        });
    }
    getSyncErpService(ticket, status, prospect, maKH?, actionErp?) {
        this.campaignErp = ticket.demandCategory ? getCampaignErp(ticket.demandCategory) : ticket.campaignErp;
        if (!this.campaignErp) {
            this.dialog.open(DialogMessageNoticeComponent, {
                width: '562px',
                height: '142px',
                panelClass: 'app-dialog',
                disableClose: true,
                data: {
                    message: "Không tìm thấy chương trình bán hàng trên ERP"
                }
            });
            ticket.isDisable = false;
            return;
        }
        const param = this.erpService.mapSyncData(prospect, this.campaignErp, this.employeeErp.employee);
        this.erpService.post(param).subscribe(res => {
            const dataCampaign = res[2].result.data[0];
            const orgchart = res[3].result.data.filter(res => res.status == 'W');
            const orgchartid = orgchart[0].orgchartid;
            const body = {
                "action": "FrmFdUserForm",
                "method": "add",
                "data":
                    [
                        this.erpService.mapCreateYCDCHForm(ticket.bookingTicketCode, dataCampaign, maKH, orgchartid, this.employeeErp, ticket.timestamp)
                    ]
                ,
                "type": "rpc",
                "tid": 68
            }
            this.erpService.post(body).subscribe(res => {
                const systemno = res.result.data[0].systemno;
                const body = {
                    "action": "FrmFdTransfer",
                    "method": "runTransferCode",
                    "data":
                        [
                            this.erpService.approveYCDCH(systemno)
                        ]
                    ,
                    "type": "rpc",
                    "tid": 39
                }
                this.erpService.post(body).subscribe(res => {
                    if (res) {
                        this.csApproveRejectTicket(ticket, status, systemno, maKH, null, actionErp);
                        const dialogRef = this.dialog.open(DialogMessageImageComponent, {
                            width: '550px',
                            height: '230px',
                            data: {
                                imageUrl: 'assets/img/dxres/img-thanhcong.svg',
                                message: `Mã phiếu ${systemno} đã được tạo thành công trên DXS CRM cho mã khách hàng ${maKH}`,
                            }
                        });
                        dialogRef.afterClosed().subscribe((result: any) => {
                        });
                    }

                })
            })
        })
    }
    csApproveRejectTicket(ticket: any, status: string, systemno, maKH?, reason?, actionErp?) {
        this.primaryTransactionService.csApproveRejectTicket({ id: ticket.id, status: status, notes: '', ptCode: '', systemnoErp: systemno, codeCustomerErp: maKH, reason, actionErp }).subscribe(res => {
            const index = this.rowData.findIndex(i => i.id === ticket.id);
            if (index !== -1) {
                this.rowData[index].status = status;
            }
            this.dialog.closeAll()
        });
    }

    isConfirmYCDCO(ticket: TicketSale): boolean {
        return this.isGDSApproveRejectYCDCO(ticket) ||
            this.isSaApproveRejectYCDCO(ticket) ||
            this.isDVKHApproveReject(ticket);
    }

    isGDSApproveReject(ticket: TicketSale): boolean {
        return (ticket.status === PrimaryTransactionStatus.CLOSE && this.canAdminApprovedTicket);
    }

    isGDSApproveRejectYCDCO(ticket: TicketSale): boolean {
        return (ticket.ticketType === "YCDC" && this.canAdminApprovedTicket && ticket.status === PrimaryTransactionStatus.PROCESSING_UNPAID);
    }
    isSaApproveRejectYCDCO(ticket: TicketSale): boolean {
        return (ticket.ticketType === "YCDC" && this.canSaApprovedTicket && ticket.status === PrimaryTransactionStatus.POS_CONFIRM_UNPAID);
    }

    isDVKHApproveReject(ticket: TicketSale): boolean {
        return (ticket.status === PrimaryTransactionStatus.DEPOSIT_APPROVED && this.canCsApprovedTicket);
    }

    isExtendPriority(unit: PropertyTable, index: number): boolean {
        return unit.extendable && !unit.priorities[index].id &&
            (unit.extendPosId === this.user.pos.id ||
                unit.extendPosId === this.user.pos.parentId ||
                !unit.extendPosId || (this.user.f1San && unit.extendPosId === this.user.f1San.id) ||
                this.canMatchAllUnit);
    }

    onYCDCHAdminApproveReject(ticket: TicketSale, unitId: string, statusUpdate: string, isReason = true, isRequireReason = false) {

        if (statusUpdate === PrimaryTransactionStatus.POS_REJECT_UNPAID) {
            const dialogRef = this.dialog.open(ConfirmPopup, {
                width: '650px',
                data: {
                    title: 'Bạn có muốn từ chối yêu cầu này không?',
                    isReason, isRequireReason,
                    textCancel: 'Hủy',
                    textOk: 'Đồng ý',
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
                if (result && result.execute) {
                    this.primaryTransactionService
                        .adminApproveRejectTicket({ id: ticket.id, status: statusUpdate, reason: result.reason, propertyUnitId: unitId })
                        .subscribe((res: any) => {
                            ticket.status = statusUpdate;
                        });
                } else {
                    return false;
                }
            });
        } else {
            const dialogRef = this.dialog.open(ConfirmPopup, {
                width: '650px',
                data: {
                    title: isRequireReason ? 'Bạn có muốn từ chối yêu cầu này không?' : 'Bạn có muốn duyệt yêu cầu này không?',
                    isReason, isRequireReason,
                    textCancel: 'Hủy',
                    textOk: 'Đồng ý',
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
                if (result && result.execute) {
                    this.primaryTransactionService
                        .adminApproveRejectTicket({ id: ticket.id, status: statusUpdate, reason: result.reason, propertyUnitId: unitId })
                        .subscribe((res: any) => {
                            ticket.status = statusUpdate;
                            this.dialog.closeAll();
                        });
                } else {
                    ticket.isDisable = false;
                    return false;
                }
            });
        }

    }
    onSaApproveReject(ticket: TicketSale, statusUpdate: PrimaryTransactionStatus.SA_CONFIRM_UNPAID | PrimaryTransactionStatus.SA_REJECT_UNPAID, isReason = true, isRequireReason = false) {

        if (statusUpdate === PrimaryTransactionStatus.SA_REJECT_UNPAID) {
            const dialogRef = this.dialog.open(ConfirmPopup, {
                width: '650px',
                data: {
                    title: 'Bạn có muốn từ chối yêu cầu này không?',
                    isReason, isRequireReason,
                    textCancel: 'Hủy',
                    textOk: 'Đồng ý',
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
                if (result && result.execute) {
                    this.primaryTransactionService
                        .saApproveRejectTicket({ id: ticket.id, status: statusUpdate, reason: result.reason })
                        .subscribe((res: any) => {
                            ticket.status = statusUpdate;
                        });
                } else {
                    return false;
                }
            });
        } else {
            const dialogRef = this.dialog.open(ConfirmPopup, {
                width: '650px',
                data: {
                    title: isRequireReason ? 'Bạn có muốn từ chối yêu cầu này không?' : 'Bạn có muốn duyệt yêu cầu này không?',
                    isReason, isRequireReason,
                    textCancel: 'Hủy',
                    textOk: 'Đồng ý',
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
                if (result && result.execute) {
                    this.primaryTransactionService
                        .saApproveRejectTicket({ id: ticket.id, status: statusUpdate, reason: result.reason })
                        .subscribe((res: any) => {
                            ticket.status = statusUpdate;
                        });
                } else {
                    ticket.isDisable = false;
                    return false;
                }
            });
        }

    }
    onSyncBookingTicket(ticket: TicketSale, isCancel = false) {
        this.syncBooking(ticket, isCancel);
    }
    onSyncEscrowTicket(ticket: TicketSale, isCancel = false) {
        this.syncEscrow(ticket, isCancel);
    }

    onSuggestCancelYCDCH(ticket: TicketSale, isReason = true, isRequireReason = false) {
        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: 'Bạn có muốn huỷ yêu cầu này không?',
                isReason, isRequireReason,
                textCancel: 'Hủy',
                textOk: 'Đồng ý',
                isCancelTicket: true,
                bankInfo: ticket.customer ? ticket.customer.bankInfo : null
            }
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                const params = {
                    id: ticket.id,
                    reason: result.reason,
                    bankInfo: result.bankInfo,
                };
                this.primaryTransactionService.cancelRequestYCDCH(params).subscribe((res: any) => {
                    this.dialog.closeAll();
                });
            } else {
                ticket.isDisable = false;
                return false;
            }
        });
    }

    onCancelYCDCH(ticket: TicketSale, isReason = true, isRequireReason = false) {
        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: 'Bạn có muốn huỷ không?',
                isReason, isRequireReason,
                textCancel: 'Hủy',
                textOk: 'Đồng ý',
            }
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                this.primaryTransactionService
                    .cancelYCDCH({ id: ticket.id, reason: result.reason })
                    .subscribe((res: any) => {
                        ticket.status = PrimaryTransactionStatus.TICKET_CANCELLED;
                    });
            } else {
                ticket.isDisable = false;
                return false;
            }
        });
    }

    onAdminApproveCancelRequestYCDCH(ticket: TicketSale, isReason = true, isRequireReason = false) {
        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: 'Bạn có muốn duyệt đề nghị huỷ này không?',
                isReason, isRequireReason,
                textCancel: 'Hủy',
                textOk: 'Đồng ý',
            }
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                this.primaryTransactionService
                    .adminApproveCancelYCDCH({ id: ticket.id, reason: result.reason })
                    .subscribe((res: any) => {
                        ticket.status = PrimaryTransactionStatus.ADMIN_APPROVED_CANCEL_REQUESTED;
                        this.dialog.closeAll();
                    });
            } else {
                return false;
            }
        });
    }

    onAdminCancelRequestYCDCH(ticket: TicketSale, isReason = true, isRequireReason = false) {
        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: 'Bạn có muốn từ chối đề nghị huỷ này không?',
                isReason, isRequireReason,
                textCancel: 'Hủy',
                textOk: 'Đồng ý',
            }
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                this.primaryTransactionService
                    .adminCancelYCDCH({ id: ticket.id, reason: result.reason })
                    .subscribe((res: any) => {
                        ticket.status = PrimaryTransactionStatus.BOOKING_APPROVED;
                        this.dialog.closeAll();
                    });
            } else {
                return false;
            }
        });
    }

    onDVKHApproveCancelRequestYCDCH(ticket: TicketSale, isReason = true, isRequireReason = false) {

        if (!isNullOrUndefined(ticket.systemnoErp) && ticket.systemnoErp && !this.isSyncCRMInvestor) {

            this.erpService.getUserLogin().subscribe(res => {
                res = JSON.parse(res)
                const dialogRef = this.dialog.open(ConfirmErpPopup, {
                    width: '650px',
                    data: {
                        title: 'Bạn đang đăng nhập ERP với tài khoản ' + res.id,
                        textCancel: 'Dùng Tài Khoản Khác',
                        textOk: 'Tiếp Tục',
                    }
                });
                dialogRef.afterClosed().subscribe((result: any) => {
                    if (result && result.execute) {

                        const dialogRef = this.dialog.open(ConfirmPopup, {
                            width: '650px',
                            data: {
                                title: 'Bạn có muốn duyệt đề nghị huỷ này không?',
                                isReason, isRequireReason,
                                textCancel: 'Hủy',
                                textOk: 'Đồng ý',
                            }
                        });
                        dialogRef.afterClosed().subscribe((resultPopupConfirm: any) => {
                            if (resultPopupConfirm && resultPopupConfirm.execute) {
                                if (result.isChecked) {
                                    const dataErp = this.erpService.getDataEmployeeErp(res.emloyee);
                                    this.erpService.post(dataErp).subscribe(res => {
                                        let employeeErpLogin;
                                        if (res.result.data && res.result.data.length > 0) {
                                            employeeErpLogin = res.result.data[0].name;
                                        }
                                        this.erpService.createYCHDCH(ticket.systemnoErp, employeeErpLogin).subscribe((res: any) => {
                                            if (res.result.success) {
                                                this.primaryTransactionService
                                                    .dvkhApproveCancelYCDCH({ id: ticket.id, reason: resultPopupConfirm.reason })
                                                    .subscribe((res: any) => {
                                                        ticket.status = PrimaryTransactionStatus.CS_APPROVED_CANCEL_REQUESTED;
                                                    });
                                            }
                                        })
                                    });
                                } else {
                                    this.primaryTransactionService
                                        .dvkhApproveCancelYCDCH({ id: ticket.id, reason: resultPopupConfirm.reason })
                                        .subscribe((res: any) => {
                                            ticket.status = PrimaryTransactionStatus.CS_APPROVED_CANCEL_REQUESTED;
                                        });
                                }
                            } else {
                                ticket.isDisable = false;
                                return false;
                            }
                        });
                    } else if (result && !result.execute) {
                        this.erpService.logout().then(() => {
                            this.erpService.getAccessToken().then(res => {
                                return true;
                            })
                        });
                    } else {
                        return false;
                    }
                });
            });
        } else {
            const dialogRef = this.dialog.open(ConfirmPopup, {
                width: '650px',
                data: {
                    title: 'Bạn có muốn duyệt đề nghị huỷ này không?',
                    isReason, isRequireReason,
                    textCancel: 'Hủy',
                    textOk: 'Đồng ý',
                }
            });
            dialogRef.afterClosed().subscribe((result: any) => {
                if (result && result.execute) {
                    this.primaryTransactionService
                        .dvkhApproveCancelYCDCH({ id: ticket.id, reason: result.reason })
                        .subscribe((res: any) => {
                            ticket.status = PrimaryTransactionStatus.CS_APPROVED_CANCEL_REQUESTED;
                            this.dialog.closeAll();
                        });
                } else {
                    ticket.isDisable = false;
                    return false;
                }
            });
        }
    }

    onDVKHCancelRequestYCDCH(ticket: TicketSale, isReason = true, isRequireReason = false) {
        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: 'Bạn có muốn từ chối đề nghị huỷ này không?',
                isReason, isRequireReason,
                textCancel: 'Hủy',
                textOk: 'Đồng ý',
            }
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                this.primaryTransactionService
                    .csCancelYCDCH({ id: ticket.id, reason: result.reason })
                    .subscribe((res: any) => {
                        ticket.status = PrimaryTransactionStatus.CANCEL_REQUESTED;
                        this.dialog.closeAll();
                    });
            } else {
                return false;
            }
        });
    }

    extendBookingTime(ticket: TicketSale) {

        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: `GIA HẠN THỜI GIAN TRẢ VỀ`,
                message: `(Thời gian trả về: <strong>${ticket.expiredTime}</strong>)`,
                textInputLabel: 'Thêm thời gian trả về(phút)',
                textInputValue: '60',
                isTextInput: true,
                textCancel: 'Hủy',
                textOk: 'Đồng ý',
            }
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                this.primaryTransactionService
                    .extendBookingTime({ ticketId: ticket.id, dWellMinute: result.textInputValue })
                    .subscribe((res: any) => {
                        if (res && res.success) {
                            this.dialog.closeAll();
                            this.toastr.success('Thêm thời gian trả về', 'Thành công! ');
                        }
                    });
            } else {
                return false;
            }
        });

    }
    openProcess(type: string = 'BookingProcess') {
        const createGroupPopup = this.dialog.open(PopupProcessComponent, {
            width: (type === 'RefundProcess' && this.isSyncERP) ? '745px' : '900px',
            data: {
                type: type,
                isSyncERP: this.isSyncERP,
            },
            disableClose: true,
            panelClass: 'no-padding-dialog-container'
        });
        createGroupPopup.afterClosed().subscribe((result: boolean) => {
            if (result) {
            }
        });
    }

    openProcessRefund(type: string = 'RefundProcess') {
        this.openProcess(type);
    }
    openProcessDeposit(type: string = 'DepositProcess') {
        this.openProcess(type);
    }
    extendPriorities() {
        const popupRef = this.dialog.open(PopupExtendPriorityComponent, {
            width: '600px',
            panelClass: 'app-dialog',
            disableClose: true,
            data: {
                projectId: this.projectId,
                listUnit: this.listExtendUnit,
                salesUnit: this.pos
            }
        });

        popupRef.afterClosed().subscribe(res => {
            if (res && res.isSuccess) {
                this.dialog.open(DialogMessageNoticeComponent, {
                    width: '562px',
                    height: '142px',
                    panelClass: 'app-dialog',
                    data: {
                        message: 'Bổ sung ưu tiên thành công'
                    }
                });
                this.listExtendUnit = [];
                // this.getTableData();
            }
        });
    }
    sortPriorities() {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: '650px',
            data: {
                title: 'Bạn có muốn sắp xếp lại ưu tiên?',
                isReason: false,
                isRequireReason: false,
                textCancel: 'Hủy',
                textOk: 'Đồng ý',
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res && res.execute) {
                const params = {
                    salesProgramId: this.currentSalesProgram.id
                };
                this.propertyService.sortPriorities(this.project.id, params)
                    .subscribe((res: any) => {
                        this.dialog.open(DialogMessageNoticeComponent, {
                            width: '562px',
                            height: '142px',
                            panelClass: 'app-dialog',
                            data: {
                                message: 'Sắp xếp ưu tiên thành công'
                            }
                        });
                    });
            }
        });
    }
    onChangeCheckboxAll(event) {
        if (event) {
            this.listExtendUnit = this.propertyArrModelFilter.filter(x => x.posId !== this.rootPos && !x.extendable && x.primaryStatus !== 'SUCCESS' && !(x.priorities[0].id && x.priorities[1].id && x.priorities[2].id));
            this.listExtendUnit.map(x => x.cbExtendtion = true);
        } else {
            this.propertyArrModelFilter.map(x => x.cbExtendtion = false);
            this.listExtendUnit = [];
        }
    }
    onChangeCheckbox(event, item) {
        if (event) {
            this.listExtendUnit.push(item);
            const allPropertyArrModelFilter = this.propertyArrModelFilter.filter(x => !x.extendable && x.primaryStatus !== 'SUCCESS' && !(x.priorities[0].id && x.priorities[1].id && x.priorities[2].id));
            if (allPropertyArrModelFilter.length === this.listExtendUnit.length) {
                this.cbPriorityAll = true;
            }
        } else {
            const index = this.listExtendUnit.findIndex(x => x.id === item.id);
            this.listExtendUnit.splice(index, 1);
            if (this.cbPriorityAll) {
                this.cbPriorityAll = false;
            }
        }
    }
    onChangeCBExtendPriority(event) {
        this.extensionPriorityFilter = event;
        if (this.unitTable) {
            this.unitTable['refeshUI'] = true;
            this.isRefeshUnitTable = true;
        }
        this.setupMappingTable(this.propertyArrModel);
    }
    onChangeDate() {
        const date = moment(this.endDate.toString(), 'DD-MM-YYYY HH:mm:ss').valueOf();
        this.createdTo = date;
    }

    onChangeStartDate() {
        const date = moment(this.startDate.toString(), 'DD-MM-YYYY HH:mm:ss').valueOf();
        this.createdFrom = date;
    }

    downloadUpdatePrioritiesTemplate() {
        let p: any = {};
        p.projectId = this.project.id;
        p.createdTo = this.createdTo;
        p.createdFrom = this.createdFrom;
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            p.salesProgramIds = this.lstSaleProgramId;
        } else {
            p.salesProgramId = this.currentSalesProgram.id;
        }
        // p.salesProgramId = this.currentSalesProgram.id;
        this.isDownloadLoadingAll$.next(true);
        this.propertyService.downloadUpdatePrioritiesTemplate(p).subscribe(res => {
            this.isDownloadLoadingAll$.next(false);
        });
    }

    downloadTemplateYCDCO(url, outFile) {
        const sheetNames = [];
        let dataValidations = [];
        let collections = [];
        dataValidations = [
            { cell: 'F3', formula: ['Config!$B$1:$B$2'] },
            { cell: 'F4', formula: ['Config!$B$1:$B$2'] },
            { cell: 'AI3', formula: ['Config!$B$1:$B$2'] },
            { cell: 'AI4', formula: ['Config!$B$1:$B$2'] },
            { cell: 'AE3', formula: ['Config!$A$1:$A$7'] },
            { cell: 'AE4', formula: ['Config!$A$1:$A$7'] },
        ];
        this.excelService.downloadTemplate(url, outFile, sheetNames, dataValidations, collections)
            .then((res) => {
                // this.isTableLoading$.next(false);
            });
    }

    downloadMatchingPriorities() {
        let p: any = {};
        p.projectId = this.project.id;
        p.createdTo = this.createdTo;
        p.createdFrom = this.createdFrom;
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            p.salesProgramIds = this.lstSaleProgramId;
        } else {
            p.salesProgramId = this.currentSalesProgram.id;
        }
        this.isDownloadLoadingAll$.next(true);
        // const fileName = 'Danh_sach_rap_uu_tien' + getCurrentTimeSigned();
        this.propertyService.dowloadMatchingPriorities(p).subscribe(res => {
            this.isDownloadLoadingAll$.next(false);
        });
    }
    downloadKHTN() {
        let p: any = {};
        p.projectId = this.project.id;
        if (this.createdTo) {
            p.createdTo = this.createdTo;
        }
        if (this.createdFrom) {
            p.createdFrom = this.createdFrom;
        }
        this.isDownloadLoadingAll$.next(true);
        // const fileName = 'Danh_sach_khtn' + new Date().getTime();
        this.primaryTransactionService.dowloadKHTN(p).subscribe(res => {
            this.isDownloadLoadingAll$.next(false);
        });
    }
    downloadPrioritiesOfPos() {
        let p: any = {};
        p.projectId = this.project.id;
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            p.salesProgramIds = this.lstSaleProgramId;
        } else {
            p.salesProgramId = this.currentSalesProgram.id;
        }
        this.isDownloadLoadingAll$.next(true);
        // const fileName = 'Danh_sach_so_luong_uom_san_pham' + getCurrentTimeSigned();
        this.propertyService.dowloadPrioritiesOfPos(p).subscribe(res => {
            this.isDownloadLoadingAll$.next(false);
        });
    }

    sendEmailEndPriority() {
        if (this.countSalesProgramChoose !== 1) {
            this.toastrService.error('Vui lòng chọn 1 CTBH cụ thể để sử dụng chức năng này');
            return;
        }
        const popupEmailRef = this.dialog.open(PopupEmailComponent, {
            width: '787px',
            height: '614px',
            panelClass: 'app-dialog',
            disableClose: true,
            autoFocus: false,
            data: {
                projectId: this.projectId,
                stage: this.projectStage || '',
                salesProgramId: this.currentSalesProgram.id
            }
        });

        popupEmailRef.afterClosed().subscribe(res => {
            if (res && res.isSuccess) {
                let p: any = {};
                p.projectId = this.project.id;
                p.salesProgramId = this.currentSalesProgram.id;
                this.projectService.sendEmailEndPriority(p)
                    .pipe(takeUntil(this.unsubscribe$))
                    .subscribe(res => {
                        if (res && res.success) {
                            this.dialog.open(DialogMessageNoticeComponent, {
                                width: '562px',
                                height: '142px',
                                panelClass: 'app-dialog',
                                disableClose: true,
                                data: {
                                    message: "Gửi email xác nhận xác nhận UT thành công"
                                }
                            });
                        } else {
                            this.dialog.open(DialogMessageNoticeComponent, {
                                width: '562px',
                                height: '142px',
                                panelClass: 'app-dialog',
                                disableClose: true,
                                data: {
                                    message: "Có lỗi vui lòng thao tác lại"
                                }
                            });
                        }
                    }), error => {
                        this.dialog.open(DialogMessageNoticeComponent, {
                            width: '562px',
                            height: '142px',
                            panelClass: 'app-dialog',
                            disableClose: true,
                            data: {
                                message: "Có lỗi vui lòng thao tác lại"
                            }
                        });
                    };
            }
        });
    }

    showCheckboxExtendablePos(item) {
        if (this.currentSalesProgram.lock
            && item.primaryStatus !== 'SUCCESS'
            && this.canLockSaleList
            && !item.extendable
        ) {
            if (item.priorities) {
                if (item.priorities[0].id && item.priorities[1].id && item.priorities[2].id) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    onClickCheckBoxChange(checked: boolean, item) {
        item.checked = checked;
        if (item && item.id === Constant.PRIORITY_ID.LOADING) {
            this.selectedPriorities = [];
            this.filterPriorityOptions.map((e: any) => e.id === Constant.PRIORITY_ID.LOADING ? e.checked = checked : e.checked = false)
            this.getAllData(checked);
        } else {
            if (item && item.id === Constant.PRIORITY_ID.EMPTY) {
                this.selectedPriorities = [];
                this.filterPriorityOptions.map((e: any) => e.id === Constant.PRIORITY_ID.EMPTY ? e.checked = checked : e.checked = false)
            } else {
                this.selectedPriorities = this.selectedPriorities.filter((el) => (el === Constant.PRIORITY_ID.UT1 || el === Constant.PRIORITY_ID.UT2 || el === Constant.PRIORITY_ID.UT3));
                this.filterPriorityOptions.forEach((e: any) => {
                    if (e.id === Constant.PRIORITY_ID.LOADING || e.id === Constant.PRIORITY_ID.EMPTY) {
                        e.checked = false;
                    }
                })
            }
            if (checked) {
                this.selectedPriorities = this.selectedPriorities.concat(item.id);
            } else {
                this.selectedPriorities = this.selectedPriorities.filter((el) => item.id !== el);
            }
            this.onSearchFilterUT();
            this.onChangeCBExtendPriority(false);
        }
    }

    onSearchFilterUT() {
        this.selectedPriorities = this.selectedPriorities.sort();
        this.onFilterPriority(this.selectedPriorities.toString() || '');
    }

    onClickViewFilter() {
        this.selectedPriorities = this.filterBangHang.priority ? this.filterBangHang.priority.split(',') : [];
    }

    onRevoveFilterPriority() {
        this.filterBangHang.priority = '';
        this.selectedPriorities = [];
        this.onSearchFilterUT();
    }
    importTicketEscrow(oEvent) {
        this.isTableLoading$.next(true);
        const selectedFile = oEvent.target.files[0];
        if (!this.checkFileExtension(selectedFile)) {
            this.toastr.error('Lỗi!', 'Loại tệp tin không hợp lệ. Tệp được tải nhập phải là loại .xls hoặc .xlsx.');
            return;
        }
        const body = {
            projectId: this.selectedProject.id,
            salesProgramId: this.currentSalesProgram.id
        }
        return this.primaryTransactionService.importTicketEscrow(selectedFile, body).subscribe((res: any) => {
            if (res.status < 400) {

                const result = res._body;
                if (result.errors) {
                    this.toastr.error(result.errors.message);
                    return;
                }
                this.toastr.success('Đang tải lên', 'Quản lý chuyển cọc! ');
            } else {
                this.toastr.error('Lỗi');
            }
        });
    }
    downloadTemplateChuyenCoc() {
        let link = document.createElement("a");
        link.download = "Template-ChuyenCoc";
        link.href = "assets/excel/Template-ChuyenCoc.xlsx";
        link.click();
    }
    handleDownload(event) {
        this.primaryTransactionService.downloadTicket(event.id, event.escrowTicketCode ? event.escrowTicketCode : event.bookingTicketCode, { url: event.file.absoluteUrl }).subscribe(res => {
        });
    }
    createCustomerAccount() {
        const params = { salesProgramId: this.currentSalesProgram.id };
        return this.primaryTransactionService.createCustomerAccount(this.selectedProject.id, params).subscribe((res: any) => {
            this.toastr.success('Đang tạo Tài Khoản', 'Quản lý Khách Hàng! ');
        });
    }
    downloadDanhsachGD() {
        const url = this.getTemplateFileByType('BANG_KE_XAC_NHAN_GD');
        let salesProgramId: any
        if (this.selectSaleProgramApply.length > 1) {
            salesProgramId = this.selectSaleProgramApply;
        }
        else {
            salesProgramId = this.currentSalesProgram.id;
        }
        if (url) {
            this.isDownloadLoadingAll$.next(true);
            const params = {
                projectId: this.selectedProject.id,
                stage: this.projectStage,
                url: url,
                salesProgramId: salesProgramId
            }
            this.primaryTransactionService.downloadDanhsachGD(params).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
            });
        } else {
            this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
        }
    }
    downloadDmsp() {
        const fileName = 'DMSP' + this.selectedProject.name + '_' + getCurrentTimeSigned();
        const propertyTable = new PropertyTable({
            category: { id: this.categories[this.selectedTab] },
        });
        let sheets = [];
        this.isDownloadLoadingAll$.next(true);
        let params: any = {
            categoryId: this.categoryId,
            projectId: this.selectedProject ? this.selectedProject.id : this.projectId,
            priority: this.filterBangHang.priority,
            primaryStatus: [PropertyUnitStatus.LOCK, PropertyUnitStatus.CLOSE, PropertyUnitStatus.COMING, PropertyUnitStatus.PROCESSING, PropertyUnitStatus.UNSUCCESS, PropertyUnitStatus.CANCEL],
            posId: this.filterBangHang.posId,
            _fields: `id,project.name,project.id,pos.name,pos.id,code,primaryStatus,priceAbove,priceAboveVat,price,priceVat,housePriceVat,landPriceVat,housePrice,landPrice,publishPrice,view1,attributes,contractPrice,contractPriceForMaintenanceFee`,
            _fields_attributes : AttributeEnum.view1
        };
        if (this.lstSaleProgramId && this.lstSaleProgramId !== '') {
            params.salesProgramIds = this.lstSaleProgramId;
        } else {
            params.salesProgramId = this.currentSalesProgram.id;
        }
        if (this.user.hasPermission(PropertyUnitPermissionConst.PropertyPrimaryUpdatePriceForce)) {
            params.primaryStatus.push( PropertyUnitStatus.SUCCESS, PropertyUnitStatus.LOCK_CONFIRM ,PropertyUnitStatus.CONFIRM );
        }
        this.projectService.getTable(params).pipe(
            finalize(() => this.isDownloadLoadingAll$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
            // sheet 1
            const sheet1 = {
                headers: propertyTable.getDmspHeaders(),
                hideRows: propertyTable.getDmspHideRows(),
                data: res.map((item: any, idx) => {
                    item.stt = (idx + 1);
                    return new PropertyTable(item);
                }),
                sheetName: 'Template'
            };
            sheets.push(sheet1);
            this.excelService.exportAsExcelFile(sheets, fileName);
        });
    }
    downloadDanhsachSPGD() {
        this.isDownloadLoadingAll$.next(true);
        let salesProgramId: any;
        if (this.selectSaleProgramApply.length > 1) {
            salesProgramId = this.selectSaleProgramApply;
        }
        else {
            salesProgramId = this.currentSalesProgram.id;
        }
        const params = {
            projectId: this.selectedProject.id,
            salesProgramId: salesProgramId,
        }
        this.primaryTransactionService.downloadDanhsachSPGD(params).pipe(
            finalize(() => this.isDownloadLoadingAll$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
        });
    }
    downloadDanhsachKHGD() {
        const url = this.getTemplateFileByType('BANG_KE_KH_GD');
        if (url) {
            this.isDownloadLoadingAll$.next(true);
            const params = {
                projectId: this.selectedProject.id,
                salesProgramId: this.currentSalesProgram.id,
                url: url
            }
            this.primaryTransactionService.downloadDanhsachKHGD(params).pipe(
                finalize(() => this.isDownloadLoadingAll$.next(false)),
                takeUntil(this.unsubscribe$)
            ).subscribe(res => {
            });
        } else {
            this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
        }
    }
    getTemplateFileByType(type) {
        if (this.selectedProject && this.selectedProject.setting && this.selectedProject.setting.templateFileHasType) {
            const fileTemplate = this.selectedProject.setting.templateFileHasType.find(item => item.type === type);
            if (fileTemplate) {
                return fileTemplate.file.absoluteUrl;
            }
        }
        // this.toastr.error('Không tìm thấy file mẫu', 'Quản lý File! ');
    }

    onColumnResized(params) {
        params.api.resetRowHeights();
    }

    onChangeCbExeptionalUnit(checked) {
        this.exceptionOnlyFilter = checked;
        if (checked) {
            this.onChangeKeySearchDSGD();
        } else {
            this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);
        }
    }

    onFilterUnitDSGD(event: Event, status?: string) {
        event.stopPropagation();
        this.forceUpdate1 = true;
        if (status !== this.statusFilterUnitDSGD) {
            this.statusFilterUnitDSGD = status;
        }
        this.onChangeKeySearchDSGD();
    }

    onFilterFreeDSGD(event?: Event, status?: string) {
        if (event) {
            event.stopPropagation();
        }
        this.forceUpdate2 = true;
        if (status !== this.statusFilterFreeDSGD) {
            this.statusFilterFreeDSGD = status;
        }
        this.onChangeKeySearchDSGD();
    }

    printFileStatus() {
        const params = {
            categoryId: this.categoryId,
            projectId: this.selectedProject ? this.selectedProject.id : this.projectId,
            primaryStatus: this.filterBangHang.primaryStatus,
            posId: this.selectPos.toString() || '',
            url: this.templateFileHasStatus[0].file.absoluteUrl,
            status: this.templateFileHasStatus[0].status,
            skip: this.pdfStart,
            limit: this.pdfEnd,
            salesProgramId: this.currentSalesProgram.id
        };
        const dialogRef = (msg: string) => this.dialog.open(DialogMessageImageComponent, {
            width: '550px',
            data: {
                imageUrl: 'assets/img/dxres/warning.svg',
                message: msg,
                title: ''
            }
        });
        if (this.selectPos.length > 0) {
            if (this.currentSalesProgram && this.currentSalesProgram.notSetPriority) {
                this.isDownloadTicketLoadingTemplate$.next(true);
                this.projectService.printPropertyByQuery(params)
                .subscribe(() => {
                    this.isDownloadTicketLoadingTemplate$.next(false);
                });
            } else {
                if (this.selectedPriorities.length > 0 && !this.selectedPriorities.includes(Constant.PRIORITY_ID.EMPTY) && !this.selectedPriorities.includes(Constant.PRIORITY_ID.LOADING)) {
                    params['priority'] = this.selectedPriorities.toString() || '';
                    this.isDownloadTicketLoadingTemplate$.next(true);
                    this.projectPrimaryTransactionService.printTicketByQuery(params)
                    .subscribe(() => {
                        this.isDownloadTicketLoadingTemplate$.next(false);
                    });
                } else {
                    dialogRef('Vui lòng chọn ưu tiên cần tải về').afterClosed().subscribe((result: any) => {
                    });
                }
            }
        } else {
            dialogRef('Vui lòng chọn đơn vị bán hàng cần tải về').afterClosed().subscribe((result: any) => {
            });
        }
    }

    downloadFileStatus() {
        const params = {
            categoryId: this.categoryId,
            projectId: this.selectedProject ? this.selectedProject.id : this.projectId,
            primaryStatus: this.filterBangHang.primaryStatus,
            posId: this.selectPos.toString() || '',
            url: this.templateFileHasStatus[0].file.absoluteUrl,
            status: this.templateFileHasStatus[0].status,
            skip: this.pdfStart,
            limit: this.pdfEnd,
            salesProgramId: this.currentSalesProgram.id
        };
        let fileName = getShortName(this.templateFileHasStatus[0].file.name) + getCurrentTimeSigned();
        const dialogRef = (msg: string) => this.dialog.open(DialogMessageImageComponent, {
            width: '550px',
            data: {
                imageUrl: 'assets/img/dxres/warning.svg',
                message: msg,
                title: ''
            }
        });
        if (this.selectPos.length > 0) {
            if (this.currentSalesProgram && this.currentSalesProgram.notSetPriority) {
                this.isDownloadTicketLoadingTemplate$.next(true);
                this.projectService.downloadPropertyByQuery(params, fileName)
                .subscribe(() => {
                    this.isDownloadTicketLoadingTemplate$.next(false);
                });
            } else {
                if (this.selectedPriorities.length > 0 && !this.selectedPriorities.includes(Constant.PRIORITY_ID.EMPTY) && !this.selectedPriorities.includes(Constant.PRIORITY_ID.LOADING)) {
                    params['priority'] = this.selectedPriorities.toString() || '';
                    this.isDownloadTicketLoadingTemplate$.next(true);
                    this.projectPrimaryTransactionService.downloadTicketByQuery(params, fileName)
                    .subscribe(() => {
                        this.isDownloadTicketLoadingTemplate$.next(false);
                    });
                } else {
                    dialogRef('Vui lòng chọn ưu tiên cần tải về').afterClosed().subscribe((result: any) => {
                    });
                }
            }
        } else {
            dialogRef('Vui lòng chọn đơn vị bán hàng cần tải về').afterClosed().subscribe((result: any) => {
            });
        }
    }

    // PRIVATE FUNCTION
    // handle websocket
    private _wsHandlePrimaryTransUpdatedAdmin(mesData) {
        try {
            if (this.project.id === mesData.projectId) {
                const ticket: any = mesData.ticket;
                const unit: any = mesData.unit;
                let noChange: boolean = false;
                if (this.rowData && ticket) {
                    const index1 = this.rowData.findIndex(i => i.id === ticket.id);
                    if (index1 !== -1) {
                        this.rowData[index1].status = ticket.status;
                        this.rowData[index1].statusString = TXN_STATUS[ticket.status];
                        this.rowData[index1].systemnoErp = ticket.systemnoErp;
                        this.rowData[index1].reciept = ticket.reciept;
                        this.rowData[index1].setRecieptStatus();

                        let rowNode = this.gridOptions.api.getRowNode(ticket.id);
                        if (ticket.ticketType === 'YCDC') {
                            rowNode = this.gridOptionsYCDCO.api.getRowNode(ticket.id);

                        }
                        this.gridOptions.api.flashCells({ rowNodes: [rowNode] });
                    }
                }
                if (unit) {
                    const indexSaleList1 = this.saleList.findIndex(i => i.id === unit.id);
                    if (indexSaleList1 > -1) {
                        if (ticket) {
                            const currentTicket = this.saleList[indexSaleList1].priorities.find(x => x.id === ticket.id);
                            const priority  = (unit.priorities || []).find(x => x.id === ticket.id);
                            if (currentTicket) {
                                currentTicket.status = ticket.status;
                                currentTicket.smsOtp = ticket.smsOtp;
                                currentTicket.customerConfirmDate = ticket.customerConfirmDate;
                                currentTicket.profileConfirmDate = ticket.profileConfirmDate;
                                currentTicket.unitConfirmDate = ticket.unitConfirmDate;
                                currentTicket.tooltipSms = priority.tooltipSms ;
                                currentTicket.customerSmsExpiredDate = priority.customerSmsExpiredDate;
                                currentTicket.serviceConfirmDate = ticket.serviceConfirmDate;
                            }
                        }
                        this.saleList[indexSaleList1].priorities = unit.priorities;
                        this.saleList[indexSaleList1].primaryStatus = unit.primaryStatus;
                        this.saleList[indexSaleList1].description = unit.description;
                        this.saleList[indexSaleList1].isException = unit.isException;
                        this.saleList[indexSaleList1].exceptionalReason = unit.exceptionalReason;
                        this.saleList[indexSaleList1].modifiedDate = unit.modifiedDate;
                        this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);
                        this.changedRowIds1.push(unit.id);
                        this.changedRowIds1 = this.changedRowIds1.slice();
                    } else {
                        if (this.saleList2 && this.saleList2.length > 0) {
                            const indexSaleList2 = this.saleList2.findIndex(i => i.id === unit.id);
                            if (indexSaleList2 > -1) {
                                this.saleList2[indexSaleList2].priorities = unit.priorities;
                                this.saleList2[indexSaleList2].priority0 = unit.priorities && unit.priorities.find(x => (x.priority === 0 || !x.priority));
                                this.saleList2[indexSaleList2].primaryStatus = unit.primaryStatus;
                                this.saleList2[indexSaleList2].description = unit.description;
                                this.saleList2[indexSaleList2].isException = unit.isException;
                                this.saleList2[indexSaleList2].exceptionalReason = unit.exceptionalReason;
                                this.saleList2[indexSaleList2].registeredPos = unit.registeredPos;
                                this.saleList2[indexSaleList2].posId = unit.pos ? unit.pos.id : '';
                                this.saleList2[indexSaleList2].posName = unit.pos ? unit.pos.name : '';
                                this.saleList2[indexSaleList2].registeredQueue = unit.registeredQueue;
                                this.saleList2[indexSaleList2].modifiedDate = unit.modifiedDate;
                                if (unit.priorities[0] && unit.registeredDate) {
                                    this.saleList2[indexSaleList2].registeredDate = new Date(unit.registeredDate);
                                    this.saleList2[indexSaleList2].isRegistering = false;
                                }
                                if (unit.primaryStatus === PropertyUnitStatus.COMING) {
                                    this.saleList2[indexSaleList2].registeredDate = null;
                                }

                                // remove item when it confirm by another pos
                                if (unit.primaryStatus === PropertyUnitStatus.PROCESSING
                                    && !this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicket)
                                ) {
                                    if (this.user.hasPermission(PropertyTicketPermissionConst.SaleAdmin.SaCreateTicket)
                                        && this.user.staffIds && !this.user.staffIds.includes(unit.priorities[0].employeeId)) {
                                        if (this.currentSalesProgram.allowViewAllUnitProcessing) {
                                            this.saleList2[indexSaleList2].priorities = [];
                                            this.saleList2[indexSaleList2].priority0 = null;
                                            this.saleList2[indexSaleList2].primaryStatus = unit.primaryStatus;
                                            this.saleList2[indexSaleList2].registeredPos = null;
                                            this.saleList2[indexSaleList2].posId = '';
                                            this.saleList2[indexSaleList2].posName = '';
                                            this.saleList2[indexSaleList2].price = 0;
                                            this.saleList2[indexSaleList2].priceVat = 0;
                                            this.saleList2[indexSaleList2].priceAbove = 0;
                                            this.saleList2[indexSaleList2].priceAboveVat = 0;
                                        } else {
                                            this.saleList2 = this.saleList2.filter(x => x.id !== this.saleList2[indexSaleList2].id);
                                        }
                                    } else if (
                                        (unit.registeredPos && this.user.pos && (unit.registeredPos.id === this.user.pos.id || unit.registeredPos.id === this.user.pos.parentId))
                                        || (this.user.pos && !this.user.hasPermission(PropertyTicketPermissionConst.SaleAdmin.SaCreateTicket)
                                            && (unit.pos && unit.pos.id === this.user.pos.id || unit.pos.id === this.user.pos.parentId))
                                    ) {

                                        this.toastr.success('Thành công', 'Đăng ký sản phẩm');
                                    } else {
                                        if (this.currentSalesProgram.allowViewAllUnitProcessing) {
                                            this.saleList2[indexSaleList2].priorities = [];
                                            this.saleList2[indexSaleList2].priority0 = null;
                                            this.saleList2[indexSaleList2].primaryStatus = unit.primaryStatus;
                                            this.saleList2[indexSaleList2].registeredPos = null;
                                            this.saleList2[indexSaleList2].posId = '';
                                            this.saleList2[indexSaleList2].posName = '';
                                            this.saleList2[indexSaleList2].price = 0;
                                            this.saleList2[indexSaleList2].priceVat = 0;
                                            this.saleList2[indexSaleList2].priceAbove = 0;
                                            this.saleList2[indexSaleList2].priceAboveVat = 0;
                                        } else {
                                            this.saleList2 = this.saleList2.filter(x => x.id !== this.saleList2[indexSaleList2].id);
                                        }
                                    }
                                }
                                // remove item when it confirm by another pos
                                if (unit.primaryStatus === PropertyUnitStatus.CONFIRM
                                    && !this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicket)
                                ) {
                                    if (this.user.hasPermission(PropertyTicketPermissionConst.SaleAdmin.SaCreateTicket)
                                        && this.user.staffIds && !this.user.staffIds.includes(unit.priorities[0].employeeId)) {
                                        this.saleList2 = this.saleList2.filter(x => x.id !== this.saleList2[indexSaleList2].id);
                                    } else if (
                                        (unit.registeredPos && this.user.pos && (unit.registeredPos.id === this.user.pos.id || unit.registeredPos.id === this.user.pos.parentId))
                                        || (this.user.pos && !this.user.hasPermission(PropertyTicketPermissionConst.SaleAdmin.SaCreateTicket)
                                            && (unit.pos && unit.pos.id === this.user.pos.id || unit.pos.id === this.user.pos.parentId))
                                    ) {

                                        // this.toastr.success('Thành công', 'Đăng ký sản phẩm');
                                    } else {
                                        this.saleList2 = this.saleList2.filter(x => x.id !== this.saleList2[indexSaleList2].id);
                                    }
                                }
                                if (unit.primaryStatus === PropertyUnitStatus.LOCK){
                                    this.saleList2 = this.saleList2.filter(x => x.id !== this.saleList2[indexSaleList2].id);
                                }
                            } else {
                                const newUnit = new PropertyTable(unit);
                                if (newUnit.primaryStatus === PropertyUnitStatus.COMING
                                    && (newUnit.posId === this.user.f1San.id || newUnit.posId === this.user.f1San.parentId || !newUnit.posId)
                                    || (unit.primaryStatus === PropertyUnitStatus.PROCESSING
                                        && (newUnit.priority0 && this.user.staffIds && this.user.staffIds.includes(newUnit.priority0.employeeId)))
                                ) {
                                    this.saleList2.push(newUnit);
                                } else {
                                    noChange = true;
                                }
                            }
                            if (!noChange) {
                                this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);
                                this.changedRowIds2.push(unit.id);
                                this.changedRowIds2 = this.changedRowIds2.slice();
                            }
                        }
                    }
                }

                // update Units Table
                if (unit && this.propertyArrModel && this.propertyArrModel.length > 0) {
                    const currentUnit = this.propertyArrModel.find(el => el.id === unit.id);
                    if (currentUnit) {
                        const propertyUnit = new PropertyTable(unit);
                        currentUnit.primaryStatus = propertyUnit.primaryStatus;
                        currentUnit.price = propertyUnit.price;
                        currentUnit.priceVat = propertyUnit.priceVat;
                        currentUnit.bgColor = propertyUnit.bgColor;
                        currentUnit.textColor = propertyUnit.textColor;
                        this.setupMappingTable(this.propertyArrModel);
                        if (this.unitTable) {
                            this.unitTable['changedRowCodes'] = [currentUnit.view1];
                        }
                    }
                }
            }
        } catch (error) {
            console.log('PrimaryTransUpdatedAdmin', error);
        }
    }
    private _wsHandleProjectUnitExtendPos(data) {
        try {
            if (this.projectId === data.message.data.projectId) {
                this.showLockExtendPriority = true;
                if (data.message.data.listUnits && data.message.data.listUnits.length > 0) {
                    data.message.data.listUnits.forEach(el => {
                        const unit = this.propertyArrModel.find(x => x.id === el.id);
                        if (unit) {
                            unit.extendable = true;
                            unit.extendPos = el.extendPos;
                            unit.extendPosId = el.extendPos ? el.extendPos.id : '';
                            unit.priorities.forEach(pr => {
                                pr.extPosName = unit.extendPos ? unit.extendPos.name : 'Tất cả ĐVBH';
                            });
                        } else {
                            if (el.extendPos && (el.extendPos.id === this.user.pos.id || el.extendPos.id === this.user.pos.parentId || !el.extendPos.id ||
                                (this.user.f1San && el.extendPos.id === this.user.f1San.id)
                            )) {
                                this.propertyService.getUnitById(el.id).subscribe(res => {
                                    const newUnit = new PropertyTable(res, this.marketPos);
                                    this.propertyArrModel.push(newUnit);
                                    this.setupMappingTable(this.propertyArrModel);
                                });
                            }
                        }
                    });
                }
            }
        } catch (error) {
            console.log('ProjectUnitExtendPos', error);
        }
    }
    private _wsHandleProjectUnitLockExtendPos(data) {
        try {
            if (this.projectId === data.message.data.projectId) {
                if (data.message.data.listUnits && data.message.data.listUnits.length > 0) {
                    data.message.data.listUnits.forEach(el => {
                        const unit = this.propertyArrModel.find(x => x.id === el);
                        if (unit) {
                            unit.extendable = false;
                        }
                    });
                }
                if (!this.propertyArrModel.some(x => x.extendable)) {
                    this.showLockExtendPriority = false;
                }
            }
        } catch (error) {
            console.log('ProjectUnitLockExtendPos', error);
        }
    }
    private _wsHandleProjectUnitUpdatePriority(data) {
        try {
            if (this.projectId === data.message.data.projectId) {
                const propertyUnit = new PropertyTable(data.message.data.unit, this.marketPos);
                const current = this.propertyArrModel.find(el => el.id === propertyUnit.id);
                if (this.canViewAllTicket || this.canViewMarketAllTicket) {
                    current.priorities = propertyUnit.priorities;
                } else {
                    if (propertyUnit.priorities) {
                        let priorities = [];
                        propertyUnit.priorities.forEach(e => {
                            if (e.posId === this.user.pos.id || e.posId === this.user.pos.parentId ||
                                (this.user.f1San && e.posId === this.user.f1San.id)
                            ) {
                                priorities.push(e);
                                this.toastr.success('Thành Công!', 'Lưu thông tin');
                            } else {
                                e.customerName = '';
                                e.employeeName = '';
                                e.posName = '';
                                priorities.push(e);
                            }
                        });

                        current.priorities = priorities;
                    }
                }
                current.primaryStatus = propertyUnit.primaryStatus;
                current.bgColor = propertyUnit.bgColor;
                current.textColor = propertyUnit.textColor;
                this.setupMappingTable(this.propertyArrModel);
                if (this.unitTable) {
                    this.unitTable['changedRowCodes'] = [propertyUnit.view1];
                }
            }
        } catch (error) {
            console.log('ProjectUnitUpdatePriority', error);
        }
    }

    private _wsHanleRevokeUnit(dataWs: { projectId?: string, propertyUnits?: any[], propertyIds?: string[] } = {}) {
        try {
            const propertyUnits = dataWs.propertyUnits || [];
            const propertyIds = dataWs.propertyIds || [];
            if (this.projectId === dataWs.projectId &&
                this.isProjectLeader() ||
                this.user.hasPermission(ConstPermissionProperty.Primary.Get) ||
                this.user.hasPermission(ConstPermissionProperty.Primary.ViewMarketAll) ||
                this.user.hasPermission(ConstPermissionProperty.Primary.GetAll)) {

                this.propertyArrModel = this.propertyArrModel.filter(el => !propertyIds.includes(el.id));

                if (this.isProjectLeader() || this.user.hasPermission(ConstPermissionProperty.Primary.GetAll)) {
                    this.propertyArrModel = this.propertyArrModel.concat(propertyUnits.map(el => new PropertyTable(el, this.marketPos)));
                } else if(this.user.hasPermission(ConstPermissionProperty.Primary.ViewMarketAll)) {
                    this.propertyArrModel = this.propertyArrModel.concat(propertyUnits.filter(e => !e.pos || e.pos.id !== this.rootPos).map(el => new PropertyTable(el, this.marketPos)));
                }

                this.setupMappingTable(this.propertyArrModel);
            }

            for (const unit of propertyUnits) {
                this._handleTransferedUnit(unit);
                if (unit.attributes && unit.attributes.length > 0) {
                    const view1 = unit.attributes.find(x => x.attributeName === 'View 1');
                    if (view1) {
                        if (this.unitTable) {
                            this.unitTable['changedRowCodes'] = [view1.value];
                        }
                    }
                }
            }
            this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);
        } catch (error) {
            console.log('RevokeUnit', error);
        }
    }

    private _wsHanletTransferUnit(dataWs: { projectId?: string, propertyUnit?: any }) {
        try {
            const propertyUnit = new PropertyTable(dataWs.propertyUnit, this.marketPos);

            if (this.projectId === dataWs.projectId) {
                if (this.user.hasPermission(ConstPermissionProperty.Primary.GetAll)
                 || this.isProjectLeader()) {
                    const idx = this.propertyArrModel.findIndex(el => el.id === propertyUnit.id);
                    if (idx === -1) {
                        this.propertyArrModel = this.propertyArrModel.concat(propertyUnit);
                    } else {
                        this.propertyArrModel = this.propertyArrModel.map(el => el.id === propertyUnit.id ? propertyUnit : el);
                    }
                } else if(this.user.hasPermission(ConstPermissionProperty.Primary.ViewMarketAll)) {
                    if (propertyUnit.posId !== this.rootPos && propertyUnit.posIdF1 !== this.rootPos) {
                        this.propertyArrModel = this.propertyArrModel.filter(el => el.id !== propertyUnit.id);
                        this.propertyArrModel = this.propertyArrModel.concat(propertyUnit);
                    }
                } else if (this.user.hasPermission(ConstPermissionProperty.Primary.Get)) {
                    if (propertyUnit.posId || propertyUnit.posIdF1) {
                        if (this.user.pos.id === propertyUnit.posId || this.user.pos.parentId === propertyUnit.posId ||
                          this.user.pos.id === propertyUnit.posIdF1 || this.user.pos.parentId === propertyUnit.posIdF1) {
                            this.propertyArrModel = this.propertyArrModel.filter(el => el.id !== propertyUnit.id);
                            this.propertyArrModel = this.propertyArrModel.concat(propertyUnit);
                        } else {
                            this.propertyArrModel = this.propertyArrModel.filter(el => el.id !== propertyUnit.id);
                        }
                    }
                }
                this.setupMappingTable(this.propertyArrModel);

                // realtime transaction
                this._handleTransferedUnit(dataWs.propertyUnit);
                this.onChangeKeySearchDSGD(this.filterDSGD.keySearch, this.status);
                if (this.unitTable) {
                    this.unitTable['changedRowCodes'] = [propertyUnit.view1];
                }
            }
        } catch (error) {
            console.log('TransferUnit', error);
        }
    }
    private _wsHandleRemoveUnit(dataWs: { projectId?: string, ids?: any }) {
        try {
            if (this.projectId === dataWs.projectId) {
                this.propertyArrModel = this.propertyArrModel.filter(el => !dataWs.ids.includes(el.id));
                this.setupMappingTable(this.propertyArrModel);
            }
        } catch (error) {
            console.log('RemoveUnit', error);
        }
    }

    private _handleTransferedUnit(unit: any) {
        if (!this.startStage) {
            return;
        }
        try {
            if (!unit || !unit.id) {
                return;
            }
            let tmpIdx = this.saleList.findIndex(item => item.id === unit.id);
            if (tmpIdx > -1) {
                if (unit.primaryStatus === PropertyUnitStatus.CLOSE || unit.primaryStatus === PropertyUnitStatus.LOCK) {
                    this.saleList.splice(tmpIdx, 1);
                } else if (this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicket)) {
                    this.saleList[tmpIdx].posId = unit.pos ? unit.pos.id : '';
                    this.saleList[tmpIdx].posName = unit.pos ? unit.pos.name : '';
                    this.saleList[tmpIdx].primaryStatus = unit.primaryStatus;
                    this.saleList[tmpIdx].modifiedDate = unit.modifiedDate;
                } else {
                    if (unit.pos.id !== this.user.pos.id || unit.pos.id !== this.user.pos.parentId ||
                        (this.user.f1San && unit.pos.id !== this.user.f1San.id)
                    ) {
                        this.saleList.splice(tmpIdx, 1);
                    } else {
                        this.saleList[tmpIdx].posId = unit.pos ? unit.pos.id : '';
                        this.saleList[tmpIdx].posName = unit.pos ? unit.pos.name : '';
                        this.saleList[tmpIdx].primaryStatus = unit.primaryStatus;
                        this.saleList[tmpIdx].modifiedDate = unit.modifiedDate;
                    }
                }
                this.changedRowIds1.push(unit.id);
                this.changedRowIds1 = this.changedRowIds1.slice();
            }

            tmpIdx = this.saleList2.findIndex(item => item.id === unit.id);
            if (tmpIdx > -1) {
                if (unit.primaryStatus === PropertyUnitStatus.CLOSE || unit.primaryStatus === PropertyUnitStatus.LOCK) {
                    this.saleList2.splice(tmpIdx, 1);
                } else if (this.user.hasPermission(PropertyTicketPermissionConst.CustomerService.ApproveTicket)) {
                    this.saleList2[tmpIdx] = new PropertyTable(unit);
                } else {
                    if (unit.pos && unit.pos.id !== this.user.pos.id && unit.pos.id !== this.user.pos.parentId ||
                        (this.user.f1San && unit.pos.id !== this.user.f1San.id)) {
                        this.saleList2.splice(tmpIdx, 1);
                    } else {
                        this.saleList2[tmpIdx] = new PropertyTable(unit);
                    }
                }
                this.changedRowIds2.push(unit.id);
                this.changedRowIds2 = this.changedRowIds2.slice();
            } else {
                if (unit.primaryStatus !== PropertyUnitStatus.CLOSE && unit.primaryStatus !== PropertyUnitStatus.LOCK
                    && (!unit.pos || unit.pos.id === this.user.pos.id || unit.pos.id === this.user.pos.parentId ||
                    (this.user.f1San && unit.pos.id !== this.user.f1San.id) || this.canAutoSortDSGD
                )) {
                    this.saleList2.push(new PropertyTable(unit));
                    this.changedRowIds2.push(unit.id);
                    this.changedRowIds2 = this.changedRowIds2.slice();
                }
            }
        } catch (error) {
            console.log('TransferedUnit', error);
        }
    }

    onClickCheckBoxPos(checked: boolean, item) {
        item.checked = checked;
        if (checked) {
            if (!this.selectPos.includes(item.id)) {
                this.selectPos.push(item.id);
            }
        } else {
            if (this.selectPos.length > 0) {
                this.selectPos = this.selectPos.filter(i => i !== item.id);
            }
        }
        // this.getTableData(true);
    }

    onClickCheckBoxSaleProgram(checked: boolean, item) {
        item.checked = checked;
        if (checked) {
            if (!this.selectSaleProgram.includes(item.id)) {
                this.selectSaleProgram.push(item.id);
            }
        } else {
            if (this.selectSaleProgram.length > 0) {
                this.selectSaleProgram = this.selectSaleProgram.filter(i => i !== item.id);
            }
        }
    }

    onCheckAll() {
        this.isCheckAll = !this.isCheckAll;
        this.salesProgram.forEach((saleProgram: any) => {
            saleProgram.checked = this.isCheckAll;
            if (!this.selectSaleProgram.includes(saleProgram.id)) {
                this.selectSaleProgram.push(saleProgram.id);
            }
            return saleProgram;
        });

    }

    onClearCheckboxPos() {
        this.selectPos = [];
        this.selectPosApply = [];
        this.pos.map((e: any) => e.checked = false);
        this.getTableData(true);
    }

    onClickCheckBoxStatus(checked: boolean, item) {
        item.checked = checked;
        if (checked) {
            if (!this.selectStatus.includes(item.id)) {
                this.selectStatus.push(item.id);
            }
        } else {
            if (this.selectStatus.length > 0) {
                this.selectStatus = this.selectStatus.filter(i => i !== item.id);
            }
        }
        this.searchStatus = this.selectStatus.toString() || '';
        // this.searchCode();
    }

    openInputSearch(index: number) {
        this.textAreas.find((item, idx) => {
            return idx === index;
        }).nativeElement.focus();
    }

    onClickCheckBoxPosSearch(checked: boolean, item) {
        item.checked = checked;
        if (checked) {
            if (!this.selectPosSearch.includes(item.id)) {
                this.selectPosSearch.push(item.id);
            }
        } else {
            if (this.selectPosSearch.length > 0) {
                this.selectPosSearch = this.selectPosSearch.filter(i => i !== item.id);
            }
        }
        this.dvbh = this.selectPosSearch.toString() || '';
        // this.searchCode();
    }


    displayDownloadMenu() {
        let result = false;
        if (this.selectedTab === 0) {
            if (this.canImportPrimary || this.canDownloadPrioritiesOfPosReport || this.canDownloadPriorityReport
                || this.canDownloadPrimary || this.canDownloadDmsp || this.canTransferUnit) {
                result = true;
            }
        } else if (this.selectedTab === 2 && this.canDownloadListDemand) {
            result = true;
        } else if (this.selectedTab === 3 && (this.canDownloadListDemand || this.canImportTransferEscrow)) {
            result = true;
        } else if (this.selectedTab === 4 && (this.canDownloadSaleList || this.canViewAllTicket || this.canDownloadListDemand
            || this.canImportTransferEscrow || this.canViewAllCustomer)) {
            result = true;
        }
        return result;
    }
    reRenderTable() {
        if (this.unitTable) {
            this.unitTable['refeshUI'] = true;
            this.isRefeshUnitTable = true;
        }
    }

    resetFilter() {
        this.uncheckFilter();
        this.searchCode();
    }
    uncheckFilter() {
        this.selectPos = [];
        this.selectStatus = [];
        this.selectPosSearch = [];
        this.selectPosApply = [];
        this.selectStatusApply = [];
        this.selectPosSearchApply = [];
        this.selectSaleProgram = [];
        this.dropdownStatus.map(item => {
            item['checked'] = false;
            return item;
        });
        this.pos.map(item => {
            item['checked'] = false;
            return item;
        });
        this.salesProgram.map(item => {
            item['checked'] = false;
            return item;
        });
        this.searchStatus = '';
        this.dvbh = null;
        this.searchKey = new FormControl();
    }

    getAllData(isFilter = false) {
        if (!this.currentSalesProgram.id) {
            return;
        }
        this.isTableLoading$.next(true);
        this.propertyArrModel = [];
        this.propertyArrModelFilter = [];
        this.propertyOfPriorityFilter = [];
        this.listExtendUnit = [];

        const params = {
            salesProgramId: this.currentSalesProgram.id,
            categoryId: this.categoryId,
            projectId: this.selectedProject ? this.selectedProject.id : this.projectId,
            priority: this.selectedPriorities.toString() || '',
            primaryStatus: this.filterBangHang.primaryStatus,
            posId: this.selectPos.toString() || '',
            _fields: `id,project.name,project.id,pos.name,pos.id,code,apartmentType,areaWide,areaLong,primaryStatus,shortCode,attributes.attributeName,block,floor,
                attributes.value,bedroom,direction,priceAbove,price,area,priorities,extendable,extendPos,reasonRevoke,modifiedDate,priceVat,category.id,
                stage,lstImage`
        };

        this.projectService.getTable(params).pipe(
            finalize(() => this.isTableLoading$.next(false)),
            takeUntil(this.unsubscribe$)
        ).subscribe(res => {
            if (this.unitTable) {
                this.unitTable['refeshUI'] = true;
                this.isRefeshUnitTable = true;
            }
            this.propertyArrModel = res.map(el => new PropertyTable(el, this.marketPos));
            if (this.propertyArrModel.some(x => x.extendable)) {
                this.showLockExtendPriority = true;
                this.viewTableModeValue = 2;
            } else {
                this.showLockExtendPriority = false;
            }
            this.onChangeKeySearchBH(this.filterBangHang.keySearch);
            if (isFilter) {
                this.onChangeCBExtendPriority(true);
                this.selectedPriorities = this.selectedPriorities.concat('4');
            } else {
                this.onChangeCBExtendPriority(false);
            }
        });
    }

    toggleEditTicket(isOpen = false) {
        const p = {
            projectId: this.projectId,
            salesProgramId: this.currentSalesProgram ? this.currentSalesProgram.id : ''
        }
        if(isOpen) {
            this.projectService.enableEditTicket(p).subscribe(
                res => {
                    if(res) {
                        this.toastr.success('Thành Công!', 'Mở chỉnh sửa');
                        this.currentSalesProgramId = this.currentSalesProgram.id;
                        this.search();
                    } else {
                        this.toastr.error('', 'Không thành Công! ');
                    }
                }, err => {
                    this.toastr.error('', 'Không thành Công! ');
                }
            )
        } else {
            this.projectService.disableEditTicket(p).subscribe(
                res => {
                    if(res) {
                        this.toastr.success('Thành Công!', 'Khóa chỉnh sửa');
                        this.search();
                    } else {
                        this.toastr.error('', 'Không thành Công! ');
                    }
                }, err => {
                    this.toastr.error('', 'Không thành Công! ');
                }
            )
        }
    }

    onSortFilter(){
        this.pos.sort((a: any, b: any) => {
            return a.checked ? -1 : 1
        });
        this.dropdownStatus.sort((a: any, b: any) => {
            return a.checked ? -1 : 1
        });
    }
    searchTableData(isChangePos = false) {
        this.selectPosApply = [...this.selectPos];
        this.getTableData(isChangePos);
    }
    filterCode() {
        this.selectPosSearchApply = [...this.selectPosSearch];
        this.selectStatusApply = [...this.selectStatus];
        this.searchCode();
    }
    menuClosed(isBangHang = true) {
        if (isBangHang) {
            this.selectPos = [...this.selectPosApply];
            this.pos.map(item => {
                const checked = { checked: this.selectPos.includes(item.id) }
                item = Object.assign(item, { ...checked });
                return item;
            });
            this.selectSaleProgram = [...this.selectSaleProgramApply];
            this.salesProgram.map(item => {
                const checked = { checked: this.selectSaleProgram.includes(item.id) }
                item = Object.assign(item, { ...checked });
                return item;
            })
            if (this.selectSaleProgram.length === 0) {
                this.isCheckAll = false;
                this.salesProgram.map(item => {
                    if (item.id === this.currentSalesProgram.id) {
                        item.checked = true;
                        return item;
                    }
                })
            } else if (this.selectSaleProgramApply.length === this.salesProgram.length) {
                this.isCheckAll = true;
            } else {
                this.isCheckAll = false;
            }
        } else {
            this.selectPosSearch = [...this.selectPosSearchApply];
            this.pos.map(item => {
                const checked = { checked: this.selectPosSearch.includes(item.id) }
                item = Object.assign(item, { ...checked });
                return item;
            });
            this.selectStatus = [...this.selectStatusApply];
            this.dropdownStatus.map(item => {
                const checked = { checked: this.selectStatus.includes(item.id) }
                item = Object.assign(item, { ...checked });
                return item;
            });
        }
    }
    onApplyFilter(event) {
        if (event.poses && event.poses.length > 0) {
            this.selectPoses = event.poses;
        } else {
            this.selectPoses = [];
        }
        if (event.keywords) {
            this.onChangeKeySearchBH(event.keywords);
        } else {
            this.getTableData(false, event);
        }
    }
    onTemplateChange(event, setQuery = false) {
        this.enableRecount = true;
        this.currentTemplateCode = event ? event.templateCode : '';
        this.currentTemplateName = event ? event.templateName : '';
        // const rooms: any[] = Object.values(this.jsonCanHo).filter((room: PropertyTable) => this.currentTemplateCode ? this.currentTemplateCode === room.block : true);
        this.propertyOfPriorityFilter = this.propertyArrModelFilter.filter((room: PropertyTable) => this.currentTemplateCode ? this.currentTemplateCode === room.block && room.posId !== this.rootPos : room.posId !== this.rootPos);
        // console.log(this.route);
        if (setQuery) {
           const query = {...this.route.snapshot.queryParams};
           this.updateQueryParams(query, 'tpl', this.currentTemplateCode);
        }
        if (this.categoryId === CategoryEnum.CAN_HO) {
            this.replaceMappingTableCanHo();
        } else if (this.categoryId === CategoryEnum.DAT_NEN) {
            this.replaceMappingDatNen();
        }
        if (this.unitTable) {
            this.unitTable['refeshUI'] = true;
        }
    }

    viewDetailStatistic(blockIndex: number, floorIndex: number = -1, roomIndex: number = -1) {
        const blockName = this.blockFloorRoom[blockIndex].block;
        let rooms: any[] = Object.values(this.jsonCanHo).filter((room: PropertyTable) => blockName === room.block);
        let title = blockName;
        if (floorIndex >= 0) {
            const floorName = this.blockFloorRoom[blockIndex].floors[floorIndex];
            rooms = rooms.filter(room => floorName === room.floor);
            title = `${title} - Tầng ${floorName}`;
        }
        if (roomIndex >= 0) {
            const roomName = this.blockFloorRoom[blockIndex].rooms[roomIndex];
            rooms = rooms.filter(room => roomName === room.room);
            title = `${title} - Số phòng ${roomName}`;
        }
        const statistic = this._countStatusSaleList(rooms);
        this.dialog.open(PopupStatisticComponent, {
            width: '500px',
            data: {
                title: title,
                statistic: [
                    {
                        label: UNIT_STATUS['LOCK'],
                        value: statistic.lock,
                        color: '#ffffff'
                    },
                    {
                        label: UNIT_STATUS['CLOSE'],
                        value: statistic.close,
                        color: '#cccccc'
                    },
                    {
                        label: UNIT_STATUS['COMING'],
                        value: statistic.coming,
                        color: '#ffe69b'
                    },
                    {
                        label: UNIT_STATUS['PROCESSING'],
                        value: statistic.processing,
                        color: '#975bf5'
                    },
                    {
                        label: UNIT_STATUS['LOCK_CONFIRM'],
                        value: statistic.lockConfirm,
                        color: '#ff7600'
                    },
                    {
                        label: UNIT_STATUS['CONFIRM'],
                        value: statistic.confirm,
                        color: '#8de0ff'
                    },
                    {
                        label: UNIT_STATUS['SUCCESS'],
                        value: statistic.success,
                        color: '#fc3f3f'
                    },
                    {
                        label: UNIT_STATUS['CANCEL'],
                        value: statistic.cancel,
                        color: '#66ff66'
                    },
                    {
                        label: UNIT_STATUS['CANCEL'],
                        value: statistic.transfer,
                        color: '#d60093'
                    },
                    {
                        label: 'Hợp đồng',
                        value: statistic.deposit,
                        color: '#ffccff'
                    },
                    {
                        label: UNIT_STATUS['MOVED'],
                        value: statistic.moved,
                        color: '#BDD7EE'
                    }
                ]
            }
        });
    }

    changeViewTableMode(value: number) {
        this.viewTableModeValue = value;
        if (value === 1 && !this.currentTemplateCode) {
            this.onTemplateChange({
                templateCode: this.currentSalesProgram.unitTableTemplateUrl && this.currentSalesProgram.unitTableTemplateUrl[0] && this.currentSalesProgram.unitTableTemplateUrl[0].templateCode
            });
        }
        if (value === 2) {
            this.onTemplateChange({
                templateCode: ''
            });
        }
    }
    syncBooking(ticket, isCancel = false){
        const popupSync = this.dialog.open(PopupSyncCRMComponent, {
            width: '650px',
            data: {
                ticket: ticket,
                listSelectedUnits: this.saleList.concat(this.saleList2),
                salesProgram: this.currentSalesProgram,
                projectId: this.projectId,
                isCancel
            },
            disableClose: true,
            panelClass: 'app-dialog'
        });
        popupSync.afterClosed().subscribe((result: any) => {
            if (result && result.code) {
            }
        });

    }

    createCustomerCarePlusAccount() {
        const popupSync = this.dialog.open(PopupCreateUserCareplusComponent, {
             width: '650px',
             data: {
                 projectId: this.projectId,
             },
             disableClose: true,
             panelClass: 'app-dialog'
         });
         popupSync.afterClosed().subscribe((result: any) => {
             if (result && result.code) {
             }
         });
     }
    createDocumentsForCustomer() {
        const dialogRef = this.dialog.open(DialogConfirmComponent, {
            width: '500px',
            data: {
                title: `Kiểm tra thông tin dưới trước khi <b>Tiếp tục<b>`,
                content: `Hoàn tất ráp ưu tiên.<br>
                            Thiết lập các template Email gửi hồ sơ cho khách hàng.<br>
                            Thiết lập template chứng từ liên quan.<br>`,
                imageUrl: 'assets/img/dxres/warning.svg',
                textConfirm: 'Tiếp tục tạo',
                textCancel: 'Hủy bỏ'
            },
            autoFocus: false
        });
        dialogRef.afterClosed().subscribe((result: boolean) => {
            if (result) {
                this.isTicketCreateDocuments$.next(true);
                this.projectService
                    .createDocumentsForCustomer(this.projectId, this.currentSalesProgram.id).pipe(
                        finalize(() => this.isTicketCreateDocuments$.next(false)),
                        takeUntil(this.unsubscribe$)
                    )
                    .subscribe(
                        res => {
                            if (res.success) {
                                this.isTicketCreateDocuments$.next(false);
                                this.toastr.success('Thành Công!', 'Tạo hồ sơ khách hàng');
                                this.search();
                            } else {
                                this.isTicketCreateDocuments$.next(false);
                                this.toastr.error('', 'Không thành Công! ');
                            }
                        }, err => {
                            this.isTicketCreateDocuments$.next(false);
                            this.toastr.error('', 'Không thành Công! ');
                        }
                    );
            }
        });
    }

    private updateQueryParams(query, field, value?) {
        if (!value && value !== 0) {
            delete query[field];
        } else {
            Object.assign(query, {[field]: value});
        }

        this.router.navigate([], {
            relativeTo: this.route,
            queryParams: query,
        });
    }
    syncEscrow(ticket, isCancel = false){
        const popupSync = this.dialog.open(PopupSyncEscrowCRMComponent, {
            width: '650px',
            data: {
                ticket: ticket,
                listSelectedUnits: this.saleList.concat(this.saleList2),
                salesProgram: this.currentSalesProgram,
                projectId: this.projectId,
                isCancel
            },
            disableClose: true,
            panelClass: 'app-dialog'
        });
        popupSync.afterClosed().subscribe((result: any) => {
            if (result && result.code) {
            }
        });

    }
    goToRegisterConsignmentPage() {
        if (!this.registerConsignmentUrl) return;
        this.router.navigate([]).then(result => { window.open(this.registerConsignmentUrl, '_blank'); });
    }
    goToDocumentUrlPage() {
        if (!this.documentUrl) return;
        this.router.navigate([]).then(result => { window.open(this.documentUrl, '_blank'); });
    }
    getTooltipUnitStatus(primaryStatus: string){
        return UNIT_STATUS[primaryStatus] || primaryStatus;
    }
}
