<div class="event-sale">
  <ng-container>
      <div (click)="isCollapse = !isCollapse; reCalculateGridHeight()"
          style="position: absolute;right: 15px;top: 6px;cursor: pointer;z-index: 2;">
          <img [src]="isCollapse ? './assets/img/dxres/uncollapse.svg' : './assets/img/dxres/collapse.svg'" alt="">
      </div>
  </ng-container>
  <div class="header-event-sale" #elementTop [class]="isCollapse ? 'collapse' : ''" fxLayout="column"
      style="margin-bottom: 8px;">
      <div fxLayout="row" fxLayoutAlign="space-between center" class="pr-35 mb-10 h-28">
          <div fxLayout="row" fxLayoutGap="10px">
              <div class="text-project">{{selectedProject?.name}}</div>

              <div>
                  <div fxFlex="200px" *ngIf="(selectedTab === 0)">
                      <!-- Chọn CTBH -->
                      <button [matMenuTriggerFor]="dvbh" class="btn-select btn-select-sale-program" (menuClosed)="menuClosed(true)">
                        <span class="name-sale-program">{{ nameSaleProgram }}</span>
                        <span *ngIf="salesProgram.length > 0" class="number-filters">{{countSalesProgramChoose}}</span>
                        <img src="./assets/img/icon/ic_expand_more_24px.svg" class="icon-show">
                      </button>
                      <mat-menu #dvbh="matMenu" class="pos-items">
                        <button mat-menu-item
                            (click)="onCheckAll();$event.stopPropagation();"
                            class="btn-checkbox">
                            <span style="position: relative; top: -2px; left: 0"
                                (click)="$event.stopPropagation();">
                                <app-checkbox-button style="display: inherit;"
                                    (outChange)="onCheckAll()"
                                    [checked]="isCheckAll"
                                    >
                                </app-checkbox-button>
                            </span>
                            Tất cả
                        </button>
                          <button mat-menu-item *ngFor="let item of salesProgram"
                              (click)="onClickCheckBoxSaleProgram(!item.checked, item);$event.stopPropagation();"
                              class="btn-checkbox">
                              <span style="position: relative; top: -2px; left: 0"
                                  (click)="$event.stopPropagation();">
                                  <app-checkbox-button style="display: inherit;"
                                      (outChange)="onClickCheckBoxSaleProgram($event, item)"
                                      [checked]="item.checked"
                                      >
                                  </app-checkbox-button>
                              </span>
                              {{item.name}}
                          </button>
                          <div fxLayout="row" fxLayoutAlign="center" class="clear-btn">
                              <button mat-menu-item (click)="onClearCheckboxSaleProgram();$event.stopPropagation();">
                                  <img src="./assets/img/icon/reload.svg" class="icon">
                                  <span class="text-checkbox">Thiết lập lại</span>
                              </button>
                              <button mat-menu-item  (click)="getAllLstBangHang();">
                                  <img src="./assets/img/icon/filter.svg" class="icon">
                                  <span class="text-checkbox">Áp dụng</span>
                              </button>
                          </div>
                      </mat-menu>
                  </div>
                  <div fxFlex="200px" *ngIf="(selectedTab === 4)">
                      <!-- <app-select [clearable]="false" (change)="refresh($event)" [items]="salesProgram"
                          bindLabel="name" bindValue="id" [(ngModel)]="currentSalesProgram.id"
                          placeholder="Chương trình BH">
                      </app-select> -->

                      <button [matMenuTriggerFor]="dvbh" class="btn-select btn-select-sale-program" (menuClosed)="menuClosed(true)">
                        <span class="name-sale-program">{{ nameSaleProgram }}</span>
                        <span *ngIf="salesProgram.length > 0" class="number-filters">{{countSalesProgramChoose}}</span>
                        <img src="./assets/img/icon/ic_expand_more_24px.svg" class="icon-show">
                      </button>
                      <mat-menu #dvbh="matMenu" class="pos-items">
                        <button mat-menu-item
                            (click)="onCheckAll();$event.stopPropagation();"
                            class="btn-checkbox">
                            <span style="position: relative; top: -2px; left: 0"
                                (click)="$event.stopPropagation();">
                                <app-checkbox-button style="display: inherit;"
                                    (outChange)="onCheckAll()"
                                    [checked]="isCheckAll"
                                    >
                                </app-checkbox-button>
                            </span>
                            Tất cả
                        </button>
                          <button mat-menu-item *ngFor="let item of salesProgram"
                              (click)="onClickCheckBoxSaleProgram(!item.checked, item);$event.stopPropagation();"
                              class="btn-checkbox">
                              <span style="position: relative; top: -2px; left: 0"
                                  (click)="$event.stopPropagation();">
                                  <app-checkbox-button style="display: inherit;"
                                      (outChange)="onClickCheckBoxSaleProgram($event, item)"
                                      [checked]="item.checked"
                                      >
                                  </app-checkbox-button>
                              </span>
                              {{item.name}}
                          </button>
                          <div fxLayout="row" fxLayoutAlign="center" class="clear-btn">
                              <button mat-menu-item (click)="onClearCheckboxSaleProgram();$event.stopPropagation();">
                                  <img src="./assets/img/icon/reload.svg" class="icon">
                                  <span class="text-checkbox">Thiết lập lại</span>
                              </button>
                              <button mat-menu-item  (click)="getAllLstGiaoDich();">
                                  <img src="./assets/img/icon/filter.svg" class="icon">
                                  <span class="text-checkbox">Áp dụng</span>
                              </button>
                          </div>
                      </mat-menu>


                  </div>
                  <div fxFlex="143px" *ngIf="(selectedTab === 6)">
                      <app-select [clearable]="false" (change)="refreshUnitDrawn($event)" [items]="salesProgram"
                                  bindLabel="name" bindValue="id" [(ngModel)]="currentSalesProgram.id"
                                  placeholder="Chương trình BH">
                      </app-select>
                  </div>
              </div>
              <div>
                  <ng-container *ngIf="selectedProject?.id && selectedTab === 0">
                      <div fxFlex="160px" class="input-search" style="margin-top: 1px;">
                        <app-filter-property  [poses]="pos" [owners]="owners" (search)="onApplyFilter($event)">
                        </app-filter-property>
                      </div>

                  </ng-container>
                  <ng-container *ngIf="(selectedTab > (categories.length + 2) && selectedTab != 5 && selectedTab !== 6)">
                      <div fxFlex="200px" class="input-search">
                          <input [(ngModel)]="filterDSGD.keySearch"
                              (keyup.enter)="onChangeKeySearchDSGD(filterDSGD.keySearch, '', true)" type="text"
                              matInput class="app-input" placeholder="Tìm kiếm">
                          <img src="./assets/img/icon/search.png" class="icon-search"
                              (click)="onChangeKeySearchDSGD(filterDSGD.keySearch, '', true)">
                      </div>
                  </ng-container>
                  <ng-container *ngIf="(selectedTab === 2 || selectedTab === 3)">
                      <div fxFlex="200px" class="input-search show-filter">
                          <input (keyup.enter)="searchCode()" type="text" matInput class="app-input"
                              placeholder="Tìm kiếm" [formControl]="searchKey">
                          <img src="./assets/img/icon/search.png" class="icon-search" (click)="searchCode()">
                              <button [matMenuTriggerFor]="filter" class="icon-filter btn-filter" (click)="onSortFilter()" (menuClosed)="menuClosed(false)">
                                  <img src="./assets/img/icon/icon-filter.png">
                              </button>
                              <span *ngIf="selectPosSearchApply.length > 0 || selectStatusApply.length > 0" class="number-filters">{{selectPosSearchApply.length + selectStatusApply.length}}</span>
                          <mat-menu #filter="matMenu" class="filter-items">
                            <div class="box-filter">
                                <div class="div-filter">
                                    <p class="text-filter">Từ ngày</p>
                                    <div class="px-4">
                                        <app-date-picker (click)="$event.stopPropagation();" class="fix-input-date" [(ngModel)]="createdDateStart"></app-date-picker>
                                    </div>
                                </div>
                                <div class="div-filter">
                                    <p class="text-filter">Đến ngày</p>
                                    <div class="px-4">
                                        <app-date-picker (click)="$event.stopPropagation();" class="fix-input-date" [(ngModel)]="createdDateEnd"></app-date-picker>
                                    </div>
                                </div>
                            </div>
                              <div class="box-filter">
                                  <div class="div-filter">
                                      <p class="text-filter">Đơn vị bán hàng</p>
                                      <div class="div-filter__scroll">
                                          <button mat-menu-item *ngFor="let item of pos"
                                              (click)="onClickCheckBoxPosSearch(!item.checked, item);$event.stopPropagation();"
                                              class="filter-checkbox">
                                              <span style="position: relative; top: -2px; left: 0" (click)="$event.stopPropagation()">
                                                  <app-checkbox-button style="display: inherit;"
                                                      (outChange)="onClickCheckBoxPosSearch($event, item);$event.stopPropagation();"
                                                      [checked]="item.checked">
                                                  </app-checkbox-button>
                                              </span>
                                              <span class="text-checkbox" [matTooltip]="item.name">
                                                  {{item.name}}
                                              </span>
                                          </button>
                                      </div>
                                  </div>
                                  <div class="div-filter">
                                      <p class="text-filter">Trạng thái</p>
                                      <div class="div-filter__scroll">
                                          <button mat-menu-item *ngFor="let item of dropdownStatus"
                                              (click)="onClickCheckBoxStatus(!item.checked, item);$event.stopPropagation();"
                                              class="filter-checkbox" >
                                              <span style="position: relative; top: -2px; left: 0" (click)="$event.stopPropagation()">
                                                  <app-checkbox-button style="display: inherit;"
                                                      (outChange)="onClickCheckBoxStatus($event, item);$event.stopPropagation();"
                                                      [checked]="item.checked">
                                                  </app-checkbox-button>
                                              </span>
                                              <span class="text-checkbox" [matTooltip]="item.name">
                                                  {{item.name}}
                                              </span>
                                          </button>
                                      </div>
                                  </div>
                              </div>
                              <div class="footer-filter">
                                  <button class="btn-reset"
                                  (click)="resetFilter();$event.stopPropagation();searchCode();">
                                  <img src="./assets/img/icon/reload.svg" class="mr-5 icon">
                                  <span class="text-reset">
                                      Thiết lập lại
                                  </span>
                                  </button>
                                  <button class="btn-reset"
                                  (click)="$event.stopPropagation();filterCode();">
                                  <img src="./assets/img/icon/filter.svg" class="mr-5 icon">
                                  <span class="text-reset">
                                      Lọc
                                  </span>
                                  </button>
                              </div>
                          </mat-menu>
                      </div>
                  </ng-container>
              </div>
          </div>
          <div fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="center center">
              <div *ngIf="!startEvent || endEvent">
                  <div class="text-notice color-red" *ngIf="currentSalesProgram.lock && !showLockExtendPriority"
                      matTooltip="Giai đoạn ĐVBH được quyền ráp ưu tiên đã hết. ĐVBH không thể ráp mới/ chỉnh sửa ƯT đã ráp.">
                      <img src="./assets/img/icon/icon-notifications.svg" style="width: 16px;margin-right: 3px;">
                      Giai đoạn ráp ưu tiên đã kết thúc
                  </div>
                  <div class="text-notice color-red" *ngIf="showLockExtendPriority"
                      matTooltip="Giai đoạn ĐVBH được quyền ráp ưu tiên bổ sung đối với những sản phẩm trong rổ chung/ giao cho đơn vị.">
                      <img src="./assets/img/icon/icon-notifications.svg" style="width: 16px;margin-right: 3px;">
                      Giai đoạn ráp ưu tiên đã được mở
                  </div>
              </div>
              <span class="text-header" *ngIf="startEvent && !endEvent">
                  <ng-container *ngIf="startStage">
                      <button class="app-btn app-btn-lv1 app-btn-green mat-ripple i-center"
                          style="border-radius: 20px" *ngIf="startPriority">
                          Đang mở bán {{ (projectStage == 1) ? 'Ưu tiên ' + priority + ' - ' : '' }} Giai đoạn
                          {{projectStage}}
                      </button>
                      <button class="app-btn app-btn-lv1 app-btn-red mat-ripple i-center" style="border-radius: 20px"
                          *ngIf="!startPriority">
                          Kết thúc mở bán {{ (projectStage == 1) ? 'Ưu tiên ' + priority + ' - ' : '' }} Giai đoạn
                          {{projectStage}}
                      </button>
                  </ng-container>
                  <ng-container *ngIf="!startStage">
                      <button class="app-btn app-btn-lv1 app-btn-red mat-ripple i-center" style="border-radius: 20px">
                          Kết thúc mở bán Giai đoạn {{projectStage}}
                      </button>
                  </ng-container>
              </span>
              <span class="text-header ml-10" *ngIf="endEvent">
                  <ng-container>
                      <button class="app-btn app-btn-lv1 app-btn-red mat-ripple" style="border-radius: 20px">
                          Sự kiện đã kết thúc
                      </button>
                  </ng-container>
              </span>
              <span class="text-header" *ngIf="!isEnabledEdit && canUpdateCustomerTicket && isOrderEditableUsed">
                  <ng-container>
                      <button class="app-btn app-btn-lv1 app-btn-red mat-ripple" style="border-radius: 20px">
                          Đã khóa sửa phiếu cọc
                      </button>
                  </ng-container>
              </span>
              <span class="text-header" *ngIf="isEnabledEdit && canUpdateCustomerTicket">
                  <ng-container>
                      <button class="app-btn app-btn-lv1 app-btn-green mat-ripple" style="border-radius: 20px">
                          Mở sửa phiếu cọc
                      </button>
                  </ng-container>
              </span>
          </div>
      </div>
      <div fxLayout="row" fxLayoutAlign="space-between center" class="pr-25 fix-btn-color" fxLayoutGap="10px" *ngIf="!canViewMarketAllTicket">
          <div fxLayout="row" fxLayoutAlign="end center" [ngClass]="(!settingData.id) && 'hidden'" fxLayoutGap="10px">
              <button (click)="openEventDashboard(settingData.id)" mat-raised-button color="primary"
                  *ngIf="canViewEventProject">
                  <span>Điều khiển sự kiện</span>
              </button>
              <button mat-raised-button color="primary" (click)="handleClickInputFile(fileInput1)"
                *ngIf="selectedTab === 4 && canImportYCDCO">
                <span> Tải nhập YCDCO</span>
                <input #fileInput1 type="file" value="primary" (change)="openDialogImportYCDCO($event)"
                    accept=".xls,.xlsx" style="display:none;" />
              </button>
              <div
                  *ngIf="selectedTab === 0 && (canMovePosUnit || canRevokeUnit || canPublishStatus || canImportPrimary)">
                  <button mat-raised-button color="primary" [matMenuTriggerFor]="qlsp" class="btn-collapse">
                      Quản lý sản phẩm
                      <img src="./assets/img/icon/ic_expand_more_24px_white.png" class="icon-collapse">
                  </button>
                  <mat-menu #qlsp="matMenu">
                      <button mat-menu-item (click)="openTransferSaleProgramDialog(false)" *ngIf="canMovePosUnit">
                          Chuyển chương trình bán hàng
                      </button>
                      <button mat-menu-item (click)="openTransferDialog(false, true)" *ngIf="canMovePosUnit">
                          Chuyển sản phẩm
                      </button>
                      <button mat-menu-item (click)="openTransferDialog(true, false)" *ngIf="canRevokeUnit">
                          Thu hồi sản phẩm
                      </button>
                      <button mat-menu-item (click)="openTransferDialog(false, false, true)" *ngIf="canRemoveUnit">
                          Xóa sản phẩm
                      </button>
                      <button mat-menu-item (click)="openPublishDialog()" *ngIf="canPublishStatus">
                          Điều chỉnh trạng thái
                      </button>
                      <button mat-menu-item (click)="handleClickInputFile(fileInput1)" *ngIf="canImportPrimary">
                          <span> Tải nhập (sản phẩm/ bảng hàng) </span>
                          <input #fileInput1 type="file" value="primary" (change)="openDialogPropertyPrimary($event)"
                              accept=".xls,.xlsx" style="display:none;" />
                      </button>
                      <button mat-menu-item (click)="handleClickInputFile(fileInput2)" *ngIf="canTransferUnit">
                          <span> Tải nhập bảng giao sản phẩm </span>
                          <input #fileInput2 type="file" value="primary" (change)="openDialogTransferUnit($event)"
                              accept=".xls,.xlsx" style="display:none;" />
                      </button>

                      <button mat-menu-item (click)="handleClickInputFile(fileInput3)" *ngIf="canUpdatePrimary">
                        <span> Tải nhập cập nhật bảng hàng</span>
                        <input #fileInput3 type="file" value="primary" (change)="openDialogUpdatePropertyPrimary($event)"
                            accept=".xls,.xlsx" style="display:none;" />
                      </button>

                      <button mat-menu-item (click)="openImageDialog()" *ngIf="canUpdatePrimary">
                        <span> Tải nhập mặt bằng</span>
                        <input #fileInput4 type="file" value="primary" (change)="openDialogUpdateImagePropertyUnit($event)"
                            multiple accept="image/*" style="display:none;" />
                      </button>
                  </mat-menu>
              </div>
              <div *ngIf="selectedTab === 0 && (canUpdatePrice || canUpdatePrice)">
                  <button mat-raised-button color="primary" [matMenuTriggerFor]="qlg" class="btn-collapse">
                      Quản lý giá
                      <img src="./assets/img/icon/ic_expand_more_24px_white.png" class="icon-collapse">
                  </button>
                  <mat-menu #qlg="matMenu">
                      <button mat-menu-item (click)="handleClickInputFile(fileInput1)" *ngIf="canUpdatePrice">
                          <span> Cập nhật giá </span>
                          <input #fileInput1 type="file" value="primary" (change)="openDialogUpdatePrice($event)"
                              accept=".xls,.xlsx" style="display:none;" />
                      </button>
                      <button mat-menu-item (click)="openTransferDialog(false, false, false, true)" *ngIf="canUpdatePrice">
                          Công bố giá
                      </button>
                      <button mat-menu-item (click)="openPublishPriceHistory()" *ngIf="canUpdatePrice">
                          Lịch sử công bố giá
                      </button>
                  </mat-menu>
              </div>
              <div
                  *ngIf="selectedTab === 0 && (canLockSaleList || canUpdateProject || canDownloadPrioritiesOfPosReport)">
                  <button mat-raised-button color="primary" [matMenuTriggerFor]="qlut" class="btn-collapse">
                      Quản lý ưu tiên
                      <img src="./assets/img/icon/ic_expand_more_24px_white.png" class="icon-collapse">
                  </button>
                  <mat-menu #qlut="matMenu">
                      <button mat-menu-item *ngIf="listExtendUnit?.length > 0 && selectedTab == 0"
                          (click)="extendPriorities()">
                          <span> Ráp bổ sung ưu tiên </span>
                      </button>
                      <button mat-menu-item (click)="handleLockStatus()"
                          *ngIf="canLockSaleList && !currentSalesProgram.lock">
                          Khoá ráp ưu tiên
                      </button>
                      <button mat-menu-item (click)="handleLockEditPriorities()"
                          *ngIf="canLockSaleList && currentSalesProgram.lock">
                          Khoá chỉnh sửa ưu tiên
                      </button>
                      <button mat-menu-item (click)="handleLockExtendPos()"
                          *ngIf="canUpdateProject && showLockExtendPriority && currentSalesProgram.lock">
                          Khóa ráp bổ sung ưu tiên
                      </button>
                      <button mat-menu-item (click)="sortPriorities()" *ngIf="canUpdateProject">
                          Sắp xếp ưu tiên
                      </button>
                      <ng-container *ngIf="canDownloadPrioritiesOfPosReport">
                          <ng-container
                              *ngIf="!(isDownloadPrioritiesOfPosLoading$ | async); else elseDownloadTemplate">
                              <button mat-menu-item (click)="sendEmailEndPriority()">
                                  Gửi email xác nhận ưu tiên
                              </button>
                          </ng-container>
                      </ng-container>
                  </mat-menu>
              </div>
              <ng-container *ngIf="(selectedTab === 4 && canSendEmail)">
                  <button mat-raised-button color="primary" (click)="openEmailDialogSalesUnit()">
                      <span> Gửi email xác nhận </span>
                  </button>
              </ng-container>
              <ng-container *ngIf="(selectedTab === 4 && canCreateAccountCustomer)">
                  <button mat-raised-button color="primary" (click)="createCustomerAccount()">
                      <span> Tạo tài khoản KH </span>
                  </button>
              </ng-container>
              <ng-container *ngIf="(selectedTab === 2 && canCreateUserCare && isPublic)">
                <button mat-raised-button color="primary" (click)="createCustomerCarePlusAccount()">
                    <span> Tạo tài khoản KH PropCare</span>
                </button>
                </ng-container>
              <button mat-raised-button color="primary" (click)="handleClickInputFile(fileInput1)"
                  *ngIf="selectedTab === 0 && canImportPrimary">
                  <span> Tải nhập </span>
                  <input #fileInput1 type="file" value="primary" (change)="openDialogPropertyPrimary($event)"
                      accept=".xls,.xlsx" style="display:none;" />
              </button>
              <button mat-raised-button color="primary" (click)="openCreateTicket()"
                  *ngIf="(canCreateDemand || canCreateTicketForEmp) && selectedTab == 2">
                  <span>Tạo yêu cầu</span>
              </button>
              <div *ngIf="selectedTab !== 5 && displayDownloadMenu()">
                  <button mat-raised-button color="primary" [matMenuTriggerFor]="download" class="btn-collapse"
                      *ngIf="!(isDownloadLoadingAll$ | async); else elseDownloadTemplate">
                      Tải về
                      <img src="./assets/img/icon/ic_expand_more_24px_white.png" class="icon-collapse">
                  </button>

                  <mat-menu #download="matMenu" id="downloadMatMenu">
                      <ng-container>
                          <button mat-menu-item (click)="downloadAll()"
                              *ngIf="canDownloadListDemand && selectedTab === 2">
                              <span>Tải về YCDCH</span>
                          </button>
                      </ng-container>
                      <ng-container>
                          <button mat-menu-item (click)="downloadAll()"
                              *ngIf="canDownloadListDemand && selectedTab === 3">
                              <span>Tải về YCDCO</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="(selectedTab === 4) && canExportDSGD">
                          <button mat-menu-item (click)="downloadSaleList()">
                              <span> Tải về Danh sách giao dịch</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab == 0 && canImportPrimary">
                          <button mat-menu-item
                              (click)="downloadTemplate('assets/excel/Template-CanHo.xlsx','Template-BangHang')"
                              class="downloadMatMenu">
                              <span> Tải về biểu mẫu bảng hàng </span>
                          </button>
                      </ng-container>

                      <ng-container *ngIf="selectedTab == 0 && canUpdatePrimary">
                          <button mat-menu-item
                              (click)="downloadUpdatePrioritiesTemplate()"
                              class="downloadMatMenu">
                              <span> Tải về biểu mẫu cập nhật bảng hàng </span>
                          </button>
                      </ng-container>

                      <ng-container *ngIf="selectedTab == 0 && canDownloadPrioritiesOfPosReport">
                          <button mat-menu-item (click)="downloadPrioritiesOfPos()" class="downloadMatMenu">
                              <span> Tải về thống kê số lượng ưu tiên </span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab == 0 && canDownloadPriorityReport">
                          <div class="export-excel">
                              <button mat-menu-item (click)="downloadMatchingPriorities()" class="downloadMatMenu">
                                  <span> Tải về ráp ưu tiên </span>
                              </button>
                              <div class="from-to">
                                  <div>
                                      <label class="bold">Từ ngày</label>
                                      <div class="input-value app-form-group">
                                          <input type="text" placeholder="DD/MM/YYYY HH:mm:ss | DD-MM-YYYY HH:mm:ss"
                                              matInput class="reason-input app-input"
                                              (click)="$event.stopPropagation();" (change)="onChangeStartDate()"
                                              [(ngModel)]="startDate" />
                                      </div>
                                  </div>
                                  <div>
                                      <label class="bold">Đến ngày</label>
                                      <div class="input-value app-form-group">
                                          <input type="text" placeholder="DD/MM/YYYY HH:mm:ss | DD-MM-YYYY HH:mm:ss"
                                              matInput class="reason-input app-input"
                                              (click)="$event.stopPropagation();" (change)="onChangeDate()"
                                              [(ngModel)]="endDate" />
                                      </div>
                                  </div>
                              </div>
                          </div>
                          <ng-container *ngIf="canDownloadPriorityReport">
                              <div class="export-excel">
                                  <button mat-menu-item (click)="downloadKHTN()">
                                      <span> Tải về khách hàng tiềm năng </span>
                                  </button>
                                  <div class="from-to">
                                      <div>
                                          <label class="bold">Từ ngày</label>
                                          <div class="input-value app-form-group">
                                              <input type="text"
                                                  placeholder="DD/MM/YYYY HH:mm:ss | DD-MM-YYYY HH:mm:ss" matInput
                                                  class="reason-input app-input" (click)="$event.stopPropagation();"
                                                  (change)="onChangeStartDate()" [(ngModel)]="startDate" />
                                          </div>
                                      </div>
                                      <div>
                                          <label class="bold">Đến ngày</label>
                                          <div class="input-value app-form-group">
                                              <input type="text"
                                                  placeholder="DD/MM/YYYY HH:mm:ss | DD-MM-YYYY HH:mm:ss" matInput
                                                  class="reason-input app-input" (click)="$event.stopPropagation();"
                                                  (change)="onChangeDate()" [(ngModel)]="endDate" />
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </ng-container>
                      </ng-container>

                      <ng-container *ngIf="selectedTab == 0 && canDownloadPrimary">
                          <div class="export-excel">
                              <button mat-menu-item (click)="downloadPropertyPrimary()">
                                  <span> Tải về - Bảng hàng </span>
                              </button>
                              <div class="from-to" *ngIf="canPropertyUnitGetAll">
                                  <div>
                                      <label class="bold">Từ ngày</label>
                                      <div class="input-value app-form-group">
                                          <input type="text"
                                              placeholder="DD/MM/YYYY HH:mm:ss | DD-MM-YYYY HH:mm:ss" matInput
                                              class="reason-input app-input" (click)="$event.stopPropagation();"
                                              (change)="onChangeStartDate()" [(ngModel)]="startDate" />
                                      </div>
                                  </div>
                                  <div>
                                      <label class="bold">Đến ngày</label>
                                      <div class="input-value app-form-group">
                                          <input type="text"
                                              placeholder="DD/MM/YYYY HH:mm:ss | DD-MM-YYYY HH:mm:ss" matInput
                                              class="reason-input app-input" (click)="$event.stopPropagation();"
                                              (change)="onChangeDate()" [(ngModel)]="endDate" />
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </ng-container>
                      <ng-container *ngIf="selectedTab == 0 && canDownloadDmsp">
                          <button mat-menu-item (click)="downloadDmsp()">
                              <span> Tải về biểu mẫu cập nhật giá </span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 0 && canTransferUnit">
                          <button mat-menu-item
                              (click)="downloadTransferTemplate('assets/excel/Template-GiaoSanPham.xlsx','Template-GiaoSanPham')"
                              class="downloadMatMenu">
                              <span> Tải về biểu mẫu giao sản phẩm </span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="(selectedTab === 2 || selectedTab === 3) && canDownloadListDemand">
                          <button mat-menu-item (click)="downloadBKCT()">
                              <span>Tải bảng kê chuyển tiền</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="(selectedTab === 2 || selectedTab === 3) && canDownloadListDemand">
                          <button mat-menu-item (click)="downloadRefund()">
                              <span>Tải bảng kê hoàn tiền</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="(selectedTab === 3 && canImportTransferEscrow)">
                          <button mat-menu-item>
                              <span (click)="downloadTemplateChuyenCoc()">
                                  Tải biểu mẫu chuyển cọc</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 4 && canDownloadSaleList">
                          <button mat-menu-item (click)="downloadDanhsachGD()">
                              <span>Tải về bảng kê xác nhận giao dịch</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 4 && canViewAllTicket">
                          <button mat-menu-item (click)="downloadDanhsachSPGD()">
                              <span>Tải về bảng kê sản phẩm giao dịch</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 4 && canDownloadListDemand">
                          <button mat-menu-item (click)="downloadTTTV()">
                              <span>Tải bảng kê thỏa thuận tư vấn</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 4 && canDownloadListDemand">
                          <button mat-menu-item (click)="downloadTTTVCam()">
                              <span>Tải bảng kê TTDB (chờ bổ sung HS)</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 4 && canImportTransferEscrow">
                          <button mat-menu-item (click)="downloadChuyenCocERP()">
                              <span>Tải về chuyển cọc ERP</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 4 && canViewAllCustomer">
                          <button mat-menu-item (click)="downloadDanhsachKHGD()">
                              <span>Tải về Bảng kê KH GD</span>
                          </button>
                      </ng-container>
                      <ng-container *ngIf="selectedTab === 4 && canImportYCDCO">
                          <button mat-menu-item (click)="downloadTemplateYCDCO('assets/excel/Template-YCDCO.xlsx','Template-YCDCO')">
                              <span>Tải về biểu mẫu YCDCO</span>
                          </button>
                      </ng-container>
                  </mat-menu>
              </div>
              <div *ngIf="(selectedTab === 4 && canImportTransferEscrow)">
                  <button mat-raised-button color="primary" (click)="handleClickInputFile(fileInput1)" class="mr-10">
                      <span> Tải nhập chuyển cọc</span>
                      <input #fileInput1 type="file" value="primary" (change)="importTicketEscrow($event)"
                          accept=".xls,.xlsx" style="display:none;" />
                  </button>
              </div>

            <div *ngIf="selectedTab === 0 && registerConsignmentUrl">
                <button mat-raised-button color="warn"  (click)="goToRegisterConsignmentPage()">
                    <span>Ký gửi</span>
                </button>
            </div>
            <div *ngIf="selectedTab === 0 && documentUrl">
                <button mat-raised-button color="warn"  (click)="goToDocumentUrlPage()">
                    <span>Hướng dẫn, Tài liệu</span>
                </button>
            </div>
            <div *ngIf="selectedTab === 2 && canSynErpTicket && isSyncCRMInvestor">
                <button mat-raised-button color="warn"  (click)="syncBooking(null)">
                    <span>Đồng bộ YCDCH</span>
                </button>
            </div>
            <div *ngIf="selectedTab === 2 && canSynErpTicket && isSyncCRMInvestor">
                <button mat-raised-button color="warn"  (click)="syncBooking(null, true)">
                    <span>Đồng bộ Hủy YCDCH</span>
                </button>
            </div>
            <div *ngIf="selectedTab === 3 && canSynErpTicket && isSyncCRMInvestor">
                <button mat-raised-button color="warn"  (click)="syncEscrow(null)">
                    <span>Đồng bộ YCDCO</span>
                </button>
            </div>
              <div *ngIf="selectedTab === 4 && isHasPermisstionEdit && !isOrderEditableUsed && !isEnabledEdit">
                  <button mat-raised-button color="primary" (click)="toggleEditTicket(true)">
                      <span>Mở sửa Phiếu cọc</span>
                  </button>
              </div>
              <div *ngIf="selectedTab === 4 && isHasPermisstionEdit && !isOrderEditableUsed && isEnabledEdit">
                  <button mat-raised-button color="primary" (click)="toggleEditTicket(false)">
                      <span>Khóa sửa Phiếu cọc</span>
                  </button>
              </div>
                <div *ngIf="selectedTab === 4 && canTicketCreateDocuments && currentSalesProgram.allowSendCustomerStartPriority">
                    <button mat-raised-button color="primary"  *ngIf="!(isTicketCreateDocuments$ | async); else elseDownloadTemplate"  (click)="createDocumentsForCustomer()">
                        <span>Tạo hồ sơ khách hàng</span>
                    </button>
                </div>
                <div *ngIf="selectedTab === 4 && canCsSendSmsCusConfirm && currentSalesProgram.customerConfirmRequired">
                    <button mat-raised-button color="primary" *ngIf="!(isSendSmsCustomer$ | async); else elseDownloadTemplate"  (click)="openSendSmsCustomerDialog()">
                        <span> Gửi SMS Khách hàng</span>
                    </button>
                </div>
          </div>

      </div>
  </div>
  <div class="table-area fix-scroll" [ngClass]="(!settingData.id) && 'hidden'">
      <mat-tab-group class="mat-tab-group--flex" [class]="isCollapse ? 'isCollapse' : ''"
          [selectedIndex]="selectedTab" (selectedIndexChange)="changeTab($event)" id="tabGroup">

          <mat-tab style="height: 100%;" label="Bảng hàng" [disabled]="!canDownloadPrimary">
              <div [class.hide]="!(isTableLoading$ | async)" fxLayout="column" fxFlex="100"
                  fxLayoutAlign="center center" class="mh-500">
                  <mat-spinner color="primary" class="mat-spinner"></mat-spinner>
                  <h4>loading...</h4>
              </div>

              <div class="can-ho-table" [class.hide]="(isTableLoading$ | async)">

                  <div class="count-table" fxLayout="row" fxLayoutAlign="space-between center" *ngIf="!canViewMarketAllTicket">
                      <div class="count-table__inner">
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step1'}"
                              (click)="onFilterStatusBangHang('');step='step1'" matRipple matTooltip="Tất cả"> <img
                                  src="./assets/img/icon/sum.svg" alt="" height="18px">
                              <div>Tất cả</div>
                          </span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step2'}"
                          (click)="onFilterStatusBangHang('LOCK');step='step2'" matRipple
                          [matTooltip]="getTooltipUnitStatus('LOCK')"> <span class="lock"> {{countPrimaryStatus.lock}}
                          </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step2'}"
                              (click)="onFilterStatusBangHang('CLOSE');step='step2'" matRipple
                              [matTooltip]="getTooltipUnitStatus('CLOSE')"> <span class="close"> {{countPrimaryStatus.close}}
                              </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step3'}"
                              (click)="onFilterStatusBangHang('COMING');step='step3'" matRipple
                              [matTooltip]="getTooltipUnitStatus('COMING')"> <span class="coming"> {{countPrimaryStatus.coming}}
                              </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step4'}"
                              (click)="onFilterStatusBangHang('PROCESSING');step='step4'" matRipple
                              [matTooltip]="getTooltipUnitStatus('PROCESSING')">
                              <span class="processing"> {{countPrimaryStatus.processing}} </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step4'}"
                          (click)="onFilterStatusBangHang('LOCK_CONFIRM');step='step4'" matRipple
                          [matTooltip]="getTooltipUnitStatus('LOCK_CONFIRM')">
                          <span class="lock-confirm"> {{countPrimaryStatus.lockConfirm}} </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step5'}"
                              (click)="onFilterStatusBangHang('CONFIRM');step='step5'" matRipple
                              [matTooltip]="getTooltipUnitStatus('CONFIRM')">
                              <span class="confirm"> {{countPrimaryStatus.confirm}} </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step6'}"
                              (click)="onFilterStatusBangHang('SUCCESS');step='step6'" matRipple
                              [matTooltip]="getTooltipUnitStatus('SUCCESS')">
                              <span class="success"> {{countPrimaryStatus.success}} </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step7'}"
                              (click)="onFilterStatusBangHang('CANCEL');step='step7'" [matTooltip]="getTooltipUnitStatus('CANCEL')">
                              <span class="cancel"> {{countPrimaryStatus.cancel}} </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step8'}"
                              (click)="onFilterStatusBangHang('CLOSE', 'TRANSFER');step='step8'" matRipple  [matTooltip]="getTooltipUnitStatus('TRANSFER')">
                              <span class="transfer"> {{countPrimaryStatus?.transfer}} </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step9'}"
                              (click)="onFilterStatusBangHang('SUCCESS',null,true);step='step9'" matRipple  [matTooltip]="getTooltipUnitStatus('DEPOSIT')">
                              <span class="deposit"> {{countPrimaryStatus?.deposit}} </span></span>
                          <span *ngIf="canPropertyUnitGetAll" class="status" [ngClass]="{'active': step==='step10'}"
                              (click)="onFilterStatusBangHang('MOVED');step='step10'" matRipple [matTooltip]="getTooltipUnitStatus('MOVED')">
                              <span class="moved"> {{countPrimaryStatus?.moved}} </span></span>
                          <div *ngIf="canPropertyUnitGetAll">
                              <button [matMenuTriggerFor]="dvbh" class="btn-select" (click)="onSortFilter()" (menuClosed)="menuClosed()">
                                  Đơn vị bán hàng
                                  <span *ngIf="selectPosApply.length > 0" class="number-filters">{{selectPosApply.length}}</span>
                                  <img src="./assets/img/icon/ic_expand_more_24px.svg" class="icon-show">
                              </button>
                              <mat-menu #dvbh="matMenu" class="pos-items">
                                  <button mat-menu-item *ngFor="let item of posFilter"
                                      (click)="onClickCheckBoxPos(!item.checked, item);$event.stopPropagation();"
                                      class="btn-checkbox">
                                      <span style="position: relative; top: -2px; left: 0"
                                          (click)="$event.stopPropagation();">
                                          <app-checkbox-button style="display: inherit;"
                                              (outChange)="onClickCheckBoxPos($event, item)"
                                              [checked]="item.checked"
                                              >
                                          </app-checkbox-button>
                                      </span>
                                      {{item.name}}
                                  </button>
                                  <div fxLayout="row" fxLayoutAlign="center" class="clear-btn">
                                      <button mat-menu-item (click)="onClearCheckboxPos();$event.stopPropagation();">
                                          <img src="./assets/img/icon/reload.svg" class="icon">
                                          <span class="text-checkbox">Thiết lập lại</span>
                                      </button>
                                      <button mat-menu-item  (click)="searchTableData(true);$event.stopPropagation();">
                                          <img src="./assets/img/icon/filter.svg" class="icon">
                                          <span class="text-checkbox">Lọc</span>
                                      </button>
                                  </div>
                              </mat-menu>
                          </div>
                          <div *ngIf="canPropertyUnitGetAll" class="group-action">
                              <ng-container
                                  *ngIf="!(isDownloadTicketLoadingTemplate$ | async); else elseDownloadTemplate">
                                  <ng-container *ngIf="canLockSaleList">
                                      <div class="app-button-icon show-action" *ngIf="templateFileHasStatus?.length > 0">
                                          <img src="assets/img/icon/printing_dark.svg" class="show-action-print">
                                          <img src="assets/img/icon/printing_blue_v2.svg"
                                              class="show-action-print-hover" (click)="printFileStatus()">
                                          <div class="action">
                                              <div class="action__item border-bottom" fxLayout="row"
                                                  fxLayoutAlign="space-between center">
                                                  <label fxFlex="30" class="bold">Từ vị trí</label>
                                                  <div class="input-value app-form-group" fxLayout="row">
                                                      <input type="text" placeholder="Bắt đầu từ 1" matInput
                                                          class="reason-input app-input" [(ngModel)]="pdfStart" />
                                                  </div>
                                              </div>
                                              <div class="action__item border-bottom" fxLayout="row"
                                                  fxLayoutAlign="space-between center">
                                                  <label fxFlex="30" class="bold">Số lượng</label>
                                                  <div class="input-value app-form-group" fxLayout="row">
                                                      <input type="text" placeholder="SL không quá 100" matInput
                                                          class="reason-input app-input" [(ngModel)]="pdfEnd" />
                                                  </div>
                                              </div>
                                          </div>
                                      </div>
                                      <div class="app-button-icon show-action" *ngIf="templateFileHasStatus?.length > 0">
                                          <img src="assets/img/icon/download_dark.svg" class="show-action-print">
                                          <img src="assets/img/icon/download_blue.svg" class="show-action-print-hover"
                                              (click)="downloadFileStatus()">
                                          <div class="action">
                                              <div class="action__item border-bottom" fxLayout="row"
                                                  fxLayoutAlign="space-between center">
                                                  <label fxFlex="30" class="bold">Từ vị trí</label>
                                                  <div class="input-value app-form-group" fxLayout="row">
                                                      <input type="text" placeholder="Từ vị trí" matInput
                                                          class="reason-input app-input" [(ngModel)]="pdfStart" />
                                                  </div>
                                              </div>
                                              <div class="action__item border-bottom" fxLayout="row"
                                                  fxLayoutAlign="space-between center">
                                                  <label fxFlex="30" class="bold">Số lượng</label>
                                                  <div class="input-value app-form-group" fxLayout="row">
                                                      <input type="text" placeholder="Số lượng" matInput
                                                          class="reason-input app-input" [(ngModel)]="pdfEnd" />
                                                  </div>
                                              </div>
                                          </div>
                                      </div>
                                      <button mat-button [matMenuTriggerFor]="menuDN" (click)="onClickViewFilter()"
                                          *ngIf="canPropertyUnitGetAll"
                                          style="color: #197CF4; line-height: unset; font-weight: unset; padding: 3px 5px;">
                                          <img src="./assets/img/icon/filter.svg">
                                          <span class="hidden-xl">
                                              Lọc ưu tiên
                                          </span>
                                          <!-- <mat-icon *ngIf="filterBangHang.priority" class="filter-close"
                                              (click)="onRevoveFilterPriority(); $event.stopPropagation();">close
                                          </mat-icon> -->
                                      </button>
                                      <mat-menu #menuDN="matMenu">
                                          <button mat-menu-item
                                              *ngFor="let item of filterPriorityOptions"
                                              (click)="onClickCheckBoxChange(!item.checked, item);$event.stopPropagation();">
                                              <span style="position: relative; top: -2px; left: 0"
                                                  (click)="$event.stopPropagation();">
                                                  <app-checkbox-button style="display: inherit;"
                                                      (outChange)="onClickCheckBoxChange($event, item)" [checked]="item.checked">
                                                  </app-checkbox-button>
                                              </span>
                                              {{item.name}}
                                          </button>
                                      </mat-menu>
                                  </ng-container>
                              </ng-container>
                          </div>
                          <div *ngIf="currentSalesProgram.unitTableTemplateUrl && currentSalesProgram.unitTableTemplateUrl.length > 1">
                            <div class="app-form-group" style="min-width: 150px;">
                                <ng-select [(ngModel)]="currentTemplateCode" [items]="currentSalesProgram.unitTableTemplateUrl" [clearable]="viewTableModeValue == 2"
                                    bindLabel="templateName" searchable="false"
                                    bindValue="templateCode" (change)="onTemplateChange($event, true)" (clear)="onTemplateChange($event, true)">
                                </ng-select>
                            </div>
                          </div>
                      </div>

                      <div fxLayout="row" class="view-style">
                          <div class="hidden-xl">Xem theo dạng: &nbsp;</div>
                          <div fxLayout="column" fxLayoutGap="4px">
                              <span class=slide-tabs>
                                  <span *ngFor="let viewMode of ViewTableModes" class="slide-tab"
                                      (click)="changeViewTableMode(viewMode.value)"
                                      [ngClass]="{'active': (viewTableModeValue === viewMode.value)}">
                                      {{ viewMode.text }}
                                  </span>
                              </span>
                          </div>
                      </div>

                  </div>

                  <main class="can-ho-table__main" [ngClass]="isCollapse ? 'can-ho-table__collapse' : ''" *ngIf="selectedTab === 0">
                      <ng-container *ngIf="viewTableModeValue === 1; else templateModeCanHo">
                          <ng-container
                              *ngIf="currentSalesProgram && currentSalesProgram.hasUnitExcelTemplate; else templateTable">
                              <app-unit-table [propertyUnits]="propertyArrModelFilter" [marketPos] = "marketPos"
                                  [currentSalesProgram]="currentSalesProgram" [templateCode]="currentTemplateCode" [templateName]="currentTemplateName"
                                  [settingProject]="project ? project.setting : null" [recoveryUnitId]="recoveryUnit"
                                  (openPriorityPopup)="openPopup($event)" (recount)="recount()" #unitTable></app-unit-table>
                          </ng-container>

                          <ng-template #templateTable>
                              <div class="can-ho-table__main__map" fxLayout="row" fxLayoutGap="12px"
                                  [ngClass]="(propertyArrModel.length === 0) && 'hidden'">
                                  <div fxFlex="auto" class="draw-blocks" [style.height]="(drawBlocksHeight)"
                                      fxLayout="row" fxLayoutGap="30px">
                                      <ng-container *ngFor="let propsBlock of canHoDraw; let indexBlock = index">
                                          <div *ngIf="blockFloorRoom[indexBlock].isDisplay" class="block-main">
                                              <div class="title-block pointer" (click)="viewDetailStatistic(indexBlock)" > BLOCK {{blockFloorRoom[indexBlock].block}}</div>
                                              <div class="draw-row">
                                                <div class="draw-cell-title draw-cell h-20"></div>
                                                <div *ngFor="let i of [].constructor(propsBlock.maxRoomsOneFloor); let index = index">
                                                    <div class="draw-cell event-cell h-20 pointer" (click)="viewDetailStatistic(indexBlock, -1, index)">
                                                        <div class="draw-cell-item cell-position cell-floor">
                                                            <b>{{ blockFloorRoom[indexBlock].rooms[index] }}</b>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="number-priorities-floor" *ngIf="canMatchAllUnit"></div>
                                              </div>
                                              <div *ngFor="let props of propsBlock.floors; let indexFloor = index" class="draw-row">
                                                  <div class="draw-cell-title draw-cell h-20 pointer" (click)="viewDetailStatistic(indexBlock, indexFloor)">
                                                      <div fxLayoutAlign="start center" class="cell-floor">
                                                          <b>{{blockFloorRoom[indexBlock].floors[indexFloor]}}</b>
                                                      </div>
                                                  </div>
                                                  <div [ngClass]="{'pointer': jsonCanHo[prop.queryJson]}"
                                                      class="draw-cell event-cell h-20 relative"
                                                      *ngFor="let prop of props; let indexRoom = index"
                                                      (click)="openPopup(jsonCanHo[prop.queryJson])"
                                                      [style.background-color]="jsonCanHo[prop.queryJson] ? jsonCanHo[prop.queryJson].bgColor : '#FFFFFF'"
                                                      [style.opacity]="getOpacity(jsonCanHo[prop.queryJson])"
                                                      style="height: 25px; display: flex; justify-content: center;"
                                                  >
                                                      <div class="draw-cell-item cell-position">
                                                          <div [style.text-decoration]="jsonCanHo[prop.queryJson] && jsonCanHo[prop.queryJson].posId ? '' : 'underline'"
                                                              [style.color]="jsonCanHo[prop.queryJson] ? jsonCanHo[prop.queryJson].textColor : ''">
                                                              <b>{{jsonCanHo[prop.queryJson] && jsonCanHo[prop.queryJson].room}}</b>
                                                          </div>
                                                      </div>
                                                      <i class='fa fa-usd fs-small' *ngIf="jsonCanHo[prop.queryJson] && (!jsonCanHo[prop.queryJson]?.price && !jsonCanHo[prop.queryJson]?.priceVat)"></i>
                                                      <i class='fa fa-usd fs-small dolar-green' *ngIf="!jsonCanHo[prop.queryJson]?.publishPrice && (jsonCanHo[prop.queryJson]?.price || jsonCanHo[prop.queryJson]?.priceVat)"></i>
                                                  </div>
                                                  <div class="number-priorities-floor" *ngIf="canMatchAllUnit">
                                                      {{numberPrioritiesOfFloor[blockFloorRoom[indexBlock].block + '-' + blockFloorRoom[indexBlock].floors[indexFloor]]}}
                                                  </div>
                                              </div>
                                          </div>
                                      </ng-container>
                                  </div>
                              </div>
                          </ng-template>
                      </ng-container>


                      <ng-template #templateModeCanHo>
                          <div id="table-can-ho" class="app-table-container app-table-container--flex">
                              <div class="app-table-container-header" id="table-header">
                                  <div fxFlex="5" class="app-table-cell" *ngIf="canPropertyUnitGetAll">
                                      <span>STT</span>
                                  </div>
                                  <div fxFlex="20" class="app-table-cell">
                                      <span> Thông Tin Sản Phẩm </span>
                                  </div>
                                  <div fxFlex="25" class="app-table-cell">
                                      <span> Ưu tiên 1({{countPriorities.p1}})</span>
                                  </div>
                                  <div fxFlex="25" class="app-table-cell">
                                      <span> Ưu tiên 2({{countPriorities.p2}})</span>
                                  </div>
                                  <div fxFlex="25" class="app-table-cell">
                                      <span> Ưu tiên 3({{countPriorities.p3}})</span>
                                  </div>
                                  <div fxFlex="7" class="app-table-cell">
                                      <app-checkbox-button (outChange)="onChangeCheckboxAll($event)" class="d-block"
                                          [(ngModel)]="cbPriorityAll"
                                          *ngIf="currentSalesProgram.lock && canLockSaleList">
                                      </app-checkbox-button>
                                      <span *ngIf="listExtendUnit.length > 0">{{listExtendUnit.length}}</span>
                                  </div>
                              </div>

                              <div class="app-table-container-body app-table-container-body--overflow-auto">
                                  <div class="app-table-container-body-row"
                                      *ngFor="let item of propertyOfPriorityFilter;let idx = index">
                                      <div fxFlex="5" style="align-items: center;" class="app-table-cell"
                                          *ngIf="canPropertyUnitGetAll">
                                          <span class="bh-stt">{{(idx + 1)}}</span>
                                      </div>

                                      <div fxFlex="20" class="app-table-cell" style="display: block;">
                                          <span (click)="openPopup(item)" class="title-bh"
                                              [style.background-color]="item.bgColor"
                                              [style.opacity]="getOpacity(item)"
                                              [style.text-decoration]="item && item.posId ? '' : 'underline'"
                                              [style.color]="item.textColor ? item.textColor : ''">
                                              {{item.code}}
                                          </span>
                                          <div> {{item.block}} | {{ item.floor }} </div>
                                      </div>
                                      <div fxFlex="25" style="display: block;" class="app-table-cell">
                                          <ng-container
                                              *ngIf="item?.priorities[0]?.id && item?.priorities[0].customerName">
                                              <p class="fw-bold"> {{item?.priorities[0].customerName}} </p>
                                              <p> TVV: {{item?.priorities[0].employeeName}} </p>
                                              <p> ĐVBH: {{item?.priorities[0].posName}} </p>
                                              <p> Mã YCDCH: {{item?.priorities[0].bookingTicketCode}} </p>
                                              <p> Cập nhập :
                                                  {{item?.priorities[0].modifiedDate | date: 'dd-MM-yyyy hh:mm:ss'}}
                                              </p>
                                          </ng-container>
                                          <ng-container *ngIf="isExtendPriority(item, 0) && canMatchUnit">
                                              <button mat-raised-button color="primary" (click)="openPopup(item)">
                                                  <span> Bổ sung UT </span>
                                              </button>
                                              <p> ĐVBH: {{item?.priorities[0].extPosName}} </p>
                                          </ng-container>
                                      </div>
                                      <div fxFlex="25" style="display: block;" class="app-table-cell">
                                          <ng-container
                                              *ngIf="item?.priorities[1]?.id && item?.priorities[1].customerName">
                                              <p class="fw-bold"> {{item?.priorities[1].customerName}} </p>
                                              <p> TVV: {{item?.priorities[1].employeeName}} </p>
                                              <p> ĐVBH: {{item?.priorities[1].posName}} </p>
                                              <p> Mã YCDCH: {{item?.priorities[1].bookingTicketCode}} </p>
                                              <p> Cập nhập :
                                                  {{item?.priorities[1].modifiedDate | date: 'dd-MM-yyyy hh:mm:ss'}}
                                              </p>
                                          </ng-container>
                                          <ng-container *ngIf="isExtendPriority(item, 1) && canMatchUnit">
                                              <button mat-raised-button color="primary" (click)="openPopup(item)">
                                                  <span> Bổ sung UT </span>
                                              </button>
                                              <p> ĐVBH: {{item?.priorities[1].extPosName}} </p>
                                          </ng-container>
                                      </div>
                                      <div fxFlex="25" style="display: block;" class="app-table-cell">
                                          <ng-container
                                              *ngIf="item?.priorities[2]?.id && item?.priorities[2].customerName">
                                              <p class="fw-bold"> {{item?.priorities[2].customerName}} </p>
                                              <p> TVV: {{item?.priorities[2].employeeName}} </p>
                                              <p> ĐVBH: {{item?.priorities[2].posName}} </p>
                                              <p> Mã YCDCH: {{item?.priorities[2].bookingTicketCode}} </p>
                                              <p> Cập nhập :
                                                  {{item?.priorities[2].modifiedDate | date: 'dd-MM-yyyy hh:mm:ss'}}
                                              </p>
                                          </ng-container>
                                          <ng-container *ngIf="isExtendPriority(item, 2) && canMatchUnit">
                                              <button mat-raised-button color="primary" (click)="openPopup(item)">
                                                  <span> Bổ sung UT </span>
                                              </button>
                                              <p> ĐVBH: {{item?.priorities[2].extPosName}} </p>
                                          </ng-container>
                                      </div>
                                      <div fxFlex="7" style="display: block;" class="app-table-cell">
                                          <app-checkbox-button (outChange)="onChangeCheckbox($event, item)"
                                              [ngModel]="item.cbExtendtion" *ngIf="showCheckboxExtendablePos(item)">
                                          </app-checkbox-button>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </ng-template>
                  </main>
                  <ng-container *ngIf="currentSalesProgram && !currentSalesProgram.hasUnitExcelTemplate">

                      <div [ngClass]="(propertyArrModel.length > 0 || !canImportPrimary) && 'hidden'" class="mh-500"
                          fxLayoutAlign="center center" fxLayout="column">
                          <h2 class="mb-15">Vui lòng tải lên bảng hàng</h2>

                          <button mat-raised-button color="primary" (click)="handleClickInputFile(fileInput1)"
                              *ngIf="selectedTab === 0 && canImportPrimary">
                              <span> Tải nhập </span>
                              <input #fileInput1 type="file" value="primary"
                                  (change)="openDialogPropertyPrimary($event)" accept=".xls,.xlsx"
                                  style="display:none;" />
                          </button>
                      </div>
                  </ng-container>

                  <div *ngIf="(propertyArrModel.length === 0 && !canImportPrimary) && project && !project.hasUnitExcelTemplate"
                      class="mh-500" fxLayoutAlign="center center" fxLayout="column">
                      <h2 class="mb-15">Không có sản phẩm</h2>
                  </div>
              </div>

          </mat-tab>

          <mat-tab label="Danh sách YCTV">
              <div [class.hide]="!(isTableLoading$ | async)" fxLayout="column" fxFlex="100"
                  fxLayoutAlign="center center" class="mh-500">
                  <mat-spinner color="primary" class="mat-spinner"></mat-spinner>
                  <h4>loading...</h4>
              </div>
              <div [class.hide]="(isTableLoading$ | async)" *ngIf="canViewListDemand">
                  <div class="app-table-container" [ngClass]="pagination.itemsSize === 0 && 'hidden'">

                      <div fxLayout="row" fxFlexAlign="space-between start" fxLayoutGap.gt-lg="2%"
                          class="app-table-container-header">
                          <div fxFlex="60px" class="app-table-cell">
                              <span> STT </span>
                          </div>
                          <div fxFlex="33.33" class="app-table-cell">
                              <span> Mã </span>
                          </div>
                          <div fxFlex="33.33" class="app-table-cell">
                              <span> Mã sản phẩm </span>
                          </div>
                          <div fxFlex="33.33" class="app-table-cell">
                              <span> Khách hàng </span>
                          </div>
                          <div fxFlex="100px" class="app-table-cell">
                              <span> Số điện thoại </span>
                          </div>

                          <div fxFlex="100px" class="app-table-cell">
                              <span>Số CMND/HC</span>
                          </div>

                          <div fxFlex="140px" class="app-table-cell">
                              <span> Thời gian </span>
                          </div>
                          <div fxFlex="100px" class="app-table-cell">
                              <span> Hành động </span>
                          </div>
                      </div>

                      <div class="app-table-container-body" [ngClass]="pagination.items.length === 0 && 'hidden'">
                          <div fxLayout="row" fxFlexAlign="space-between start" fxLayoutGap.gt-lg="2%"
                              class="app-table-container-body-row"
                              *ngFor="let ticket of pagination.items;let idx = index">
                              <div fxFlex="60px" class="app-table-cell">
                                  <span>{{(idx + 1) + ((pagination.page - 1) * pagination.pageSize)}}</span>
                              </div>

                              <div fxFlex="33.33" class="app-table-cell" *ngIf="!isHideInfoTicket(ticket)">
                                  <a href="javascript:;" (click)="openTicketNav(ticket)" style="color: #197CF4;">
                                      {{ticket.code}}
                                  </a>
                              </div>
                              <div fxFlex="33.33" class="app-table-cell" *ngIf="isHideInfoTicket(ticket)">
                                  {{ticket.code}}
                              </div>
                              <div fxFlex="33.33" class="app-table-cell">
                                  <span>{{ticket.propertyCode}}</span>
                              </div>
                              <div fxFlex="33.33" class="app-table-cell">
                                  <span>{{ticket.customerName}}</span>
                              </div>
                              <div fxFlex="100px" class="app-table-cell">
                                  <span>{{ticket.phone}}</span>
                              </div>

                              <div fxFlex="100px" class="app-table-cell">
                                  <span>{{ticket.identity}}</span>
                              </div>

                              <div fxFlex="140px" class="app-table-cell">
                                  <span>{{ticket.createdAt}}</span>
                              </div>

                          </div>
                      </div>
                  </div>

                  <app-paging [data]="pagination" (dataChange)="onChangePaging($event)"></app-paging>
                  <div [ngClass]="pagination.items.length > 0 && 'hidden'" class="mh-500"
                      fxLayoutAlign="center center">
                      Không có yêu cầu
                  </div>
              </div>
          </mat-tab>

          <mat-tab label="Danh sách YCDCH">

              <div class="count-table" fxLayout="row" fxLayoutAlign="space-between center"
                  [ngClass]="pagination.itemsSize === 0 && 'hidden'" [class.hide]="(isTableLoading$ | async)">
                  <div fxLayout="row">
                      <h4> Tổng SL yêu cầu: &nbsp;</h4>
                      <span [ngClass]="{'underline': true}" class="status" matRipple matTooltip="Tất cả">
                          <span class="">
                              {{ pagination?.total}}
                          </span>
                      </span>
                      <h4>Giữ chỗ thành công: &nbsp;</h4>
                      <span class="status" matTooltip="Giữ chỗ thành công">
                          <span class="coming">
                              {{countTicketStatus.bookingApproved}}
                          </span>
                      </span>
                      <h4>Đã ráp ưu tiên: &nbsp;</h4>
                      <span class="status" matTooltip="Đã ráp ưu tiên">
                          <span class="confirm">{{countTicketStatus.priorityUpdated}}
                          </span>
                      </span>
                      <!-- <h4>Đã hủy: &nbsp;</h4>
          <span class="status" matTooltip="Đã hủy"> <span class="close">{{countTicketStatus.cancelled}} </span></span> -->
                      <h4>Khác: &nbsp;</h4>
                      <span class="status" matTooltip="Khác"> <span class="close">{{countTicketStatus.processing}}
                          </span></span>

                  </div>

                  <div fxFlex="50" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="16px" class="hidden-xl">
                      <div class="process" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px"
                          (click)="openProcess()">
                          <img class="process__icon" src="./assets/img/dxres/process.svg" alt="" width="20px"
                              height="20px">
                          <div class="process__text">
                              Quy trình đặt chỗ & thu tiền
                          </div>
                      </div>
                      <div class="process" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px"
                          (click)="openProcessRefund()">
                          <img class="process__icon" src="./assets/img/dxres/process.svg" alt="" width="20px"
                              height="20px">
                          <div class="process__text">
                              Quy trình huỷ chỗ & hoàn tiền
                          </div>
                      </div>
                  </div>
              </div>
              <div class="d-block">
                  <div [class.hide]="!(isTableLoading$ | async)" fxLayout="column" fxFlex="100"
                      fxLayoutAlign="center center" class="mh-100vh">
                      <mat-spinner color="primary" class="mat-spinner"></mat-spinner>
                      <h4>loading...</h4>
                  </div>
                  <div *ngIf="isTableLoading$">
                      <ag-grid-angular [ngClass]="isCollapse ? 'collapse-grid' : ''" [getRowNodeId]="getRowNodeId"
                          [components]="components" id="grid-ycdch" class="ag-theme-balham grid-respon"
                          [rowData]="rowData" [gridOptions]="gridOptions" [animateRows]="true"
                          (firstDataRendered)="onFirstDataRendered($event)">
                      </ag-grid-angular>
                  </div>
                  <app-paging [data]="pagination" (dataChange)="onChangePaging($event)" *ngIf="isTableLoading$">
                  </app-paging>
                  <ng-template #greetCell let-row>
                      <div fxLayout="column" fxLayoutGap="3px">
                          <div fxLayout="row" fxLayoutGap="3px">
                              <ng-container *ngIf="isTicketEditable(row)">
                                  <a href="javascript:;" matRipple matTooltip="Sửa" class="app-button-icon"
                                      (click)="openEditTicket(row)">
                                      <img src="assets/img/icon/edit.svg">
                                  </a>
                              </ng-container>
                              <ng-container *ngIf="isTicketOwner(row)">

                                  <a href="javascript:;" matRipple matTooltip="Copy" class="app-button-icon"
                                      (click)="onCopyYCDCH(row)">
                                      <img src="assets/img/icon/sheet.svg">
                                  </a>

                              </ng-container>
                              <ng-container *ngIf="isTicketCancelable(row)">
                                  <a ngif href="javascript:;" matRipple matTooltip="Hủy" class="app-button-icon"
                                      (click)="onCancelYCDCH(row)">
                                      <img src="assets/img/icon/delete.svg">
                                  </a>
                              </ng-container>
                              <ng-container *ngIf="isSuperAdmin">
                                <a href="javascript:;" matRipple matTooltip="Sửa" class="app-button-icon"
                                    (click)="openEditTicketBySuperAdmin(row)">
                                    <img src="assets/img/icon/edit-red.svg">
                                </a>
                            </ng-container>
                          </div>
                      </div>
                  </ng-template>
                  <ng-template #recieptCode let-row>
                      <span [innerHTML]="row?.recieptCode"></span>
                  </ng-template>
                  <ng-template #recieptStatus let-row>
                      <span [innerHTML]="row?.recieptStatus"></span>
                  </ng-template>
                  <ng-template #recieptAmount let-row>
                    <span [innerHTML]="row?.recieptAmount"></span>
                  </ng-template>
                  <ng-template #ticketSanName let-row>
                      <div class="app-table-cell">
                          <span>{{row?.pos?.name}}</span>
                      </div>
                  </ng-template>
                  <ng-template #ticketDisplayStatus let-row>
                      <div class="app-table-cell">
                          <span>{{row.status | txnStatus}}</span>
                      </div>
                  </ng-template>
                  <ng-template #ticketDisplayName let-row>
                      <div class="app-table-cell" >
                          <a href="javascript:;" (click)="openTicketNav(row)"
                              class="color-a">{{getTicketDisplayName(row)}}</a>
                      </div>
                      <!-- <div class="app-table-cell" *ngIf="isHideInfoTicket(row)">
                          {{getTicketDisplayName(row)}}
                      </div> -->
                  </ng-template>
              </div>
          </mat-tab>

          <mat-tab label="Danh sách YCDCO">

              <div class="count-table" fxLayout="row" fxLayoutAlign="space-between center"
                  [ngClass]="pagination.itemsSize === 0 && 'hidden'" [class.hide]="(isTableLoading$ | async)">
                  <div>
                      <span class="app-highlight-text">
                          {{ pagination?.total ? pagination.total + ' yêu cầu': ''}}
                      </span>
                  </div>

                  <div fxFlex="50" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="16px" class="hidden-xl">
                      <div class="process" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px"
                          (click)="openProcessDeposit()">
                          <img class="process__icon" src="./assets/img/dxres/process.svg" alt="" width="20px"
                              height="20px">
                          <div class="process__text">
                              Quy trình đặt cọc & thu tiền
                          </div>
                      </div>
                  </div>

              </div>
              <div class="d-block">
                  <div [class.hide]="!(isTableLoading$ | async)" fxLayout="column" fxFlex="100"
                      fxLayoutAlign="center center" class="mh-100vh">
                      <mat-spinner color="primary" class="mat-spinner"></mat-spinner>
                      <h4>loading...</h4>
                  </div>
                  <div *ngIf="isTableLoading$">
                      <ag-grid-angular [ngClass]="isCollapse ? 'collapse-grid' : ''" [getRowNodeId]="getRowNodeId"
                          [components]="components" id="grid-ycdco" class="ag-theme-balham grid-respon"
                          [rowData]="rowData" [gridOptions]="gridOptionsYCDCO" [animateRows]="true"
                          (dragStopped)="onColumnResized($event)"
                          (firstDataRendered)="onFirstDataRenderedYCDCO($event)">
                      </ag-grid-angular>
                  </div>
                  <app-paging [data]="pagination" (dataChange)="onChangePaging($event)" *ngIf="isTableLoading$">
                  </app-paging>
                  <ng-template #contractCodeRow let-row>
                      <div class="app-table-cell">
                          <a *ngIf="canGoContract; else contractLabel" href="javascript:;" (click)="openHDC(row.contract)" class="color-a">{{row.contractCode}}</a>
                          <ng-template #contractLabel>
                              <span >{{row.contractCode}}</span>
                          </ng-template>
                      </div>
                  </ng-template>
                  <ng-template #ticketPropertyUnitCode let-row>
                      <div class="app-table-cell">
                          <span>{{row?.propertyUnit?.code}}</span>
                      </div>
                  </ng-template>

                  <ng-template #recieptCodeYCDC let-row>
                      <span [innerHTML]="row?.recieptCode"></span>
                  </ng-template>
                  <ng-template #recieptStatusYCDC let-row>
                      <span [innerHTML]="row?.recieptStatus"></span>
                  </ng-template>
                  <ng-template #recieptAmount let-row>
                    <span [innerHTML]="row?.recieptAmount"></span>
                  </ng-template>

                  <ng-template #erpAmount let-row>
                    <div class="app-table-cell">
                        <span >{{row?.erp?.totalAmount | money}}</span> <br>
                        <span >{{row?.erp?.createDate | date: 'dd-MM-yyyy hh:mm:ss'}}</span>
                    </div>
                </ng-template>

              </div>
          </mat-tab>

          <mat-tab label="Danh sách giao dịch">
              <div [class.hide]="!(isTableLoading$ | async)" fxLayout="column" fxFlex="100"
                  fxLayoutAlign="center center" class="mh-500">
                  <mat-spinner color="primary" class="mat-spinner"></mat-spinner>
                  <h4>loading...</h4>
              </div>
              <div class="event-sale-dsgd">
                  <div class="action-table" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="32px">
                      <app-checkbox-button *ngIf="canAutoSortDSGD" [(ngModel)]="autoFreshUI">
                          Sắp xếp theo màu
                      </app-checkbox-button>
                      <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="8px"
                          class="action-table__refresh" (click)="refresh()">
                          <img src="assets/img/dxres/icon/icon-refresh.svg" alt="">
                          <span>Làm mới</span>
                      </div>
                  </div>
                  <div class="app-table-container border-none">
                      <mat-accordion class="example-headers-align" style="width: 100%;" [multi]="true">
                          <mat-expansion-panel [expanded]="grid1Expanded" (afterCollapse)="onAfterCollapse(1)"
                              (afterExpand)="onAfterExpand(1)">
                              <mat-expansion-panel-header [collapsedHeight]="'30px'" [expandedHeight]="'28px'"
                                  style="background: #E4EBF7; border-top: 1px solid #cccccc;">
                                  <mat-panel-title>
                                      <div class="count-table h-28" fxLayout="row"
                                          fxLayoutAlign="space-between center">
                                          <div class="count-table__inner">
                                              <h4> Danh sách sản phẩm ráp đích danh: &nbsp;</h4>
                                              <span (click)="onFilterUnitDSGD($event)"
                                                  [ngClass]="{'underline': !statusFilterUnitDSGD}" class="status"
                                                  matRipple matTooltip="Tất cả"> <span
                                                      class="">{{(saleList | searchPos:'status':status:true).length}}</span></span>
                                              <span (click)="onFilterUnitDSGD($event, 'CLOSE')"
                                                  [ngClass]="{'underline': statusFilterUnitDSGD === 'CLOSE'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('CLOSE')"> <span
                                                      class="close"> {{countSaleListStatus.close}} </span></span>
                                              <span (click)="onFilterUnitDSGD($event, 'COMING')"
                                                  [ngClass]="{'underline': statusFilterUnitDSGD === 'COMING'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('COMING')"> <span
                                                      class="coming"> {{countSaleListStatus.coming}} </span></span>
                                              <!-- <span (click)="onFilterUnitDSGD($event, 'PROCESSING')" [ngClass]="{'underline': statusFilterUnitDSGD === 'PROCESSING'}" class="status" matRipple matTooltip="Đã đăng ký"> <span class="processing"> {{countSaleListStatus.processing}} </span></span> -->
                                              <span (click)="onFilterUnitDSGD($event, 'CONFIRM')"
                                                  [ngClass]="{'underline': statusFilterUnitDSGD === 'CONFIRM'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('CONFIRM')"> <span
                                                      class="confirm"> {{countSaleListStatus.confirm}}
                                                  </span></span>
                                              <span (click)="onFilterUnitDSGD($event, 'LOCK_CONFIRM')"
                                                  [ngClass]="{'underline': statusFilterUnitDSGD === 'LOCK_CONFIRM'}" class="status" matRipple
                                                  [matTooltip]="getTooltipUnitStatus('LOCK_CONFIRM')">
                                                  <span class="lock-confirm"> {{countSaleListStatus.lockConfirm}}
                                                  </span></span>
                                              <span (click)="onFilterUnitDSGD($event, 'SUCCESS')"
                                                  [ngClass]="{'underline': statusFilterUnitDSGD === 'SUCCESS'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('SUCCESS')"> <span
                                                      class="success"> {{countSaleListStatus.success}}
                                                  </span></span>
                                              <!-- <span (click)="onFilterUnitDSGD($event, 'UNSUCCESS')" [ngClass]="{'underline': statusFilterUnitDSGD === 'UNSUCCESS'}" class="status" matRipple matTooltip="Giao dịch không thành công"> <span class="unsuccess"> {{countSaleListStatus.unsuccess}} </span></span> -->
                                          </div>
                                      </div>
                                  </mat-panel-title>
                              </mat-expansion-panel-header>
                              <div [hidden]="isTableLoading$ | async" fxFlex="100">
                                  <app-event-sale-table-deal-panel [status]="status"
                                      [hasPopupAdminConfirm]="currentSalesProgram.hasPopupAdminConfirm"
                                      [customerConfirmRequired]="currentSalesProgram.customerConfirmRequired"
                                      [fieldSort]="currentSort.propertyName" [reverse]="currentSort.sortOrder"
                                      [items]="saleListFilter" [canChangeStatusSaleList]="canChangeStatusSaleList"
                                      [priority]="priority" [isDVKH]="user.isRoleDVKH()"
                                      [canRevokeUnit]="canRevokeUnit" [canMovePosUnit]="canMovePosUnit"
                                      [canLiquidateUnit]="canLiquidateUnit"
                                      [canLiquidateUnitAny]="canLiquidateUnitAny"
                                      (popup)="onPopupDSGD($event)" [project]="project"
                                      [projectStage]="projectStage"
                                      [projectStatus]="projectStatus"
                                      [projectStageStatus]="projectStageStatus"
                                      [isSharedTable]="false"
                                      [templateFileHasStatus]="selectedProject?.setting?.templateFileHasStatus"
                                      [changedRowIds]="changedRowIds1" [startPriority]="startPriority"
                                      (download)="handleDownload($event)" [autoFreshUI]="autoFreshUI"
                                      [gridHeight]="gridHeight" [isRevokeAny]="isRevokeAny"
                                      [statusFilter]="statusFilterUnitDSGD" [isF1San]="isF1San()" #eventSaleTable1
                                      [saleUnitLockConfirmable]="saleUnitLockConfirmable"
                                      [combineSaleUnitConfirmable]="currentSalesProgram.combineSaleUnitConfirmable"
                                      [salesProgram]="currentSalesProgram"
                                      [eventProjectId]="eventProjectId" [isEditCustomerInfo]="isEditCustomerInfo">
                                  </app-event-sale-table-deal-panel>
                              </div>
                              <div [class.hide]="!(isTableLoading$ | async)" fxLayout="column" fxFlex="100"
                                  fxLayoutAlign="center center" class="mh-500">
                                  <mat-spinner color="primary" class="mat-spinner"></mat-spinner>
                                  <h4>loading...</h4>
                              </div>
                          </mat-expansion-panel>
                          <mat-expansion-panel [expanded]="grid2Expanded" (afterCollapse)="onAfterCollapse(2)"
                              (afterExpand)="onAfterExpand(2)">
                              <mat-expansion-panel-header [collapsedHeight]="'30px'" [expandedHeight]="'28px'"
                                  style="background: #E4EBF7; border-top: 1px solid #cccccc;">
                                  <mat-panel-title>
                                      <div class="count-table h-28" fxLayout="row"
                                          fxLayoutAlign="space-between center">
                                          <div class="count-table__inner">
                                              <h4>Danh sách sản phẩm tự do: &nbsp;</h4>
                                              <span (click)="onFilterFreeDSGD($event)"
                                                  [ngClass]="{'underline': !statusFilterFreeDSGD}" class="status"
                                                  matRipple matTooltip="Tất cả"> <span
                                                      class="">{{(saleList2 | searchPos:'status':status:true).length}}</span></span>
                                              <span (click)="onFilterFreeDSGD($event, 'CLOSE')"
                                                  [ngClass]="{'underline': statusFilterFreeDSGD === 'CLOSE'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('CLOSE')"> <span
                                                      class="close"> {{countSaleListStatus2.close}} </span></span>
                                              <span (click)="onFilterFreeDSGD($event, 'COMING')"
                                                  [ngClass]="{'underline': statusFilterFreeDSGD === 'COMING'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('COMING')"> <span
                                                      class="coming"> {{countSaleListStatus2.coming}} </span></span>
                                              <span (click)="onFilterFreeDSGD($event, 'PROCESSING')"
                                                  [ngClass]="{'underline': statusFilterFreeDSGD === 'PROCESSING'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('PROCESSING')"> <span
                                                      class="processing"> {{countSaleListStatus2.processing}}
                                                  </span></span>
                                              <span (click)="onFilterFreeDSGD($event, 'CONFIRM')"
                                                  [ngClass]="{'underline': statusFilterFreeDSGD === 'CONFIRM'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('CONFIRM')"> <span
                                                      class="confirm"> {{countSaleListStatus2.confirm}}
                                                  </span></span>
                                              <span (click)="onFilterFreeDSGD($event, 'LOCK_CONFIRM')"
                                                [ngClass]="{'underline': statusFilterFreeDSGD === 'LOCK_CONFIRM'}"
                                                class="status" matRipple [matTooltip]="getTooltipUnitStatus('LOCK_CONFIRM')">
                                                <span class="lock-confirm"> {{countSaleListStatus2.lockConfirm}} </span></span>
                                              <span (click)="onFilterFreeDSGD($event, 'SUCCESS')"
                                                  [ngClass]="{'underline': statusFilterFreeDSGD === 'SUCCESS'}"
                                                  class="status" matRipple [matTooltip]="getTooltipUnitStatus('SUCCESS')"> <span
                                                      class="success"> {{countSaleListStatus2.success}}
                                                  </span></span>
                      <!-- <span (click)="onFilterFreeDSGD($event, 'UNSUCCESS')" [ngClass]="{'underline': statusFilterFreeDSGD === 'UNSUCCESS'}" class="status" matRipple matTooltip="Giao dịch không thành công"> <span class="unsuccess"> {{countSaleListStatus2.unsuccess}} </span></span> -->
                                          </div>
                                      </div>
                                  </mat-panel-title>
                              </mat-expansion-panel-header>
                              <div [hidden]="isTableLoading$ | async" fxFlex="100">
                                  <app-event-sale-table-deal-panel [status]="status"
                                      [hasPopupAdminConfirm]="currentSalesProgram.hasPopupAdminConfirm"
                                      [customerConfirmRequired]="currentSalesProgram.customerConfirmRequired"
                                      [fieldSort]="currentSort.propertyName" [reverse]="currentSort.sortOrder"
                                      [items]="saleList2Filter" [canChangeStatusSaleList]="canChangeStatusSaleList"
                                      [priority]="priority" [isDVKH]="user.isRoleDVKH()"
                                      [canRevokeUnit]="canRevokeUnit" [canMovePosUnit]="canMovePosUnit"
                                      [canLiquidateUnit]="canLiquidateUnit"
                                      [canLiquidateUnitAny]="canLiquidateUnitAny"
                                      (popup)="onPopupDSGD($event)" [project]="project"
                                      [projectStage]="projectStage"
                                      [projectStatus]="projectStatus"
                                      [projectStageStatus]="projectStageStatus"
                                      [isSharedTable]="true" [startPriority]="startPriority"
                                      [templateFileHasStatus]="selectedProject?.setting?.templateFileHasStatus"
                                      [changedRowIds]="changedRowIds2" (download)="handleDownload($event)"
                                      [autoFreshUI]="autoFreshUI" [gridHeight]="gridHeight"
                                      [isRevokeAny]="isRevokeAny" [isImportPrimary]="canImportPrimary"
                                      [statusFilter]="statusFilterFreeDSGD" [isF1San]="isF1San()" #eventSaleTable2
                                      [saleUnitLockConfirmable]="saleUnitLockConfirmable"
                                      [combineSaleUnitConfirmable]="currentSalesProgram.combineSaleUnitConfirmable"
                                      [salesProgram]="currentSalesProgram"
                                      [eventProjectId]="eventProjectId" [isEditCustomerInfo]="isEditCustomerInfo">
                                  </app-event-sale-table-deal-panel>
                              </div>
                              <div [class.hide]="!(isTableLoading$ | async)" fxLayout="column" fxFlex="100"
                                  fxLayoutAlign="center center" class="mh-500">
                                  <mat-spinner color="primary" class="mat-spinner"></mat-spinner>
                                  <h4>loading...</h4>
                              </div>
                          </mat-expansion-panel>
                      </mat-accordion>
                  </div>
              </div>
          </mat-tab>

          <mat-tab label="Hướng dẫn quy trình">
              <app-edit-process-instructions [isSyncERP]="isSyncERP"></app-edit-process-instructions>
          </mat-tab>

          <mat-tab label="Rút thăm sản phẩm" *ngIf="isCreateDrawn || isRollDrawn">
              <app-unit-drawn
                    [isRefresh]="isRefresh"
                    [isCreateDrawn]="isCreateDrawn"
                    [isRollDrawn]="isRollDrawn"
                    [isUnitDrawn]="isUnitDrawn"
                    [project]="project"
                    [blockFloorRoom]="blockFloorRoom"
                    [currentSalesProgram]="currentSalesProgram"
              ></app-unit-drawn>
          </mat-tab>
          <div *userAuthority="['FINA', 'REGISTER', 'SHOW']">
            <mat-tab label="Đăng ký vay" *ngIf="selectedProject?.isRegisterFina">
                <app-financial-support [projectId]="projectId" [finaUrl]="selectedProject?.finaUrl" [blockFloorRoom]="blockFloorRoom"></app-financial-support>
            </mat-tab>
          </div>

          <div *userAuthority="['ADMIN', 'PROJECT', 'DASHBOARD']">
            <mat-tab label="Dashboard" *ngIf="selectedProject?.dashboardUrl">
                <app-dashboard-project [projectId]="projectId" [dashboardUrl]="selectedProject?.dashboardUrl"></app-dashboard-project>
            </mat-tab>
          </div>

      </mat-tab-group>

  </div>
</div>


<app-event-ticket-detail-setting *ngIf="project"></app-event-ticket-detail-setting>
<app-event-sale-setting [data]="settingData.id" [canEditSetting]="canEditSetting"></app-event-sale-setting>
<app-event-sale-property-ticket *ngIf="project"
  [ticketType]="selectedTab == 2 ? 'YCDCH' :  selectedTab == 3 ? 'YCDC': 'YCTV'" [projectStage]="projectStage"
  [project]="project" (created)="onTicketCreated()">
</app-event-sale-property-ticket>



<ng-template #elseDownloadTemplate>
  <span style="width: 80px; display: flex; justify-content: center;">
      <mat-spinner diameter="30" color="primary"></mat-spinner>
  </span>
</ng-template>
