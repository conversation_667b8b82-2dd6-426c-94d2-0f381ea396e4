<div style="position: relative; height: 100%;">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p class="loading-text"><PERSON>ang tải dữ liệu...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && authError" class="error-container">
    <mat-icon class="error-icon">error_outline</mat-icon>
    <h3 class="error-title">Không thể truy cập</h3>
    <p class="error-message">{{ authError }}</p>
    <button mat-raised-button color="primary" (click)="refreshPermissions()">
      Thử lại
    </button>
  </div>

  <!-- Main Content - Only show when authenticated and has permission -->
  <div *ngIf="!isLoading && !authError && isAuthenticated && canView">
    <mat-sidenav-container class="mat-sidenav-container" autosize>
      <mat-sidenav #sidenav mode="over" [(opened)]="opened" style="width: 270px;" fixedInViewport >
        <app-e-menu [projectId]="projectId" [urlEsalekit]="urlEsalekit"></app-e-menu>
        <button class="button-close" mat-button (click)="toogleSidenav()">
          <div class="icon-close">
            <i class="icon-left"></i>
            <i class="icon-right"></i>
          </div>
        </button>
      </mat-sidenav>

      <mat-sidenav-content [ngClass]="{'opened' : !opened}">
        <app-e-short-menu [projectId]="projectId" [shortMenu]="true" (toogleSidenav)="toogleSidenav()"></app-e-short-menu>
        <div [ngStyle]="{'margin-left': '50px'}">
          <router-outlet></router-outlet>
        </div>
      </mat-sidenav-content>
    </mat-sidenav-container>

    <div class="eq-link">
      <app-e-quick-link [projectId]="projectId" ></app-e-quick-link>
    </div>
  </div>

  <!-- Debug Info (only in development) -->
  <div *ngIf="!isLoading" class="debug-info" style="position: fixed; bottom: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px;">
    <div>Authenticated: {{ isAuthenticated }}</div>
    <div>Can View: {{ canView }}</div>
    <div>Can Edit: {{ canEdit }}</div>
    <div>User: {{ getCurrentUser()?.username || 'N/A' }}</div>
  </div>
</div>
