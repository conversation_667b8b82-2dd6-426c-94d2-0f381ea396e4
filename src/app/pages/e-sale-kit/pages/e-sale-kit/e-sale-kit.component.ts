import {ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {MatDrawer} from '@angular/material';
import { SidenavService } from '../../services/sidenav.service';
import { Unsubscriber } from 'app/shared/models/unsubscriber';
import { ESaleKitService } from '../../services/e-sale-kit.service';
import { ESaleKitAuthService } from '../../services/e-sale-kit-auth.service';
// import { switchMap, filter } from 'rxjs/operators'; // Không cần thiết nữa

@Component({
  selector: 'app-e-sale-kit',
  templateUrl: './e-sale-kit.component.html',
  styleUrls: ['./e-sale-kit.component.scss'],
  providers: [SidenavService, Unsubscriber],
})
export class ESaleKitComponent implements OnInit, OnD<PERSON>roy {
  opened: boolean = false;
  projectId: string = '';
  isAuthenticated: boolean = false;
  canEdit: boolean = false;
  canView: boolean = false;
  urlEsalekit: string;
  isLoading: boolean = true;
  authError: string = '';

  // @ViewChild('sidenav', {static: true}) sidenav: MatDrawer;
  @ViewChild('sidenav') sidenav: MatDrawer;

  constructor(
    private activeRouter: ActivatedRoute,
    private router: Router,
    private sidenavService: SidenavService,
    private unsubscriber: Unsubscriber,
    private _changeDetectorRef: ChangeDetectorRef,
    private eSaleKitService: ESaleKitService,
    private eSaleKitAuthService: ESaleKitAuthService
  ) {
  }

  ngOnInit() {
    this.unsubscriber.safeSubscriptions(
      this.sidenavService.opened$.subscribe(val => {
        this.opened = val;
        this._changeDetectorRef.detectChanges();
      }),
    );

    const params = this.activeRouter.snapshot.params;
    this.urlEsalekit = params['id'];

    // Đơn giản hóa: chỉ cần load project data, authentication đã được xử lý tự động
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.unsubscriber.unsubscribeAll();
  }

  toogleSidenav() {
    this.sidenav.toggle();
  }

  /**
   * Khởi tạo component đơn giản - authentication đã được xử lý tự động
   */
  private initializeComponent(): void {
    this.isLoading = true;
    this.authError = '';

    // Load project data trực tiếp
    this.unsubscriber.safeSubscriptions(
      this.eSaleKitService.getProjectDetail(this.urlEsalekit).subscribe({
        next: (projectData) => {
          this.projectId = projectData.id;
          this.isAuthenticated = true;
          this.loadPermissions();
          this.isLoading = false;
          console.log('Project loaded successfully:', projectData.id);
        },
        error: (error) => {
          console.error('Error loading project:', error);
          this.authError = 'Không thể tải dữ liệu dự án. Vui lòng kiểm tra lại URL hoặc quyền truy cập.';
          this.isLoading = false;
          this.isAuthenticated = false;
        }
      })
    );

    // Lắng nghe thay đổi permissions
    this.unsubscriber.safeSubscriptions(
      this.eSaleKitAuthService.permissions$.subscribe(permissions => {
        // Permissions được cập nhật tự động
        console.log('Permissions updated:', permissions);
      })
    );
  }

  /**
   * Load permissions từ auth service
   */
  private loadPermissions(): void {
    this.unsubscriber.safeSubscriptions(
      this.eSaleKitAuthService.permissions$.subscribe(permissions => {
        this.canEdit = permissions.canEdit;
        this.canView = permissions.canView;
        this._changeDetectorRef.detectChanges();
      })
    );
  }

  /**
   * Redirect to login page
   */
  private redirectToLogin(): void {
    // Lưu URL hiện tại để redirect sau khi login
    sessionStorage.setItem('redirectUrl', this.router.url);
    this.router.navigate(['/login']);
  }

  /**
   * Kiểm tra quyền chỉnh sửa module cụ thể
   */
  public canEditModule(module: string): boolean {
    return this.eSaleKitAuthService.canEditModule(module);
  }

  /**
   * Lấy thông tin user hiện tại
   */
  public getCurrentUser() {
    return this.eSaleKitAuthService.getCurrentUser();
  }

  /**
   * Refresh permissions
   */
  public refreshPermissions(): void {
    this.eSaleKitAuthService.refreshPermissions().subscribe({
      next: () => {
        console.log('Permissions refreshed successfully');
      },
      error: (error) => {
        console.error('Error refreshing permissions:', error);
      }
    });
  }

  getProject(esalekit: string) {
    this.eSaleKitService.getProjectDetail(esalekit).subscribe(res => {
      this.projectId = res.id;
    });
  }
}
