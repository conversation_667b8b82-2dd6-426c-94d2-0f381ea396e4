import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, ParamMap } from "@angular/router";
import { PrimaryTransactionService } from "../../project/primary-transaction.service";
import { OnlinePaymentService } from "./online.payment.service";
import { PaymentService } from "../payment.service";
import { Constant } from "app/shared/constant/constant";
import { TranslateService } from "./translate.service";
import { WsNotifyService } from "app/shared/components/ws-notify.service";
import { EventChangeService } from "app/shared/services/event.change.service";
import { MessageDialogComponent } from "./message-dialog/message-dialog.component";
import { MatDialog } from "@angular/material";
import { ToastrService } from "ngx-toastr";

@Component({
    selector: "app-online-payment",
    templateUrl: "./online.payment.component.html",
    styleUrls: ["./online.payment.component.scss"],
    
})
export class OnlinePaymentComponent implements OnInit {
    detailTransaction: any = {};
    paymentInfo: any = {};
    paymentMethods: any[] = [];

    paymentMethod: any[] = [];

    paymentData = {
        id: "",
        paymentMethod: "",
        language: "",
        amount: 0,
    };

    imgBase64: any;
    paymentId = "";

    numberMask = Constant.numberMask;
    selectedMethodIdx = -1;

    paymentStatus: string;
    isLoading: boolean = true;
    isLoadingBanking: boolean = false;
    language: string = 'vn';

    constructor(
        private translate: TranslateService,
        public route: ActivatedRoute,
        public primaryTransactionService: PrimaryTransactionService,
        public onlinePaymentService: OnlinePaymentService,
        public paymentService: PaymentService,
        private wsNotifyService: WsNotifyService,
        private eventChangeService: EventChangeService,
        private toastr: ToastrService,
        public dialog: MatDialog
    ) {}

    ngOnInit() {
        this.route.queryParams.subscribe((params) => {
            if (params["language"]) {
                this.language = params["language"];
            }
            this.paymentStatus = params["status"];

        });
        this.route.paramMap.subscribe((params: ParamMap) => {
            this.paymentId = params.get("paymentId");
            this.paymentData.id = this.paymentId;
            this.subscribeWS(this.paymentId);

            this.getPayment(this.paymentId);
        });
    }

    getPayment(paymentId) {
        
        this.paymentService.getPayment(paymentId).subscribe((res) => {
            this.paymentInfo = res;
            this.paymentInfo.acceptedTypes =
                this.paymentInfo.acceptedTypes &&
                this.paymentInfo.acceptedTypes.length
                    ? this.paymentMethods.filter(
                          (e) =>
                              this.paymentInfo.acceptedTypes.findIndex(
                                  (a) => a === e.code
                              ) >= 0
                      )
                    : this.paymentMethods;

            this.paymentInfo.transferredAmount = (
                this.paymentInfo.histories || []
            ).reduce(
                (acc, curr) => (curr.status === "PAID" ? acc + curr.amount : 0),
                0
            );
            this.paymentInfo.remainingAmount =
                this.paymentInfo.amount - this.paymentInfo.transferredAmount;

            this.paymentData.amount = this.paymentInfo.remainingAmount;

            if (this.paymentStatus) {
                let his =
                    this.paymentInfo &&
                    this.paymentInfo.histories &&
                    this.paymentInfo.histories.length
                        ? this.paymentInfo.histories[
                              this.paymentInfo.histories.length - 1
                          ]
                        : {};
                this.dialog.open(MessageDialogComponent, {
                    width: "400px",
                    data: {
                        paymentInfo: {
                            ...this.paymentInfo,
                            amount: his ? his.amount : 0,
                        },
                        isSuccess:
                            this.paymentStatus !== "CANCELED" &&
                            his.status === "PAID",
                    },
                    disableClose: true,
                    panelClass: "app-dialog",
                });
            }
            this.setLang(this.language);
            this.onlinePaymentService.getPaymentMethod(res.accountConfig ? {targetAccount: res.accountConfig.targetAccount} : null).subscribe((res) => {
                this.paymentMethods = res.data;

                this.isLoading = false;
            });
        });
    }

    paymentMethodTypeChange(value, idx) {
        this.paymentMethod = value;
        this.paymentData.paymentMethod = "";
        this.selectedMethodIdx = idx;
        this.imgBase64 = null;
        setTimeout(() => {
            document.getElementById('endPage').scrollIntoView();
        }, 100);
    }

    selectBank(code) {
        this.paymentData.paymentMethod = code;
        this.isLoadingBanking = true;

        if (this.paymentData.paymentMethod && this.validAmount()) {
            this.processPayment();
        }
    }

    setLang(lang: string) {
        let externalLangData = {
            companyName: '',
            companyAddress: '',
        };
        if (this.paymentInfo.accountConfig) {
            externalLangData.companyName =  lang === 'en' && this.paymentInfo.accountConfig.companyNameEn ? this.paymentInfo.accountConfig.companyNameEn : this.paymentInfo.accountConfig.companyName;
            externalLangData.companyAddress =  lang === 'en' && this.paymentInfo.accountConfig.companyAddressEn ? this.paymentInfo.accountConfig.companyAddressEn : this.paymentInfo.accountConfig.companyAddress;
        }
        this.translate.use(lang, externalLangData);
        this.paymentData.language = lang;
    }

    processPayment() {
        this.onlinePaymentService
            .createPayment({
                ...this.paymentData,
                amount: parseInt(
                    this.paymentData.amount.toString().replace(/,/g, "")
                ),
            })
            .subscribe((res) => {
                this.isLoadingBanking = false;
                if (res.data && res.data.type) {
                    switch (res.data.type) {
                        case "link":
                            window.location.href = res.data.content;
                            break;

                        case "image":
                            this.imgBase64 = `data:image/png;base64,${res.data.content}`;

                            let inter = setInterval(() => {
                                if (
                                    this.paymentInfo &&
                                    this.paymentInfo.reciept.status ===
                                        "WAITING_TRANSFER"
                                ) {
                                    this.getPayment(this.paymentId);
                                } else {
                                    clearInterval(inter);
                                }
                            }, 30000);
                            break;

                        default:
                            break;
                    }
                } else if (res.message) {
                    this.imgBase64 = null;
                    this.toastr.error(res.message);
                }
            });
    }

    validAmount() {
        const amount = this.paymentData.amount
            ? parseInt(this.paymentData.amount.toString().replace(/,/g, ""))
            : 0;
        if (
            !this.paymentData.amount ||
            amount > this.paymentInfo.remainingAmount ||
            amount <= 0
        ) {
            return false;
        }

        return true;
    }

    get methodList() {
        return this.paymentInfo &&
            this.paymentInfo.acceptedTypes &&
            this.paymentInfo.acceptedTypes.length
            ? this.paymentInfo.acceptedTypes
            : this.paymentMethods;
    }

    getPaymentMethodImage(code) {
        return ["CC", "ATM", "IB", "QR"].includes(code) ? code : "wallet";
    }

    private subscribeWS(id: string) {
        const infoMessagingPattern = [
            `${WsNotifyService.messagingPattern}msx-adsg.payment.${id}`,
        ];
        this.wsNotifyService.subscribe(infoMessagingPattern);
        this.eventChangeService.emitChangeSource.subscribe((data) => {
            if (data.boardcast === 'payment') {
                this.paymentStatus = "PAID";
                this.getPayment(this.paymentId);
            }
        });
    }
}
