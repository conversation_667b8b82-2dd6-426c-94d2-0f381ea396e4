# 🧪 Test Guide: New API Integration

## 📋 **Implementation Summary**

Đ<PERSON> cập nhật `UserService.userProfile()` để:

1. ✅ **Gọi API account mới** với bearer token từ cookie
2. ✅ **Convert API response** từ format mới sang format cũ
3. ✅ **Sử dụng SecurityService.setAuthor()** hiện tại
4. ✅ **Fallback** về API cũ nếu API mới fail

## 🔄 **Luồng hoạt động:**

```
Login Success → UserService.userProfile() → loadAccountFromNewApi()
    ↓
Get token from cookie → Call new API with Bearer token
    ↓
Convert response → SecurityService.setAuthor() → Save to sessionStorage
    ↓
AuthorizeService.setAuthorities() → Ready to use
```

## 🔧 **Cách test:**

### **1. Test trong browser console:**

```javascript
// 1. Check token có trong cookie không
document.cookie

// 2. Check sessionStorage có permissions không
sessionStorage.getItem('permissions')

// 3. Test manual API call
const token = 'your-access-token-here';
fetch('https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('API Response:', data);
  console.log('Total roles:', data.roles?.length);
});
```

### **2. Test integration:**

```javascript
// Trong component hoặc service
this.userService.userProfile(true).subscribe(
  (result) => {
    console.log('UserProfile result:', result);
    
    // Check permissions đã được set chưa
    const permissions = JSON.parse(sessionStorage.getItem('permissions'));
    console.log('Permissions in sessionStorage:', permissions);
    
    // Check AuthorizeService
    console.log('AuthorizeService has authorities:', this.authorizeService.checkAuthorities());
  },
  (error) => {
    console.error('UserProfile error:', error);
  }
);
```

## 📊 **Expected Results:**

### **Input (API Response):**
```json
{
  "roles": [
    {
      "functionRoles": [
        {
          "features": [
            {
              "featureId": "uuid1",
              "featureName": "property.unit.import",
              "msxId": "uuid2",
              "msxName": "property-unit"
            },
            {
              "featureId": "uuid3", 
              "featureName": "esalekit.edit",
              "msxId": "uuid4",
              "msxName": "esalekit"
            }
          ]
        }
      ]
    }
  ]
}
```

### **Converted Format:**
```json
[
  {
    "featureName": "property.unit.import",
    "action": {
      "action:READ": true,
      "action:WRITE": true
    }
  },
  {
    "featureName": "esalekit.edit", 
    "action": {
      "action:READ": true,
      "action:WRITE": true
    }
  }
]
```

### **SessionStorage Result:**
```json
{
  "property": {
    "unit": {
      "import": { "isAuthor": true }
    }
  },
  "esalekit": {
    "edit": { "isAuthor": true }
  }
}
```

## 🐛 **Troubleshooting:**

### **Nếu API call fail:**
1. Check token trong cookie: `document.cookie`
2. Check CORS settings
3. Check API endpoint có đúng không
4. Check network tab trong DevTools

### **Nếu permissions không được set:**
1. Check console logs từ `convertNewApiResponseToOldFormat()`
2. Check `SecurityService.setAuthor()` có được gọi không
3. Check sessionStorage có key `"permissions"` không

### **Nếu AuthorizeService không hoạt động:**
1. Check `authorizeService.checkAuthorities()` return true/false
2. Check `authorizeService.getAuthority(['feature', 'name'])` 
3. Check permissions structure trong sessionStorage

## 🔍 **Debug Commands:**

```javascript
// Check current state
console.log('Token:', securityService.getCookieToken());
console.log('Permissions:', sessionStorage.getItem('permissions'));
console.log('Has authorities:', authorizeService.checkAuthorities());

// Test specific permission
authorizeService.getAuthority(['property', 'unit', 'import']);
authorizeService.getAuthority(['esalekit', 'edit']);

// Force reload
userService.userProfile(true).subscribe();
```

## 📝 **Notes:**

- **Fallback**: Nếu API mới fail, sẽ fallback về API cũ
- **Backward compatible**: Không ảnh hưởng đến code hiện tại
- **Error handling**: Log errors nhưng không break app
- **Token source**: Lấy từ cookie thông qua `SecurityService.getCookieToken()`
- **Permission format**: Tương thích 100% với logic hiện tại
