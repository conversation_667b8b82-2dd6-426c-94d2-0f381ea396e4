import {
    <PERSON>mpo<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Injector,
    Inject,
    ViewChild,
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { DOCUMENT, Location } from "@angular/common";
import { MatDialog } from "@angular/material";
import { Subject, BehaviorSubject } from "rxjs";
import {
    ConfigurationService,
    SecurityService,
    StorageService,
} from "app/shared/services";
import {
    CommonService,
    ToastrService,
    StompService,
} from "app/shared/services/common";
import _ = require("lodash");
import * as moment from "moment";

import { AppSettings } from "app/app.settings";
import { Constant } from "app/shared/constant/constant";
import { MASTER_DATA } from "app/pages/master-data/master-data.data";
import { VoiceCallService } from "app/shared/services/voicecall.service";
import { CUser } from "app/api-models";
import { UserV2Service } from "app/shared/services/common/user-v2.service";
import { FormInputAddressComponent } from "app/shared/components/app-form-input";
import { ReceiptService } from "app/pages/receipt";
import { Receipt } from "app/pages/receipt/receipt.model";
import { BaseComponent } from "app/shared/components/base.component";
import { finalize, takeUntil } from "rxjs/operators";
import { MoneyToNumber } from "app/shared/parse/money-to-number";
import { MoneyOrderConstant } from "app/shared/constant/money-order.constant";
import { MoneyOrderModel } from "app/shared/models/money-order.model";
import { markAsTouched } from "app/shared/utility/form-utility";
import { ProjectStatusEnum } from "app/shared/enum/project.enum";
import { ProjectService } from "app/pages/project/project.service";
import { ContractService } from "app/pages/contract";
import { isNullOrUndefined } from "util";
import { MOStatusFull } from "app/shared/constant/money-order";
import { MoneyOrderService } from "../money-order.service";
import { ConfirmPopup } from "app/shared/components/confirm-popup/confirm-popup";
import { TransactionStatusEnum } from "app/shared/enum/transaction.enum";
import { ReasonDialog } from "app/pages/ticket/popup-dialog";
import { ContractConstants } from "../../../shared/constant/contract.constant";
import { ContractEnum } from "app/shared/enum/contract.enum";
import { PrimaryTransactionStatus } from "app/shared/enum/primary-transaction.enum";
import { PrimaryTransactionService } from "../../project/primary-transaction.service";
import { LiquidationService } from "app/pages/liquidation/liquidation.service";
@Component({
    selector: "app-money-order-payment",
    templateUrl: "./money-order-payment.component.html",
    styleUrls: ["./money-order-payment.component.scss"],
})
export class MoneyOrderPaymentComponent
    extends BaseComponent
    implements OnInit, OnDestroy
{
    @ViewChild("formAddress") formAddress: FormInputAddressComponent;

    private unsubscribe$: Subject<any> = new Subject();
    isLoading$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    // Constant
    public Constant = Constant;
    public MoneyOrderConstant = MoneyOrderConstant;
    public listTransferType = MoneyOrderConstant.TRANSFER_TYPE;
    public listTransactionType = MoneyOrderConstant.TRANSACTION_TYPE;
    public transfer = MoneyOrderConstant.TRANSFER_TYPE_TRANSFER;
    public cash = MoneyOrderConstant.TRANSFER_TYPE_CASH;
    public uploadType = Constant.UPLOAD_FILE_TYPE.RECEIPT;
    public MASTER_DATA = MASTER_DATA;
    projectStatusFilter =
        ProjectStatusEnum.COMING_SALE + ";" + ProjectStatusEnum.TRADING;
    public headerTitle: string = "Tạo phiếu thu";
    public statusName: string = "Tạo mới";
    public isHasMin: boolean = false;
    public isHasMax: boolean = false;
    public isCreate: boolean = false;
    public approvePermission: boolean = false;
    public rejectPermission: boolean = false;
    public submitPermission: boolean = false;
    public ContractConstants = ContractConstants;
    public PrimaryTransactionStatus = PrimaryTransactionStatus;

    public permission: boolean = false;
    // Variable
    public mainForm: FormGroup;
    public address: any = {};

    id: string = "";
    code: string = "";
    productCode: string = "";
    customerName: string = "";
    identityNumber: string = "";
    fullAddress: string = "";
    maxMoney: number = 0;
    ticketType: string = "";

    public dataModel: MoneyOrderModel = new MoneyOrderModel();
    public uploadedFiles: any[] = [];
    listProjects: any = [];
    listTickets: any[] = [];
    listContracts: any[] = [];
    listBank: any = [];

    isDisabled: boolean = false;
    isPrimaryContractMode: boolean = true;

    public user: CUser;

    constructor(
        injector: Injector,
        @Inject(DOCUMENT) private document,
        public storageService: StorageService,
        public appSettings: AppSettings,
        public service: ReceiptService,
        public projectService: ProjectService,
        public configurationService: ConfigurationService,
        public securityService: SecurityService,
        public commonService: CommonService,
        public stompService: StompService,
        public toastr: ToastrService,
        public voiceCall: VoiceCallService,
        public router: Router,
        public dialog: MatDialog,
        public formBuilder: FormBuilder,
        private location: Location,
        private userV2Service: UserV2Service,
        private contractService: ContractService,
        private moneyOrderService: MoneyOrderService,
        private primaryTransactionService: PrimaryTransactionService,
        private liquidationService: LiquidationService
    ) {
        super(injector.get(ReceiptService), Receipt, injector);
        this.settings = this.appSettings.settings;
        this.infoMessagingPattern = [
            this.messagingPattern + "msx-adsg.info.transaction",
        ];
        this.errorMessagingPattern =
            this.messagingPattern + "msx-adsg.error.transaction";
    }

    ngOnInit() {
        this.userV2Service.user$.subscribe((user) => {
            this.user = user;
        });
        const params = this.route.snapshot.params;
        const id = params["id"];
        if (id && id !== "create") {
            this.getPrimaryContractDetail(id);
            this.headerTitle = "Thông tin phiếu thu";
            this.id = id;
        } else {
            this.isCreate = true;
        }
        this.initForm();
        this.getProject();
    }

    initForm() {
        const currentDate = moment().format("YYYY-MM-DD");
        if (!this.mainForm) {
            this.mainForm = this.formBuilder.group({
                transactionType: [
                    {
                        value: "YCDCH",
                        disabled: this.isDisabled,
                    },
                    Validators.required,
                ],

                // Contract Info
                projectId: [
                    {
                        value: "",
                        disabled: this.isDisabled,
                    },
                    Validators.required,
                ],
                ticketId: [
                    { value: "", disabled: this.isDisabled },
                    Validators.required,
                ],
                contractId: [
                    { value: "", disabled: this.isDisabled },
                    Validators.required,
                ],
                totalMoney: [this.dataModel.totalMoney],

                bankName: [this.dataModel.bankName, Validators.required],
                bankNumber: [this.dataModel.bankNumber],
                contentBank: [this.dataModel.contentBank],

                customerName: [
                    {
                        value: this.dataModel.customer.name,
                        disabled: this.isDisabled,
                    },
                ],
                state: [
                    MoneyOrderConstant.TRANSFER_TYPE_TRANSFER,
                    Validators.required,
                ],

                // Thông tin chi phí
                money: [0, Validators.compose([Validators.required])],
                description: [this.dataModel.description, Validators.required],
                collectMoneyDate: [currentDate, Validators.required],
            });
        }
        this.mainForm.get("projectId").valueChanges.subscribe((projectId) => {
            this.mainForm.patchValue({ ticketId: "" });
            this.mainForm.patchValue({ contractId: "" });
            if (projectId) {
                if (this.isPrimaryContractMode) {
                    this.getListContract(projectId);
                } else {
                    this.getListTicket(projectId);
                }
                this.getBankList(projectId);
            }
        });
        this.mainForm.get("ticketId").valueChanges.subscribe((ticketId) => {
            if (ticketId) {
                let ticket = this.listTickets.find((e) => e.id === ticketId);
                if (ticket) this.onTicketChange(ticket);
            } else {
                this.productCode = "";
                this.customerName = "";
                this.identityNumber = "";
                this.fullAddress = "";
            }
        });
        this.mainForm.get("contractId").valueChanges.subscribe((contractId) => {
            if (contractId) {
                let contract = this.listContracts.find(
                    (e) => e.contract.id === contractId
                );
                if (contract) this.onContractChange(contract);
            } else {
                this.productCode = "";
                this.customerName = "";
                this.identityNumber = "";
                this.fullAddress = "";
            }
        });

        if (this.id) {
            this.mainForm.disable();
        }
    }
    getPrimaryContractDetail(id) {
        this.service.getTransactionById(id).subscribe((res) => {
            this.uploadedFiles = res && res.files ? res.files : [];
            this.dataModel = new MoneyOrderModel(res);
            this.code = this.dataModel.code;
            let primaryTransaction =
                (this.dataModel.primaryContract &&
                    this.dataModel.primaryContract.primaryTransaction) ||
                null;
            this.productCode =
                primaryTransaction &&
                primaryTransaction.propertyUnit &&
                primaryTransaction.propertyUnit.code;
            this.customerName =
                primaryTransaction &&
                primaryTransaction.customer &&
                primaryTransaction.customer.personalInfo &&
                primaryTransaction.customer.personalInfo.name;
            this.identityNumber =
                primaryTransaction &&
                primaryTransaction.customer &&
                primaryTransaction.customer.identities &&
                primaryTransaction.customer.identities[0].value;
            this.fullAddress =
                primaryTransaction &&
                primaryTransaction.customer &&
                primaryTransaction.customer.info &&
                primaryTransaction.customer.info.rootAddress &&
                primaryTransaction.customer.info.rootAddress.fullAddress;
            this.approvePermission =
                TransactionStatusEnum.waiting_transfer === res.status;
            this.rejectPermission =
                TransactionStatusEnum.transfered != res.status &&
                TransactionStatusEnum.processing != res.status;
            this.initForm();
        });
    }
    onBack() {
        this.location.back();
    }
    getProject(searchText: string = "") {
        this.projectService
            .getProjectListByName(searchText, this.projectStatusFilter)
            .subscribe((r) => {
                if (r && r.code === 1) {
                    this.listProjects = r.data || [];
                }
            });
    }

    onSearchTicket(event) {
        if (event && event.term && this.mainForm.get('projectId').value) {
            this.getListTicket(this.mainForm.get('projectId').value, event.term);
        }
    }

    onSearchContract(event) {
        if (event && event.term && this.mainForm.get('projectId').value) {
            this.getListContract(this.mainForm.get('projectId').value, event.term);
        }
    }

    getListTicket(projectId: string, keywords?: string) {
        let params = {
            ticketType: "YCDCH",
            status: PrimaryTransactionStatus.CS_APPROVED_CANCEL_REQUESTED,
            keywords,
            _fields:
                "surveys,customer2,_id,id,code,ticketType,demandCategory,bookingTicketCode,systemnoErp,reciept.code,reciept.status,reciept.id,customer.id,customer.info,customer.personalInfo,customer.identities,customer.bankInfo,customer.taxCode,customer.codeDx,customer.code,employee.name,pos.name,pos.id,employee.pos.name,createdDate,status,employee.id,escrowTicketCode,contract.id,contract.code,project.id,project.name,amount",
        };
        this.primaryTransactionService
            .getTicket(projectId, params)
            .subscribe((res) => {
                if (res && res.itemsSize) {
                    this.listTickets = res.items || [];
                    // this.getBankList(projectId);
                    // this.mainForm.patchValue({
                    //     ticketId: res.bookingTicketCode,
                    // });
                }
                // else{
                //     this.listPrimaryContracts = [];
                //     this.listBank = [];
                // }
            });
    }

    getListContract(projectId: string, keywords?: string) {
        this.listContracts = [];
        this.liquidationService
            .getListLiquidate({
                projectId,
                type: ContractEnum.LiquidationType.TERMINATION,
                status: ContractEnum.StatusEnum.APPROVED,
                q: keywords
            })
            .subscribe((res) => {
                if (res && res.itemsSize) {
                    this.listContracts = this.listContracts.concat(res.items);
                }
            });
        this.liquidationService
            .getListLiquidate({
                projectId,
                type: ContractEnum.LiquidationType.TRANSFER,
                status: ContractEnum.StatusEnum.ACCOUNTANT_WAITING,
            })
            .subscribe((res) => {
                if (res && res.itemsSize) {
                    this.listContracts = this.listContracts.concat(res.items);
                }
            });
    }
    getBankList(projectId) {
        this.service.getAllBankLocal(projectId).subscribe((res) => {
            this.listBank = res;
        });
    }
    getFormControl(name: string) {
        return this.mainForm.get(name);
    }

    onChangeBank(event) {
        if (!isNullOrUndefined(event)) {
            this.getFormControl("bankNumber").setValue(event.accountNumber);
        } else {
            this.getFormControl("bankNumber").setValue("");
        }
    }

    ngOnDestroy() {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }

    goBack() {
        this.router.navigate(["/money-order/receipt-list"]);
    }

    saveTransaction() {
        const money = this.mainForm.get("money").value
            ? MoneyToNumber.parseToNumber(
                  this.mainForm.get("money").value.toString()
              )
            : 0;
        if (money > this.maxMoney) {
            this.mainForm.get("money").setErrors(new Error());
            this.isHasMax = true;
        }

        this.isHasMin = !(money > 0);
        if (this.isHasMin) this.mainForm.get("money").setErrors(new Error());
        markAsTouched(this.mainForm);
        if (
            this.mainForm.get("state").value ===
            MoneyOrderConstant.TRANSFER_TYPE_CASH
        ) {
            this.mainForm.get("bankName").clearValidators();
            this.mainForm.get("bankName").updateValueAndValidity();
        } else {
            this.mainForm.get("bankName").setValidators([Validators.required]);
            this.mainForm.get("bankName").updateValueAndValidity();
        }
        if (this.mainForm.invalid) {
            this.toastr.error(
                "Không thành công",
                "Thông tin nhập vào chưa đầy đủ, vui lòng kiểm tra lại"
            );
            return;
        }

        const formValue = { ...this.mainForm.value };

        let body;
        body = {
            bankName: formValue.bankName,
            bankNumber: formValue.bankNumber,
            collectMoneyDate: formValue.collectMoneyDate,
            contentBank: formValue.contentBank,
            contractId: formValue.contractId,
            description: formValue.description,
            money: money,
            totalMoney: formValue.totalMoney,
            paymentBatch: formValue.paymentBatch,
            files: this.uploadedFiles,
            propertyTicket: {
                id: this.isPrimaryContractMode ?
                    _.get(this.listContracts.find((e) => e.contract.id === formValue.contractId), 'contract.primaryTransaction.id') :
                    formValue.ticketId
            },
            state: formValue.state,
            type: "REFUND",
        };

        this.isLoading$.next(true);
        if (this.id) {
            body.id = this.id;
            this.service
                .updateTransaction(body)
                .pipe(
                    finalize(() => this.isLoading$.next(false)),
                    takeUntil(this.unsubscribe$)
                )
                .subscribe((res: any) => {
                    if (res.ok && res.json().id) {
                        this.toastr.success(
                            "Thành công",
                            "Chỉnh sửa đề nghị thu tiền thành công."
                        );
                        this.router.navigate([`/money-order/refund`]);
                        // if (this.isPrimaryContractMode) {
                        //     this.router.navigate([`/money-order/receipt-list`]);
                        // } else {
                        //     this.router.navigate(
                        //         [`/money-order/accounting/waiting`],
                        //         { state: { id: id } }
                        //     );
                        // }
                    }
                });
            return;
        }
        this.service
            .createTransaction(body)
            .pipe(
                finalize(() => this.isLoading$.next(false)),
                takeUntil(this.unsubscribe$)
            )
            .subscribe((res: any) => {
                if (res.ok && res.json().id) {
                    this.toastr.success(
                        "Thành công",
                        "Tạo đề nghị chi tiền thành công."
                    );
                    this.router.navigate([`/money-order/refund`]);
                    // if (this.isPrimaryContractMode) {
                    //     this.router.navigate([`/money-order/receipt-list`]);
                    // } else {
                    //     this.router.navigate(
                    //         [`/money-order/accounting/waiting`],
                    //         { state: { id: id } }
                    //     );
                    // }
                }
            });
    }

    onChangeAddress(data: any) {
        this.address = data;
    }

    onTicketChange(event) {
        let ticket = event || null;
        this.productCode = _.get(ticket, "propertyUnit.code");
        this.customerName = _.get(ticket, "customer.personalInfo.name");
        this.identityNumber = _.get(ticket, "customer.identities[0].value");
        this.fullAddress = _.get(
            ticket,
            "customer.info.rootAddress.fullAddress"
        );

        this.mainForm.patchValue({
            money: event.amount,
        });
        this.maxMoney = event.amount;
    }

    onContractChange(event) {
        let contract = event || null;
        this.productCode = _.get(
            contract,
            "contract.primaryTransaction.propertyUnit.code"
        );
        this.customerName = _.get(contract, "customer.personalInfo.name");
        this.identityNumber = _.get(contract, "customer.identities[0].value");
        this.fullAddress = _.get(
            contract,
            "customer.info.rootAddress.fullAddress"
        );

        const installments = _.get(
            contract,
            "contract.policyPayment.schedule.installments"
        );
        const amount = installments
            ? installments.reduce((acc, curr) => curr.totalTransfered + acc, 0)
            : 0;
        this.mainForm.patchValue({
            money: amount,
        });
        this.maxMoney = amount;
    }

    onTransTypeChange(event) {
        this.mainForm.markAsUntouched();
        this.mainForm.controls["projectId"].clearValidators();
        this.mainForm.controls["projectId"].updateValueAndValidity();
        this.mainForm.controls["contractId"].clearValidators();
        this.mainForm.controls["contractId"].updateValueAndValidity();
        this.mainForm.controls["ticketId"].clearValidators();
        this.mainForm.controls["ticketId"].updateValueAndValidity();
        this.mainForm.controls["state"].clearValidators();
        this.mainForm.controls["state"].updateValueAndValidity();
        this.mainForm.controls["bankName"].clearValidators();
        this.mainForm.controls["bankName"].updateValueAndValidity();
        this.mainForm.controls["customerName"].clearValidators();
        this.mainForm.controls["customerName"].updateValueAndValidity();
        if (event === "YCDCH") {
            this.isPrimaryContractMode = false;
            this.mainForm.controls["projectId"].setValidators([
                Validators.required,
            ]);
            this.mainForm.controls["ticketId"].setValidators([
                Validators.required,
            ]);
            this.mainForm.controls["bankName"].setValidators([
                Validators.required,
            ]);

            this.mainForm.controls["projectId"].updateValueAndValidity();
            this.mainForm.controls["ticketId"].updateValueAndValidity();
            this.mainForm.controls["bankName"].updateValueAndValidity();
        } else {
            this.isPrimaryContractMode = true;
        }
    }

    onApprove() {
        let data = [this.dataModel];

        const dialogRef = this.dialog.open(ConfirmPopup, {
            width: "700px",
            data: {
                title: "Bạn có muốn duyệt phiếu thu này không?",
                textCancel: "Hủy",
                textOk: "Đồng ý",
            },
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                this.moneyOrderService
                    .accountingList(data)
                    .subscribe((res: any) => {
                        this.toastrService.success(
                            "Thành Công!",
                            "Duyệt phiếu thu thành công!"
                        );
                        this.router.navigate([`/money-order/receipt-list`]);
                    });
            }
        });
    }

    onReject() {
        const dialogRef = this.dialog.open(ReasonDialog, {
            width: "700px",
            data: {
                title: "Bạn có muốn từ chối phiếu thu này không?",
                textCancel: "Hủy",
                textOk: "Đồng ý",
            },
        });
        dialogRef.afterClosed().subscribe((result: any) => {
            if (result && result.execute) {
                const body = { reason: result.reason };
                this.moneyOrderService
                    .rejectAccounting(this.id, body)
                    .subscribe((res: any) => {
                        this.toastrService.success(
                            "Thành Công!",
                            "Từ chối phiếu thu thành công!"
                        );
                        this.goBack();
                    });
            }
        });
    }
}
