import { CPagination } from './../../../api-models/c-pagination';
import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { MessageTrainingModel, MessageTypeEnum } from 'app/shared/models/message-training-model';
import { moment } from 'fullcalendar';
import { LivestreamV2Service } from '../live-stream-v2.service';
import { Constant } from 'app/shared/constant/constant';
import { StorageService } from 'app/shared/services';
import { MatDialog } from '@angular/material';
import { ConfirmDeleteMessageDialogV2Component } from '../confirm-delete-message-dialog/confirm-delete-message-dialog.component';

@Component({
  selector: 'app-live-stream-v2-comment',
  templateUrl: './live-stream-v2-comment.component.html',
  styleUrls: ['./live-stream-v2-comment.component.scss']
})
export class LiveStreamV2CommentComponent implements OnInit {
  @ViewChild('scrollMe') private myScrollContainer: ElementRef;
  @Output() replyMess: EventEmitter<any> = new EventEmitter();
  @Output() getCommentCount: EventEmitter<any> = new EventEmitter();
  @Output() callVideo: EventEmitter<any> = new EventEmitter();
  @Input() eventId: string = '';
  @Input() userId: string = '';
  @Input() innerHeight: number = 0;
  @Input() isMobile?: boolean;
  @Input() isAdmin: boolean = false;
  @Input() pinnedMsg: any;
  dialogRef: any;

  pagination: CPagination<MessageTrainingModel> = new CPagination<MessageTrainingModel>();
  messages: MessageTrainingModel[] = [];
  emojiUrlPrefix = Constant.icon_url_emoji;
  messageType = MessageTypeEnum;

  isMsgsLoading = false;
  isMessPhoto = false;

  constructor(
    private livestreamService: LivestreamV2Service,
    private storageService: StorageService,
    private dialog: MatDialog,
  ) { }

  ngOnInit() {
  }

  getAllMessages(params?) {
    let p = params ? params : {};
    p.beforeDate = moment().valueOf();
    p.eventId = params.eventId ? params.eventId : this.eventId;

    this.isMsgsLoading = true;
    this.livestreamService.getAllMessages(p).subscribe(res => {
      res = res.rows ? res.rows : res;
      if (res.length > 0) {
        this.messages = [...res.reverse().map(r => new MessageTrainingModel(r)), ...this.messages];
        this.getCommentCount.emit(res.length);
      }
      this.isMsgsLoading = false;
      setTimeout(() => this.scrollToBottom(), 20);
    });
    this.livestreamService.getPinnedMessage({eventId: p.eventId}).subscribe(res => {
        if (res) {
            this.pinnedMsg = new MessageTrainingModel(res);
            if (res.type === 12) {
                this.pinnedMsg.extraData = res.extraData;
            }
        }
        setTimeout(() => this.scrollToBottom(), 20);
    });
  }

    pinnedChat(id) {
        this.livestreamService.pinnedChat(id).subscribe(res => {});
    }

  addMessage(message) {
    this.messages.push(message);
    setTimeout(() => this.scrollToBottom(), 100);
  }

  onReplyMess(data) {
    this.replyMess.emit({ ...data });
  }

  onCallVideo(userId) {
    this.callVideo.emit({ userId });
  }

  deleteMessageEvent(id: string) {
    const index = this.messages.findIndex(m => m.id === id);
    if (index != -1) {
      this.messages[index].isDelete = true;
    }
  }

  deleteMessage(id: string) {
    this.dialogRef = this.dialog.open(ConfirmDeleteMessageDialogV2Component, {
      data: {
        title: 'Bạn có muốn xóa tin nhắn này không?',
        action: 'Xóa'
      },
      disableClose: true,
      panelClass: 'confirm-dialog',
      width: '527px'
    });
    this.dialogRef.afterClosed().subscribe((data) => {
      if (typeof data === 'undefined') { return; }
      if (data.execute) {
        let params = {
          eventId: this.eventId,
          id
        };
        this.livestreamService.deleteMessage(params).subscribe();
      }
    });
  }

  scrollToBottom() {
    try {
      if (this.innerHeight) {
        this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
      } else {
        document.querySelector('.mat-tab-body-active .mat-tab-body-content').scrollTop = document.querySelector('.mat-tab-body-active .mat-tab-body-content').scrollHeight;
      }
    } catch (err) { }
  }
}
