# Quick Start - Sử dụng Authentication với access_token từ cookies

## Tổng quan

Hệ thống đã được cấu hình để tự động sử dụng `access_token` có sẵn trong cookies để gọi API CRM và lấy thông tin quyền. Không cần đăng nhập thêm.

## Cách hoạt động

1. **Token Detection**: Hệ thống tự động tìm `access_token` trong cookies
2. **API Call**: Gọi `https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account` với Bearer token
3. **Permission Mapping**: Map authorities từ API response thành E-Sale-Kit permissions
4. **Auto Update**: Permissions được cập nhật real-time trong toàn bộ ứng dụng

## Sử dụng cơ bản

### 1. <PERSON><PERSON><PERSON> tra permissions trong component

```typescript
import { ESaleKitAuthService } from "../services/e-sale-kit-auth.service";

export class MyComponent {
    constructor(private authService: ESaleKitAuthService) {}

    canEdit(): boolean {
        return this.authService.canEdit();
    }

    canEditMedia(): boolean {
        return this.authService.canEditModule("media");
    }
}
```

### 2. Sử dụng directive trong template

```html
<!-- Hiển thị khi có quyền edit -->
<button *hasPermission="'esalekit.edit'">Chỉnh sửa</button>

<!-- Hiển thị khi có quyền media -->
<div *hasPermission="'esalekit.edit.media'">
    <h3>Quản lý Media</h3>
</div>

<!-- Hiển thị khi có ít nhất một quyền -->
<section *hasPermission="['esalekit.edit.media', 'esalekit.edit.document']">Quản lý tài liệu và media</section>
```

### 3. Lấy thông tin user

```typescript
import { AuthApiService } from "../../../shared/services/auth/auth-api.service";

export class MyComponent {
    constructor(private authApiService: AuthApiService) {}

    getCurrentUser() {
        return this.authApiService.getCurrentUser();
    }

    getUserAuthorities() {
        return this.authApiService.getCurrentAuthorities();
    }
}
```

## Test hệ thống

### 1. Sử dụng AuthTestComponent

Thêm component test vào module để kiểm tra:

```typescript
// Trong routing hoặc component
<app-auth-test></app-auth-test>
```

### 2. Test trên Localhost

Hệ thống đã được tối ưu để hoạt động với localhost:

```javascript
// Trong AuthTestComponent, click "Set Test Token (Localhost)" để tạo token test
// Hoặc set token thủ công:
document.cookie = "access_token=your-real-token-here; path=/";

// Debug cookies
authApiService.debugCookies();

// Set test token
authApiService.setTestTokenForLocalhost("your-test-token");
```

### 3. Kiểm tra trong Console

```javascript
// Kiểm tra token
document.cookie.split(";").find((c) => c.includes("access_token"));

// Kiểm tra tất cả cookies
console.log("All cookies:", document.cookie);

// Test API call với token thật
fetch("https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account", {
    headers: {
        Authorization: "Bearer YOUR_REAL_TOKEN",
    },
})
    .then((r) => r.json())
    .then(console.log);
```

### 4. Localhost Cookie Settings

Cho localhost, hệ thống sử dụng:

-   `secure: false` (không yêu cầu HTTPS)
-   `sameSite: 'Lax'` (ít strict hơn)
-   Fallback đến `document.cookie` nếu `ngx-cookie-service` không hoạt động

## Troubleshooting

### Token không được tìm thấy

-   Kiểm tra cookie name: `access_token`
-   Kiểm tra domain và path của cookie
-   Đảm bảo token chưa hết hạn

### API không phản hồi

-   Kiểm tra CORS settings
-   Kiểm tra token format (Bearer prefix)
-   Kiểm tra network trong DevTools

### Permissions không hoạt động

-   Kiểm tra API response format
-   Kiểm tra authorities mapping trong console
-   Kiểm tra directive syntax

## API Response Format

API cần trả về format sau:

```json
{
    "success": true,
    "data": {
        "id": "user-id",
        "username": "username",
        "email": "<EMAIL>",
        "fullName": "Full Name",
        "authorities": ["esalekit.edit", "esalekit.edit.overview", "esalekit.edit.media", "esalekit.edit.document"]
    }
}
```

## Permissions Mapping

| Authority                | E-Sale-Kit Permission |
| ------------------------ | --------------------- |
| `esalekit.edit`          | `canEdit`             |
| `esalekit.edit.overview` | `canEditOverview`     |
| `esalekit.edit.ground`   | `canEditGround`       |
| `esalekit.edit.utility`  | `canEditUtility`      |
| `esalekit.edit.product`  | `canEditProduct`      |
| `esalekit.edit.term`     | `canEditTerm`         |
| `esalekit.edit.media`    | `canEditMedia`        |
| `esalekit.edit.document` | `canEditDocument`     |
| `esalekit.edit.news`     | `canEditNews`         |

## Debug Mode

Để bật debug mode, thêm vào localStorage:

```javascript
localStorage.setItem("esalekit_debug", "true");
```

Hoặc sử dụng AuthTestComponent để xem chi tiết thông tin authentication.

## Next Steps

1. **Test với token thật**: Đặt access_token vào cookies và test
2. **Kiểm tra permissions**: Verify các quyền được map đúng
3. **Update UI**: Cập nhật các component khác để sử dụng directive mới
4. **Remove old code**: Xóa code authentication cũ khi đã test xong

## Support

Nếu gặp vấn đề, kiểm tra:

1. Console logs cho error messages
2. Network tab cho API calls
3. Application tab cho cookies
4. AuthTestComponent cho debug info
