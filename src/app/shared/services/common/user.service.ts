import { Injectable } from '@angular/core';
import 'rxjs/add/observable/throw';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/catch';
import { ConstantUrl } from '../../constant/url';
import { SecurityService } from '../security.service';
import { StorageService } from '../storage.service';
import { DataService } from '../data.service';
import { Observable } from 'rxjs/Observable';
import { EventChangeService } from '../event.change.service';
import { DateToFormat } from 'app/shared/parse/date-to-format';
import { VoiceCallService } from '../voicecall.service';
import { Constant } from 'app/shared/constant/constant';
import { environment } from 'environments/environment';
import { CookieService } from 'ngx-cookie-service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
declare let ga: Function;
// Implementing a Retry-Circuit breaker policy
// is pending to do for the SPA app
@Injectable()
export class UserService {
    Constant = Constant;
    ConstantUrl = ConstantUrl;
    isUnAuthen = false;
    private user: any;
    constructor(
        private service: DataService,
        private securityService: SecurityService,
        private storageService: StorageService,
        private eventChangeService: EventChangeService,
        private voiceCallService: VoiceCallService,
        private cookieService: CookieService,
        private http: HttpClient
    ) { }

    login(body) {
        return this.service.post(ConstantUrl.url_sts_domain + 'auth/login', body)
            .map((res) => {
                let data: any = res.json() || {};
                this.storageService.store('userId', data.id);
                this.securityService.setAuthorizationData(data.access_token, null, data.expires_in);
                return data;
            });
    }
    userProfile(isUnAuthen): any {
        if (!isUnAuthen) {
            this.securityService.setAuthor([], true);
            return Observable.of(true);
        }

        // Gọi API account mới với bearer token từ cookie
        return this.loadAccountFromNewApi()
            .map((accountData) => {
                // Lưu user data từ API cũ (nếu cần)
                this.loadUserDataFromOldApi();

                // Xử lý permissions từ API mới
                const convertedPermissions = this.convertNewApiResponseToOldFormat(accountData);
                const isFullPermission = this.determineFullPermission(accountData);

                this.eventChangeService.emitChange({
                    boardcast: 'login'
                });
                this.getMyOrgs();

                return this.securityService.setAuthor(convertedPermissions, isFullPermission);
            });
    }

    /**
     * Gọi API account mới với bearer token từ cookie
     */
    private loadAccountFromNewApi(): Observable<any> {
        const newApiUrl = 'https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account';

        console.log('=== DEBUG: loadAccountFromNewApi started ===');
        console.log('API URL:', newApiUrl);

        // Lấy token từ cookie
        const token = this.getAccessTokenFromCookie();
        if (!token) {
            console.error('❌ No access token found in cookies');
            console.log('=== DEBUG: loadAccountFromNewApi failed - no token ===');
            return Observable.throw('No access token found');
        }

        // Tạo headers với bearer token
        const headers = new HttpHeaders({
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        });

        console.log('✅ Calling new account API with token from cookie...');
        console.log('Headers:', headers.keys());

        // Sử dụng HttpClient trực tiếp để bypass DataService
        return this.http.get(newApiUrl, { headers })
            .map((response) => {
                console.log('New API response:', response);
                return response;
            })
            .catch((error) => {
                console.error('Error calling new account API:', error);
                // Fallback to old API if new API fails
                return this.loadUserDataFromOldApi();
            });
    }

    /**
     * Load user data từ API cũ (fallback hoặc bổ sung)
     */
    private loadUserDataFromOldApi(): Observable<any> {
        return this.service.get(ConstantUrl.url_user_query + '/' + this.storageService.retrieve('userId'))
            .map((res) => {
                let data: any = res || {};
                this.user = data;
                this.storageService.store('email', data.email);
                this.storageService.store('isManager', data.managerAt);
                this.storageService.store('isFirstLogin', data.isFirstLogin);
                this.storageService.store('pos', data.pos);
                this.storageService.store('user-profile', data);
                if (data.role && data.role.length > 0) {
                    this.storageService.store('roleName', data.role[0].name);
                    this.storageService.store('roleId', data.roleId);
                    this.storageService.store('routerLink', data.role[0].routerLink);
                } else {
                    this.storageService.store('roleName', '');
                }
                return data;
            });
    }

    /**
     * Convert API response mới sang format cũ để tương thích với SecurityService.setAuthor()
     */
    private convertNewApiResponseToOldFormat(apiResponse: any): any[] {
        const permissions = [];

        if (apiResponse && apiResponse.roles && apiResponse.roles.length > 0) {
            apiResponse.roles.forEach(role => {
                if (role.functionRoles && role.functionRoles.length > 0) {
                    role.functionRoles.forEach(functionRole => {
                        if (functionRole.features && functionRole.features.length > 0) {
                            functionRole.features.forEach(feature => {
                                // Convert từ format mới sang format cũ
                                const permission = {
                                    featureName: feature.featureName,
                                    action: {
                                        "action:READ": true,  // Mặc định có quyền READ
                                        "action:WRITE": this.determineWritePermission(feature.featureName)
                                    }
                                };
                                permissions.push(permission);
                            });
                        }
                    });
                }
            });
        }

        console.log('Converted permissions:', permissions);
        return permissions;
    }

    /**
     * Xác định quyền WRITE dựa trên featureName
     */
    private determineWritePermission(featureName: string): boolean {
        if (!featureName) return false;

        const writeKeywords = [
            'edit', 'create', 'update', 'delete', 'import', 'export',
            'add', 'remove', 'modify', 'change', 'save', 'upload'
        ];

        const lowerFeatureName = featureName.toLowerCase();
        return writeKeywords.some(keyword => lowerFeatureName.includes(keyword));
    }

    /**
     * Xác định user có full permission không
     */
    private determineFullPermission(apiResponse: any): boolean {
        // Logic để xác định full permission
        if (apiResponse && apiResponse.roles) {
            const totalFeatures = this.countTotalFeatures(apiResponse);
            // Nếu có nhiều hơn 50 features thì coi như full permission
            if (totalFeatures > 50) {
                return true;
            }

            // Hoặc check có permission admin không
            return this.hasAdminPermission(apiResponse);
        }
        return false;
    }

    /**
     * Đếm tổng số features
     */
    private countTotalFeatures(apiResponse: any): number {
        let count = 0;
        if (apiResponse.roles) {
            apiResponse.roles.forEach(role => {
                if (role.functionRoles) {
                    role.functionRoles.forEach(functionRole => {
                        if (functionRole.features) {
                            count += functionRole.features.length;
                        }
                    });
                }
            });
        }
        return count;
    }

    /**
     * Kiểm tra có permission admin không
     */
    private hasAdminPermission(apiResponse: any): boolean {
        if (apiResponse.roles) {
            for (const role of apiResponse.roles) {
                if (role.functionRoles) {
                    for (const functionRole of role.functionRoles) {
                        if (functionRole.features) {
                            for (const feature of functionRole.features) {
                                if (feature.featureName &&
                                    (feature.featureName.toLowerCase().includes('admin') ||
                                     feature.featureName.toLowerCase().includes('master'))) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * Lấy access_token từ cookie
     */
    private getAccessTokenFromCookie(): string {
        // Debug: Check all cookies
        console.log('All cookies:', document.cookie);

        // Try different possible cookie names
        let token = this.cookieService.get('access_token');
        console.log('Token from "access_token":', token);

        if (!token) {
            token = this.cookieService.get('authorizationData');
            console.log('Token from "authorizationData":', token);
        }

        if (!token) {
            // Try manual parsing
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'access_token' || name === 'authorizationData') {
                    token = value;
                    console.log(`Token found manually from "${name}":`, token);
                    break;
                }
            }
        }

        console.log('Final token result:', token ? 'Found' : 'Not found');
        return token;
    }

    /**
     * Kiểm tra access_token trong cookie và load permissions nếu có
     * Không cần đăng nhập lại
     */
    public checkTokenAndLoadPermissions(): Observable<boolean> {
        console.log('=== DEBUG: checkTokenAndLoadPermissions started ===');

        // Kiểm tra có token trong cookie không - sử dụng CookieService để lấy access_token
        const token = this.getAccessTokenFromCookie();

        if (!token) {
            console.warn('❌ No access_token found in cookie');
            console.log('=== DEBUG: checkTokenAndLoadPermissions failed - no token ===');
            return Observable.of(false);
        }

        console.log('✅ Found access_token in cookie, loading permissions...');
        console.log('Token preview:', token.substring(0, 20) + '...');

        // Gọi API để lấy permissions
        return this.loadAccountFromNewApi()
            .map((accountData) => {
                // Convert và set permissions
                const convertedPermissions = this.convertNewApiResponseToOldFormat(accountData);
                const isFullPermission = this.determineFullPermission(accountData);

                // Set permissions vào system
                this.securityService.setAuthor(convertedPermissions, isFullPermission);

                console.log('Permissions loaded successfully from existing token');
                return true;
            })
            .catch((error) => {
                console.error('Error loading permissions with existing token:', error);

                // Nếu token invalid (401/403), có thể clear token
                if (error.status === 401 || error.status === 403) {
                    console.warn('Token appears to be invalid');
                    // Có thể clear token hoặc redirect to login
                    // this.securityService.resetAuthorizationData();
                }

                return Observable.of(false);
            });
    }

    /**
     * Kiểm tra user đã được authenticate chưa
     */
    public isUserAuthenticated(): boolean {
        const token = this.getAccessTokenFromCookie();
        const hasPermissions = this.storageService.retrieve('permissions');

        return !!(token && hasPermissions);
    }

    /**
     * Load permissions từ token có sẵn (dùng khi app khởi động)
     */
    public initializeFromExistingToken(): Observable<boolean> {
        console.log('Initializing app from existing token...');

        if (this.isUserAuthenticated()) {
            console.log('User already authenticated, skipping API call');
            return Observable.of(true);
        }

        return this.checkTokenAndLoadPermissions();
    }

    /**
     * Debug method để test cookie access
     */
    public debugCookieAccess(): void {
        console.log('=== COOKIE DEBUG ===');
        console.log('1. All cookies via document.cookie:', document.cookie);

        console.log('2. CookieService methods:');
        console.log('   - cookieService.get("access_token"):', this.cookieService.get('access_token'));
        console.log('   - cookieService.get("authorizationData"):', this.cookieService.get('authorizationData'));
        console.log('   - cookieService.getAll():', this.cookieService.getAll());

        console.log('3. Manual cookie parsing:');
        const cookies = document.cookie.split(';');
        cookies.forEach(cookie => {
            const [name, value] = cookie.trim().split('=');
            if (name.includes('token') || name.includes('auth') || name.includes('access')) {
                // console.log(`   - Found: ${name} = ${value?.substring(0, 20)}...`);
            }
        });

        console.log('4. SecurityService method:');
        console.log('   - securityService.getCookieToken():', this.securityService.getCookieToken());

        console.log('5. Test getAccessTokenFromCookie():');
        const token = this.getAccessTokenFromCookie();
        // console.log('   - Result:', token ? `Found: ${token.substring(0, 20)}...` : 'Not found');

        console.log('==================');
    }

    getEmployee() {
        return this.service.get(ConstantUrl.url_employee_query + 'employee/getProfile')
        .map((res) => {
            let data: any = res || {};
            let gaUserId = data.name + " - " + data.email;
            let trackerUserId = data.id;
            this.user = data;
            data.avatar = data.images ? data.images.avatar : '';
            this.storageService.store('name', data.name);
            this.storageService.store('extPhone', data.extPhone);
            this.storageService.store('user-profile', data);
            this.storageService.store('phone', data.phone);
            localStorage.setItem('ga_user_id',  gaUserId);
            localStorage.setItem('tracker_user_Id',  trackerUserId);
            return res;
        })
    }

    getMyOrgs() {
        return this.service.get(ConstantUrl.url_orgchart_query + 'orgchart/config/byUser')
        .subscribe((res) => {
            if (res && res.callConfig) {
                this.storageService.store('allowCall', res.callConfig.allowCall);
            } else {
                this.storageService.store('allowCall', false);
            }
        })
    }

    timekeeping() {
        let date = new Date();
        let body = {
            "iodate": DateToFormat.parseDate(date, 'yyyy-MM-dd hh:mm:ss'),
            "ioday":  date.getDate(),
            "iomonth": date.getMonth() + 1,
            "ioyear": date.getFullYear(),
            "iohour": date.getHours(),
            "iominute": date.getMinutes(),
            "iosecond": date.getSeconds()
        };
        const url = ConstantUrl.url_employee_query + 'tavico/employee/timekeeping';
        return this.service.post(url, body);
    }
    getUser() {
        return this.user;
    }
    register(body) {
        return this.service.post(ConstantUrl.url_sts_domain + 'auth/signup', body)
            .map((res) => {
                let data: any = res.json() || {};
                this.securityService.setAuthorizationData(data.access_token, null);
                return data;
            })
    }
    setUnAuthenticate() {
        this.isUnAuthen = true;
    }
}
