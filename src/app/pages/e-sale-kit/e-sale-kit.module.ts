import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from "@angular/forms";
import { ProjectCommonPageComponent } from './pages/project-common-page/project-common-page.component';
import { ContentBuilderComponent } from './components/content-builder/content-builder.component';
import { ETabMenuComponent } from './components/e-tab-menu/e-tab-menu.component';
import { ProjectGalleryComponent } from './pages/project-gallery/project-gallery.component';
import { ProjectDocumentComponent } from './pages/project-document/project-document.component';
import { ProjectNewsComponent } from './pages/project-news/project-news.component';
import { ProjectGroundComponent } from './pages/project-ground/project-ground.component';
import { ProjectTopComponent } from './pages/project-top/project-top.component';
import { EditBarComponent } from './components/edit-bar/edit-bar.component';
import { GalleryImageViewerComponent } from './components/gallery-image-viewer/gallery-image-viewer.component';
import { ProjectGroundService } from './services/project-ground.service';
import { SafePipe } from './pipe/safe.pipe';
import { GalleryPicturesComponent } from './components/gallery-pictures/gallery-pictures.component';
import { GalleryUploadComponent } from './components/gallery-upload/gallery-upload.component';
import { GalleryUploadFileComponent } from './components/gallery-upload-file/gallery-upload-file.component';
import { AddFolderPopupComponent } from './components/add-folder-popup/add-folder-popup.component';
import { GaleryPopupComponent } from './components/galery-popup/galery-popup.component';
import { PageCaculatorComponent } from './pages/page-caculator/page-caculator.component';
import { NewsPopupComponent } from './components/news-popup/news-popup.component';
import { TabPopupComponent } from './components/tab-popup/tab-popup.component';
import { MenuPopupComponent } from './components/menu-popup/menu-popup.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule } from '@angular/forms';
import { EShortMenuComponent } from './components/e-short-menu/e-short-menu.component';
import {NgxMasonryModule} from "ngx-masonry";
import { ERegisterAdvisorsComponent } from './components/e-register-advisors/e-register-advisors.component';
import { EQuickLinkComponent } from './components/e-quick-link/e-quick-link.component';
import { ESaleKitComponent } from './pages/e-sale-kit/e-sale-kit.component';
import { EMenuComponent } from './components/e-menu/e-menu.component';
import { ConfirmPopupComponent } from './components/confirm-popup/confirm-popup.component';
import { ESaleKitRoutingModule } from './e-sale-kit-routing.module';
import { DirectivesModule } from 'app/shared/directives/directives.module';
import { LoadingAnimationModule } from 'app/shared/components/loading-animation/loading-animation.module';
import { ConfirmDeactivateGuard } from './guards/confirm-deactivate.guard';
import { SharedModule } from 'app/shared';
import { MessageRegisterComponent } from './components/message-register/message-register.component';
import { SafeHtmlPipe } from './pipe/safe-html.pipe';
import { UserAuthorityDirective } from './services/user-authority.directive';
import { UploadUrlComponent } from './components/upload-url/upload-url.component';
import { HasPermissionDirective } from './directives/has-permission.directive';
import { ESaleKitAuthService } from './services/e-sale-kit-auth.service';
import { AuthApiService } from '../../shared/services/auth/auth-api.service';
import { CookieService } from 'ngx-cookie-service';
@NgModule({
  declarations: [
    ESaleKitComponent,
    EMenuComponent,
    ProjectCommonPageComponent,
    ContentBuilderComponent,
    ETabMenuComponent,
    ProjectGalleryComponent,
    ProjectDocumentComponent,
    ProjectNewsComponent,
    ProjectGroundComponent,
    ProjectTopComponent,
    EditBarComponent,
    GalleryImageViewerComponent,
    SafePipe,
    SafeHtmlPipe,
    GalleryImageViewerComponent,
    GalleryPicturesComponent,
    GalleryUploadComponent,
    AddFolderPopupComponent,
    UserAuthorityDirective,
    GalleryUploadFileComponent,
    ConfirmPopupComponent,
    GaleryPopupComponent,
    NewsPopupComponent,
    TabPopupComponent,
    MenuPopupComponent,
    PageCaculatorComponent,
    EShortMenuComponent,
    ERegisterAdvisorsComponent,
    EQuickLinkComponent,
    MessageRegisterComponent,
    UploadUrlComponent,
    HasPermissionDirective
  ],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    ESaleKitRoutingModule,
    LoadingAnimationModule,
    DirectivesModule,
    NgSelectModule,
    ReactiveFormsModule,
    NgxMasonryModule,
    DirectivesModule, 
  ],
  entryComponents: [
    GalleryImageViewerComponent,
    AddFolderPopupComponent,
    ConfirmPopupComponent,
    GaleryPopupComponent,
    NewsPopupComponent,
    TabPopupComponent,
    MenuPopupComponent,
    ERegisterAdvisorsComponent,
    UploadUrlComponent
  ],
  providers: [
    ProjectGroundService,
    LoadingAnimationModule,
    ConfirmDeactivateGuard,
    ESaleKitAuthService,
    AuthApiService,
    CookieService
  ],
})
export class ESaleKitModule { }
