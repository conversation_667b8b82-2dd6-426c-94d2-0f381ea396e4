import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild } from '@angular/core';
import { PropertyService } from 'app/pages/property';
import { SalesProgramModel } from 'app/shared/models/saleProgram.model';

import { PropertyTable } from '../property-table.model';

import * as excel2table from '../../../../../assets/js/render-excel';
import { ProjectSetting } from '../../project-setting.model';
import { BehaviorSubject } from 'rxjs';
import { DataService } from 'app/shared/services';
const handlebars = require('handlebars');
declare let $: any;

@Component({
  selector: 'app-unit-table',
  templateUrl: './unit-table.component.html',
  styleUrls: ['./unit-table.component.scss']
})
export class UnitTableComponent implements OnInit, OnChanges {
  @ViewChild('excelTable') excelTable: ElementRef;
  @ViewChild('imageMapTable') imageMapTable: ElementRef;
  public isLoading$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  @Input() propertyUnits: PropertyTable[] = [];
  @Input() currentSalesProgram: SalesProgramModel = new SalesProgramModel();
  @Input() settingProject: ProjectSetting = new ProjectSetting();
  @Input() recoveryUnitId: string = '';
  @Input() templateCode: string = '';
  @Input() templateName: string = '';
  @Input() marketPos: string = '';


  @Output() openPriorityPopup: EventEmitter<PropertyTable> = new EventEmitter();
  @Output() recount: EventEmitter<any> = new EventEmitter();

  firstLoad: boolean = true;
  isRenderUI: boolean = false;
  isHide: boolean = false;
  isImageMap: boolean = false;

  public excelTemplate: any = null;
  public refeshUI: boolean = false;
  public changedRowCodes: string[] = [];

  constructor(
    private propertyService: PropertyService,
    public service: DataService) { }

  ngOnInit() {
    const actionExcel: any = this.excelTable.nativeElement;
    actionExcel.addEventListener('click', this.openUnitDetail.bind(this));
    this.doConvert();
  }

  ngOnChanges() {
    this.isLoading$.next(true);
    if (this.refeshUI) {
      this.refeshUI = false;
      this.isRenderUI = false;
      this.doConvert();
    } else {
      if (this.isRenderUI) {
        this.renderUnit();
      }
    }
  }

  renderUnit() {
    while (this.changedRowCodes && this.changedRowCodes.length > 0) {
      const code = this.changedRowCodes.pop();
      const unit = this.propertyUnits.find(x => x.view1 === code);
      if (unit) {
        this.setCellColor(code, unit);
      } else {
        this.resetCellColor(code);
      }
    }
    this.isLoading$.next(false);
  }

  doConvert() {
    this.getTemplate().then(() => {
        this.renderUI();
    });
    this.isLoading$.next(false);
  }
  getTemplate() {
    return new Promise((resolve) => {
      if (this.templateName) {
        this.isHide = false;
        const unitTableTemplate = this.currentSalesProgram.unitTableTemplateUrl.find(x => x.templateName === this.templateName);
        if (!unitTableTemplate) {
          resolve(null);
        }
        const unitTableTemplateUrl = unitTableTemplate.file.absoluteUrl;
        this.propertyService.getFileFromUrl(unitTableTemplateUrl).subscribe(res => {
        this.excelTemplate = res;
        resolve(null);
        });

      } else {
        this.isHide = true;
        this.isLoading$.next(false);
      }
    });
  }
  renderUI() {
    excel2table.render('.contentexcel2table', this.excelTemplate).then(result => {
      if (result) {
        this.isRenderUI = true;
        this.propertyUnits.forEach(unit => {
          this.setCellColor(unit.view1, unit);
        });
        this.recount.emit();
        this.isLoading$.next(false);
      }
    });
  }

  setCellColor(code: string, unit: PropertyTable) {
    const element = this.excelTable.nativeElement.querySelector(`#${code}`);
    if (element) {
      element.childNodes[1].style.visibility = 'visible';
      element.style.cursor = 'pointer';
      element.style.color = unit.textColor ? unit.textColor : 'black';
      element.style.opacity = unit.posId === this.recoveryUnitId ? '0.5' : '1';
      element.style['text-decoration'] = unit.posId ? '' : 'underline';
      element.style.position = 'relative';

      element.className = (this.marketPos && unit.posId === this.marketPos) ? 'success' : unit.primaryStatus.toLowerCase();

      if(unit.liquidateType === 'TRANSFER' && unit.primaryStatus === 'CLOSE') {
        element.classList.add('transfer')
      }
      if(unit.contract && unit.contract.type  === 'deposit' && unit.primaryStatus === 'SUCCESS') {
        element.classList.add('deposit')
      }

      if((unit.price || unit.priceVat) && unit.publishPrice) return;
      const className = (unit.price || unit.priceVat) ? 'dolar-green' : '';
      element.innerHTML += `<i class='fa fa-usd ${className}'></i>`;

    }
  }
  resetCellColor(code: string) {
    const element = this.excelTable.nativeElement.querySelector(`#${code}`);
    if (element) {
      element.childNodes[1].style.visibility = 'hidden';
      element.style.cursor = 'auto';
      element.className = '';
    }
  }

  openUnitDetail(event) {
    if (event.target.attributes['data-id']) {
      const currentUnit = this.propertyUnits.find(x => x.view1 === event.target.attributes['data-id'].value);
      if (currentUnit) {
        this.openPriorityPopup.emit(currentUnit);
      }
    }
  }
}
