import {
    Component,
    Inject,
    Injector,
    Input,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
    ViewChild,
} from "@angular/core";
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { CustomerService } from "app/pages/customer";
import { FormInputAddressComponent } from "app/shared/components/app-form-input";
import { Constant } from "app/shared/constant/constant";
import { Address } from "app/shared/models/address.model";
import { PaymentProgressModel } from "app/shared/models/payment-progress.models";
import { debounceTime, startWith, switchMap, takeUntil } from "rxjs/operators";
import { Observable, Subject } from "rxjs/Rx";
import { ContractService } from "../contract.service";
import moment = require("moment");
import { ToastrService } from "ngx-toastr";
import { ContractConstants } from "app/shared/constant/contract.constant";
import { UserV2Service } from "app/shared/services/common/user-v2.service";
import { AuthorizeService } from "app/shared/authenticate";
import { CUser } from "app/api-models";
import { BehaviorSubject, forkJoin, Subscription } from "rxjs";
import { PaymentPolicyModel } from "../../../shared/models/payment-policy.models";
import { ConfirmPopup } from "../../../shared/components/confirm-popup/confirm-popup";
import { MatDialog, MatTabGroup } from "@angular/material";
import { markAsTouched } from "../../../shared/utility/form-utility";
import { AppSettings } from "../../../app.settings";
import { SalePolicyConstant } from "../../../shared/constant/sale-policy.constant";
import { getCurrentTimeSigned } from "app/shared/utility/utility";
import { PopupProvisionalInterestDetailComponent } from "./../popup-provisional-interest-detail/popup-provisional-interest-detail.component";
import { PopupInterestSlipDetailComponent } from "./../popup-interest-slip-detail/popup-interest-slip-detail.component";
import { DOCUMENT } from "@angular/common";
import { DepositContractModel } from "app/shared/models/deposit-contract.model";
import { BaseComponent } from "app/shared/components/base.component";
import { ContractEnum } from "app/shared/enum/contract.enum";
import * as _ from 'lodash';
import { PreviewExportDialog } from "../debt-report/preview-export/preview-export.component";
import { DiscountTypeRealEstateEnum } from "app/shared/enum/sale-policy.enum";

@Component({
    selector: "app-purchase-detail",
    templateUrl: "./purchase-detail.component.html",
    styleUrls: ["./purchase-detail.component.scss"],
})
export class PurchaseDetailComponent extends BaseComponent implements OnInit, OnDestroy {
    @ViewChild("formAddress") formAddress: FormInputAddressComponent;
    @ViewChild("tabGroup") tabGroup: MatTabGroup;

    @Input() isTransferred: boolean = false;
    public isLoading$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    mainForm: FormGroup = null;
    id = "";
    projects;
    selectedTab: number = 0;
    status = "";
    listPayments = ContractConstants.ListPayments;
    listPolicyPayment = [];
    listPolicyDiscount = [];
    listCurrentPolicyDiscount = [];
    isShowOwned = false;
    public isBtnSave: boolean = false;
    public isBtnEdit: boolean = false;
    public isCreate: boolean = false;
    public isUpdate: boolean = false;
    isApprove = false;
    schedules: PaymentProgressModel[] = [];
    rootAddress: any = null;
    address: any = null;
    unsubscribe$: Subject<any> = new Subject();
    filteredCustomer: Observable<any[]>;
    filteredlistHDC: any = [];
    Constant = Constant;
    isPermission = false;
    userId = "";
    isValidPaymentPolicy: boolean = true;
    isValidDiscountPolicy: boolean = true;
    invalidPaymentPolicyMessage: string = "";
    invalidDiscountPolicyMessage: string = "";
    public transferHistories: any[] = [];
    selectedDiscountPolicy: any = {};
    fileUrl: string;
    project: any;

    public isDisabledAddress: boolean = false;
    public submitPermission: boolean = false;
    public approvePermission: boolean = false;
    public allowEditAfterApproval: boolean = false;
    public rejectPermission: boolean = false;
    @Input() deposit: any = {};
    public permission: boolean = false;
    public statusName: string = ContractEnum.StatusNameEnum.INIT;
    public rejected: boolean = false;
    user: CUser = new CUser();
    public paymentHistoryData = [];
    public productPrice: number = 0;
    public totalTransfered: number = 0;
    private ticketCodeChanged: Subject<string> = new Subject<string>();
    private isGetData: boolean = true;
    primaryTransactionId = "";

    haveProvisionalInterest: boolean = false;
    isFromDeposit = false;
    interestSlipsData = [];
    public subscription: Subscription;
    listStatus = Object.keys(ContractConstants.Status).map(txn => {
        return { id: txn, name: ContractConstants.Status[txn] };
    });
    uploadedFiles: any[] = [];
    displaySaveDocBtn: boolean = false;
    listPrintDocuments: any[] = [];
    listInstallments: any[] = [];
    requireDocument: boolean;

    public installmentTypes = SalePolicyConstant.installmentTypes;

    constructor(
        injector: Injector,
        @Inject(DOCUMENT) private document,
        public appSettings: AppSettings,
        public router: Router,
        public route: ActivatedRoute,
        private formBuilder: FormBuilder,
        private contractService: ContractService,
        public toastr: ToastrService,
        private customerService: CustomerService,
        private authorizeService: AuthorizeService,
        public userV2Service: UserV2Service,
        private dialog: MatDialog
    ) {
        super(injector.get(ContractService), DepositContractModel, injector);
        this.settings = this.appSettings.settings;
        this.infoMessagingPattern = this.messagingPattern + 'msx-adsg.info.primary-contract';
        this.errorMessagingPattern = this.messagingPattern + 'msx-adsg.error.primary-contract';
    }

    ngOnInit() {
        this.getUser();
        this.initForm();
        const params = this.route.snapshot.params;
        const id = params["id"];
        if (id && id !== "create") {
            this.id = id;
            this.getData(this.id, true, false, true);
        } else {
            this.isCreate = true;
            this.enableForm();
            this.isBtnSave = true;

        }

        const queryParams = this.route.snapshot.queryParams;
        if (queryParams && queryParams.id){
            this.isFromDeposit = true;
            this.getData(queryParams.id, false, true, false, true);
        }

        this.subscribe();
        this.eventSubscribe = this.eventChangeService.emitChangeSource.subscribe((data) => {
            switch (data.broadcast) {
                case 'primary-contractCreated':
                case 'primary-contractUpdated':
                    this.getData(this.id, true);
                    break;
            }
        });

        this.subscription = this.ticketCodeChanged
            .pipe(debounceTime(300))
            .subscribe((text) => {
                this.filterTicketList(text);
            });
        this.authorizeService.hasAuthority([ 'PRIMARY', 'CONTRACT', 'UPDATE', 'APPROVEMENT'])
        .then(isHasRole => {
            if(this.id) {
                this.allowEditAfterApproval = isHasRole;
                this.isBtnEdit = true;
            }

        });
    }

    ngOnDestroy() {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
        this.subscription.unsubscribe();
    }

    getAllData() {
        forkJoin(
            this.contractService.getListPolicy({
                type: "payment",
                status: ContractEnum.StatusEnum.APPROVED,
                isValidDate: true ,
            }),
            this.contractService.getListPolicy({
                type: "discount",
                status: ContractEnum.StatusEnum.APPROVED,
                isValidDate: true ,
            }),
            this.contractService.getAllPaymentProgress()
        )
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe((res: any) => {
                if (res[0]) {
                    this.listPolicyPayment = res[0].filter(paymentPolicy => moment(paymentPolicy.startDate) <= moment() &&
                        moment() <= moment(paymentPolicy.expiredDate) && 
                        paymentPolicy.active).map((e) =>({
                            ...e,
                            name: e.code + ' - ' + e.name
                        }));
                    this.setPaymentProgress();
                }
                if (res[1]) {
                    this.listPolicyDiscount = res[1].filter(discountPolicy => moment(discountPolicy.startDate) <= moment() &&
                        moment() <= moment(discountPolicy.expiredDate) && discountPolicy.project.id === this.project.id &&
                        discountPolicy.active).map((e) =>({
                            ...e,
                            name: e.code + ' - ' + e.name
                        }));

                    this.listCurrentPolicyDiscount =   this.listPolicyDiscount;  
                    if(this.mainForm && this.id) {
                        if(
                            (!this.mainForm.get('housePrice').value && 
                            !this.mainForm.get('housePriceVat').value && 
                            !this.mainForm.get('landPrice').value && 
                            !this.mainForm.get('landPriceVat').value) || (
                                this.mainForm.get('priceType').value === 'contract'
                            )
                        ) {
                            this.listCurrentPolicyDiscount =   this.listPolicyDiscount.filter( discount => {
                                if(discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.HOUSE && discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.LAND) {
                                    return discount
                                }
                            }) 
                        }
                    }
                }
                if (res[2]) {
                    this.schedules = res[2];
                }
            });
    }

    getPriceType(calcPriceVat: boolean, calcContractPrice: boolean) {
        if (calcContractPrice) {
            return 'contract'
        };

        return calcPriceVat ? 'vat': 'non-vat'
    }

    get disallowCalcByContractPrice() {
        return this.mainForm.get("contractPrice").value <= 0;
    }

    initForm() {
        this.isDisabledAddress = true;
        this.mainForm = this.formBuilder.group({
            // thong tin chung
            code: [{ value: "", disabled: true }],
            name: [{ value: "", disabled: true }],
            selectedHDC: [{ value: "", disabled: true }, Validators.required],
            nameCustomer: [{ value: "", disabled: true }],
            startDate: [{ value: "", disabled: true }],
            expiredDate: [{ value: "", disabled: true }],
            signedDate: [{ value: new Date(), disabled: true  }],
            transferType: [{ value: "", disabled: true }],
            isDebtRemind: [{ value: true, disabled: true }],

            // thong tin du an
            projectName: [{ value: "", disabled: true }],
            codeProduct: [{ value: "", disabled: true }],

            calcCurrencyFirst: [{ value: false, disabled: true }],
            calcPriceVat: [{ value: false, disabled: true }],
            priceType: [{ value: 'vat', disabled: true }],

            productPrice: [{ value: '', disabled: true }],

            price: [{ value: '', disabled: true }],
            priceVat: [{ value: '', disabled: true }],

            housePrice: [{ value: '', disabled: true }],
            housePriceVat: [{ value: '', disabled: true }],

            contractPrice: [{ value: '', disabled: true }],
            landPrice: [{ value: '', disabled: true }],
            landPriceVat: [{ value: '', disabled: true }],

            maintenanceFeeValue: [{ value: 0, disabled: true }],
            maintenanceFeeType: [{ value: SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY, disabled: true }],
            stagePayment: [{ value: '', disabled: true }],

            policyPayment: [{ value: "", disabled: true }],
            policyDiscounts: [{ value: "", disabled: true }],
            paymentProgress: [{ value: "", disabled: true }],
            currencyUnit: [{ value: "", disabled: true }],

            // dong so huu
            customerCode: [{ value: "", disabled: true }],
            nameOwner: [{ value: "", disabled: true }],
            birthday: [{ value: "", disabled: true }],
            gender: [{ value: "", disabled: true }],
            phone: [{ value: "", disabled: true }],
            email: [{ value: "", disabled: true }],
            identityNumber: [{ value: "", disabled: true }],
            identityIssueDate: [{ value: "", disabled: true }],
            identityIssueLocation: [{ value: "", disabled: true }],

            isShowOwned: false,
            releaseStartDate: [{ value: "", disabled: true }],
            releaseEndDate: [{ value: "", disabled: true }],
        });
        this.filteredCustomer = this.mainForm
            .get("customerCode")
            .valueChanges.pipe(
                debounceTime(300),
                startWith(""),
                switchMap((value: string) => {
                    return this.filterCustomer(value ? value.trim() : "");
                }),
                takeUntil(this.unsubscribe$)
            );

        this.mainForm.get("policyPayment").valueChanges.subscribe((res) => {
            if (res) {
                this.setPaymentProgress();
            } else {
                this.isValidPaymentPolicy = true;
                this.invalidPaymentPolicyMessage = "";
                this.mainForm.patchValue({
                    stagePayment: '',
                    maintenanceFeeType: SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY,
                    maintenanceFeeValue: ''
                });

            }
        });

        this.mainForm.get("policyDiscounts").valueChanges.subscribe((res) => {
            if (res) {
                this.checkValidPolicyDiscounts();
            } else {
                this.isValidDiscountPolicy = true;
                this.invalidDiscountPolicyMessage = "";
            }
        });
        this.mainForm.get("selectedHDC").valueChanges.subscribe((res) => {
            if (this.isGetData) {
                this.ticketCodeChanged.next(res);
            }
            if (!res) {
                this.mainForm.patchValue({
                    nameCustomer: "",
                    projectName: "",
                    isDebtRemind: true,
                    codeProduct: "",
                    productPrice: null,
                });
            }
        });
    }

    enableForm() {
        this.getFormControl("selectedHDC").enable();
        this.getFormControl("isDebtRemind").enable();
        this.getFormControl("signedDate").enable();
        this.getFormControl("policyPayment").enable();
        this.getFormControl("policyDiscounts").enable();
        this.getFormControl("calcCurrencyFirst").enable();
        this.getFormControl("releaseStartDate").enable();
        this.getFormControl("releaseEndDate").enable();
        this.getFormControl("priceType").enable();
        this.getFormControl("releaseStartDate").enable();
        this.getFormControl("releaseEndDate").enable();
        if(this.isCreate || this.mainForm.get('policyPayment').value) {
            this.mainForm.get('stagePayment').enable();
            this.mainForm.get('maintenanceFeeType').enable();
            this.mainForm.get('maintenanceFeeValue').enable();
        }
    }

    setPaymentProgress() {
        let paymentPolicy = this.listPolicyPayment.find(
            (i: any) => i.id == this.mainForm.get("policyPayment").value
        );
        if (paymentPolicy) {
            this.listInstallments = paymentPolicy && paymentPolicy.schedule ? paymentPolicy.schedule.installments : [];
            this.checkValidPaymentProgress(paymentPolicy);
        } else if (this.deposit && this.deposit.policyPayment && this.deposit.policyPayment.id === this.mainForm.get('policyPayment').value) {
            this.isValidPaymentPolicy = true;
            this.invalidPaymentPolicyMessage = '';
        }
        if (paymentPolicy && paymentPolicy.schedule) {
            this.mainForm.patchValue({
                paymentProgress:
                    paymentPolicy && paymentPolicy.schedule
                        ? paymentPolicy.schedule.id
                        : "",
            });
        }
    }

    checkValidPaymentProgress(paymentPolicy: PaymentPolicyModel) {
        if (
            moment(paymentPolicy.startDate) <= moment() &&
            moment() <= moment(paymentPolicy.expiredDate) &&
            paymentPolicy.active
        ) {
            if (!this.isValidPaymentPolicy) {
                this.isValidPaymentPolicy = true;
                this.invalidPaymentPolicyMessage = "";
                this.mainForm
                    .get("policyPayment")
                    .setErrors({ invalidPolicy: false });
                this.mainForm.get("policyPayment").updateValueAndValidity();
            }
            return;
        }
        this.isValidPaymentPolicy = false;
        this.invalidPaymentPolicyMessage =
            "Chính sách thanh toán đã hết hạn sử dụng, vui lòng chọn chính sách khác";
        this.mainForm.get("policyPayment").setErrors({ invalidPolicy: true });
    }
    checkValidPolicyDiscounts() {
        const arr = this.mainForm.get('policyDiscounts').value || [];
        for (let index = 0; index < arr.length; index++) {
            const element = arr[index];
            let discountPolicy = this.listPolicyDiscount.find((i: any) => i.id === element);
            if (!discountPolicy || (this.deposit && this.deposit.policyDiscount && this.deposit.policyDiscount.id === element)) {
            } else {
                if (moment(discountPolicy.startDate) <= moment() && moment() <= moment(discountPolicy.expiredDate) && discountPolicy.active) {
                    if (!this.isValidDiscountPolicy) {
                        this.isValidDiscountPolicy = true;
                        this.invalidDiscountPolicyMessage = '';
                        this.mainForm.get('policyDiscounts').setErrors({invalidPolicy: false});
                        this.mainForm.get('policyDiscounts').updateValueAndValidity();
                    }
                    continue;
                }
                this.mainForm.get('policyDiscounts').setErrors({invalidPolicy: true});
                this.isValidDiscountPolicy = false;
                this.invalidDiscountPolicyMessage = 'Chính sách chiết khấu đã hết hạn sử dụng, vui lòng chọn chính sách khác';
                return;
            }
        }
        this.isValidDiscountPolicy = true;
        this.invalidDiscountPolicyMessage = '';
    }

    patchValue(param, isGetData = false) {
        this.project = param.primaryTransaction.project
        if (isGetData) {
            this.mainForm.patchValue({
                //
                code: param.code,
                name: param.name,
                selectedHDC: param.deposit && param.deposit.name,
            })
        } else {
            this.mainForm.patchValue({
                selectedHDC: param.name,
            })
        }
        this.isGetData = false;
        let policyDiscountIds = (param.policyDiscounts && param.policyDiscounts.length) ? param.policyDiscounts.map(x => x.id) : [];
        this.mainForm.patchValue({
            //
            startDate: param.startDate,
            expiredDate: param.expiredDate,
            signedDate: param.signedDate,
            transferType: param.transferType || "",
            isDebtRemind: param.isDebtRemind,
            //
            projectName:
                param &&
                param.primaryTransaction &&
                param.primaryTransaction.project &&
                param.primaryTransaction.project.name,
            codeProduct:
                param.primaryTransaction &&
                param.primaryTransaction.propertyUnit &&
                param.primaryTransaction.propertyUnit.code,
            productPrice:
                param.primaryTransaction &&
                param.primaryTransaction.propertyUnit &&
                param.primaryTransaction.propertyUnit.price,
            policyPayment:
                (param.policyPayment && param.policyPayment.id) || "",
            policyDiscounts: policyDiscountIds,
            currencyUnit: param.currency,

            calcCurrencyFirst: param.calcCurrencyFirst || false,
            calcPriceVat: param.calcPriceVat || false,
            priceType: this.getPriceType(param.calcPriceVat, param.calcContractPrice),
            contractPrice: param.primaryTransaction.propertyUnit && param.primaryTransaction.propertyUnit.contractPrice || 0,
            price: param.primaryTransaction.propertyUnit && param.primaryTransaction.propertyUnit.price || 0,
            priceVat: param.primaryTransaction.propertyUnit && param.primaryTransaction.propertyUnit.priceVat || 0,

            maintenanceFeeType: param.maintenanceFee && param.maintenanceFee.type && param.maintenanceFee.type || 'currency',
            maintenanceFeeValue: param.maintenanceFee && param.maintenanceFee.value && param.maintenanceFee.value || 0,
            stagePayment: param.maintenanceFee && param.maintenanceFee.stagePayment && param.maintenanceFee.stagePayment || '',

            landPrice: param.primaryTransaction.propertyUnit && param.primaryTransaction.propertyUnit.landPrice || 0,
            landPriceVat: param.primaryTransaction.propertyUnit && param.primaryTransaction.propertyUnit.landPriceVat || 0,

            housePrice: param.primaryTransaction.propertyUnit && param.primaryTransaction.propertyUnit.housePrice || 0,
            housePriceVat: param.primaryTransaction.propertyUnit && param.primaryTransaction.propertyUnit.housePriceVat || 0,
            releaseStartDate: param.releaseStartDate,
            releaseEndDate: param.releaseEndDate,
        });
        if (this.isFromDeposit) {
            this.mainForm.patchValue({
                signedDate: new Date()
            })
        }
        if( 
            (!param.primaryTransaction.propertyUnit.landPrice &&
            !param.primaryTransaction.propertyUnit.landPriceVat && 
            !param.primaryTransaction.propertyUnit.housePrice && 
            !param.primaryTransaction.propertyUnit.housePriceVat) || (
                param.calcContractPrice
            )
        ) {
            this.listCurrentPolicyDiscount =   this.listPolicyDiscount.filter( discount => {
                if(discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.HOUSE && discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.LAND) {
                    return discount
                }
            }) 
        } else {
            this.listCurrentPolicyDiscount =  this.listPolicyDiscount
        }
        // paymentProgress
        this.setPaymentProgress();
        if (
            param &&
            param.primaryTransaction &&
            param.primaryTransaction.customer
        ) {
            let customer = param.primaryTransaction.customer;
            this.mainForm.patchValue({
                nameCustomer:
                    customer.personalInfo && customer.personalInfo.name,
            });
        }
        if (
            param &&
            param.primaryTransaction &&
            param.primaryTransaction.customer2
        ) {
            const customer2 = param.primaryTransaction.customer2;
            this.isShowOwned = true;
            this.mainForm.patchValue({
                customerCode: customer2.code,
                nameOwner: customer2.name,
                birthday: customer2.info.birthday,
                gender: customer2.info.gender,
                phone: customer2.personalInfo.phone,
                email: customer2.personalInfo.email,
                identityNumber:
                    customer2.identities &&
                    customer2.identities[0] &&
                    customer2.identities[0].value,
                identityIssueDate:
                    customer2.identities &&
                    customer2.identities[0] &&
                    customer2.identities[0].date,
                identityIssueLocation:
                    customer2.identities &&
                    customer2.identities[0] &&
                    customer2.identities[0].place,
            });
            this.rootAddress =
                customer2.info && customer2.info.rootAddress
                    ? customer2.info.rootAddress
                    : new Address();
            this.address =
                customer2.info && customer2.info.address
                    ? customer2.info.address
                    : new Address();
            this.formAddress.initForm(this.rootAddress, this.address);
        } else {
            this.isShowOwned = false;
        }
        this.checkPermission(param);
        this.isGetData = true;
        this.uploadedFiles = param.files ? param.files : [];
    }
    checkPermission(contract) {
        if (this.isPermission || (this.user && contract && this.user.id === contract.createdBy)) {
            if (contract.status === ContractEnum.StatusEnum.WAITING && this.isPermission) {
                // đang chờ duyệt, chỉ hiện 2 nút duyệt/ từ chối
                this.rejectPermission = true;
                this.isUpdate = true;
                this.approvePermission = true;
            } else if (contract.status === ContractEnum.StatusEnum.INIT) {
                // khởi tạo, chỉ hiện 2 chỉnh sửa/ trình duyệt
                if(!this.isBtnSave) {
                    this.submitPermission = true;
                    this.isUpdate = true;
                    this.isBtnEdit = true;
                }
            } else if (
                contract.status === ContractEnum.StatusEnum.REJECTED
            ) {
                // bị từ chối, chỉ hiện 2 chỉnh sửa/ trình duyệt
                if(!this.isBtnSave) {
                    this.submitPermission = true;
                    this.isUpdate = true;
                    this.isBtnEdit = true;
                }
            }

            else if (contract.status === ContractEnum.StatusEnum.APPROVED && this.allowEditAfterApproval) {
                this.isUpdate = true;
                this.isBtnEdit = true;
            }

        }
    }

    isApproved() {
        return this.status === ContractEnum.StatusEnum.APPROVED;
    }

    getStatusName() {
        let item = this.listStatus.find(i => i.id === this.deposit.status);
        if (item) return item.name;
        return '';
    }

    onSubmit() {
        const ids = this.getFormControl('policyDiscounts').value;
        if (ids && ids.length) {
            this.contractService.getByDiscountPolicyByIds(ids.join(',')).subscribe(res => {
                const listDiscounts = this.listPolicyDiscount.filter(e => ids.includes(e.id));

                if (listDiscounts.some(e => {
                    const totalContract = res.find(r => r.id === e.id).total;
                    return e.productQuantity <= totalContract;
                })) {
                    this.toastr.error('Chính sách chiết khấu đã vượt quá số lượng sản phẩm áp dụng cho phép');
                    return;
                }
                this.goSubmit();
            });
        } else {
            this.goSubmit();
        }
    }
    goSubmit() {
        let params = {
            id: this.deposit.id,
            status: ContractEnum.StatusEnum.WAITING,
        };
        this.contractService.requestDeposit(params).subscribe((r) => {
            this.toastr.success(
                "Thành công!",
                `Hợp đồng mua bán/ thuê ${this.deposit.code} đã được gửi đến cấp duyệt tiếp theo`
            );
            this.onBack();
        });
    }
    onReject() {
        const rejectConfirm = this.dialog.open(ConfirmPopup, {
            width: "600px",
            data: {
                title: `Xác nhận từ chối ${this.deposit.code}:`,
                isReason: true,
                isRequireReason: true,
                placeholderReason: "Nhập lý do từ chối",
                textCancel: "Hủy bỏ",
                textOk: "Xác nhận",
            },
        });
        rejectConfirm.afterClosed().subscribe((result) => {
            if (typeof result === "undefined") {
                return;
            }
            if (result.execute) {
                let params = {
                    id: this.deposit.id,
                    status: ContractEnum.StatusEnum.REJECTED,
                    reason: result.reason,
                };
                this.contractService.approvePurchase(params).subscribe((res) => {
                    this.toastr.success("Thành công!", "Từ chối hợp đồng mua bán/ thuê");
                    this.onBack();
                });
            }
        });
    }

    getFormControl(name: string) {
        return this.mainForm.get(name);
    }

    isControlInvalid(formControl: string): boolean {
        return (
            this.mainForm.get(formControl).invalid &&
            (this.mainForm.get(formControl).dirty ||
                this.mainForm.get(formControl).touched)
        );
    }
    selectedData(event){
        if(event && event.option && event.option.value) this.getData(event.option.value, false, true, false, false, true);
    }
    getData(id: string, isGetData = false, isCreate = false, isShowProvisionalInterest = false, isShowInfo = false, isReject = false) {
        this.totalTransfered = 0;
        this.productPrice = 0;
        this.contractService.getDepositById(id).subscribe((res) => {
            if (res) {
                if (isGetData) {
                    this.status = res.status;
                }
                this.deposit = res;
                if(this.isBtnSave) {
                    this.deposit.status = this.status;
                }
                this.requireDocument = res.primaryTransaction &&
                    res.primaryTransaction.project && res.primaryTransaction.project.setting &&
                    res.primaryTransaction.project.setting.requireDocument ? res.primaryTransaction.project.setting.requireDocument.includes("depositContract") : false;
                this.patchValue(res, isGetData);
                this.getAllData()
                this.primaryTransactionId = res.primaryTransaction && res.primaryTransaction.id;
                this.listPrintDocuments = _.get(res, 'primaryTransaction.project.setting.templateFileHasStatus', [])
                    .filter(e => e.stage && e.stage.includes(4));
                // Lịch sử thanh toán
                this.transformPaymentHistory();
                // Danh sách phiếu tính lãi
                if (!isShowInfo && !isCreate) {
                    this.transformInterestSlipsData();
                    this.haveProvisionalInterest = res.hasInterest;
                }
                //
                this.transferHistories = res.transferHistories || [];
                if (this.haveProvisionalInterest && isShowProvisionalInterest && ContractEnum.StatusEnum.LIQUIDATED != res.status) {
                    const dialogRef = this.dialog.open(ConfirmPopup, {
                        width: "600px",
                        data: {
                            title: `Hợp đồng ${res.name} đã trễ hạn thanh toán.`,
                            textCancel: 'Đóng',
                            textOk: 'Chi tiết',
                        },
                    });
                    dialogRef.afterClosed().subscribe((result) => {
                        if (typeof result === "undefined") {
                            return;
                        }
                        if (result.execute) {
                            this.showProvisionalInterest();
                        }
                    });
                }
            }
        });
    }

    transformPaymentHistory() {
        if (
            this.deposit &&
            this.deposit.policyPayment &&
            this.deposit.policyPayment.schedule &&
            this.deposit.policyPayment.schedule.installments
        ) {
            this.paymentHistoryData = [];
            this.deposit.policyPayment.schedule.installments.map(
                (item, index) => {
                    // lấy danh sách phiếu thu đã duyệt của 1 đợt
                    let receipts = item.receipts.filter((i) => {
                        return i.status === "TRANSFERED";
                    });
                    if (receipts.length === 0) {
                        // đợt thanh toán không có phiếu thu
                        this.paymentHistoryData.push({
                            stt: index + 1,
                            isToContract: item.isToContract,
                            name: item.name,
                            totalAmount: item.totalAmount,
                            totalTransfered: 0,
                            totalRemaining: item.totalAmount,
                            value:
                                item.type ===
                                SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY
                                    ? "-"
                                    : item.value + "%",
                            code: "-",
                            date: "-",
                            paymentDueDate: item.paymentDueDate ? moment(item.paymentDueDate).format("DD/MM/YYYY") : "",
                            descriptionProgress: item.descriptionProgress
                        });
                    } else if (receipts.length === 1) {
                        // đợt thanh toán có 1 phiếu thu
                        this.paymentHistoryData.push({
                            stt: index + 1,
                            isToContract: item.isToContract,
                            descriptionProgress: item.descriptionProgress,
                            name: item.name,
                            totalAmount: item.totalAmount,
                            totalTransfered: receipts[0].amount,
                            totalRemaining:
                                item.totalAmount - receipts[0].amount,
                            value:
                                item.type ===
                                SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY
                                    ? "-"
                                    : item.value + "%",
                            code: receipts[0].code,
                            date: receipts[0].receiptDate ? moment(receipts[0].receiptDate).format("DD/MM/YYYY") : '',
                            paymentDueDate: item.paymentDueDate ? moment(item.paymentDueDate).format("DD/MM/YYYY") : '',
                        });
                    } else {
                        // đợt thanh toán có nhiều phiếu thu
                        let total = item.totalAmount;
                        receipts.map((r, r_idx) => {
                            this.paymentHistoryData.push({
                                stt: r_idx === 0 ? index + 1 : "",
                                isToContract:
                                    r_idx === 0 ? item.isToContract : false,
                                name: r_idx === 0 ? item.name : "",
                                totalAmount: r_idx === 0 ? item.totalAmount : 0,
                                totalTransfered: r.amount,
                                totalRemaining:
                                    r_idx === receipts.length - 1
                                        ? total - r.amount
                                        : "",
                                value:
                                    r_idx === 0
                                        ? item.type ===
                                          SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY
                                            ? "-"
                                            : item.value + "%"
                                        : "",
                                code: r.code,
                                date: moment(r.receiptDate).format("DD/MM/YYYY"),
                                paymentDueDate: (r_idx === 0 && item.paymentDueDate) ? moment(item.paymentDueDate).format("DD/MM/YYYY") : "",
                                descriptionProgress: item.descriptionProgress
                            });
                            total -= r.amount;
                        });
                    }
                    this.productPrice += item.totalAmount;
                    this.totalTransfered += item.totalTransfered;
                }
            );

            // convert data theo màn hình deposit detail
            let remainMoney = 0;
            this.paymentHistoryData.forEach((e, i) => {

                if(e.totalRemaining < 0 && i < this.paymentHistoryData.length - 1){
                    remainMoney += e.totalRemaining;
                    e.totalRemaining = 0
                }

                if(e.totalRemaining > 0 && remainMoney < 0 && i < this.paymentHistoryData.length - 1) {
                    if (e.totalRemaining + remainMoney > 0) {
                        e.totalRemaining += remainMoney;
                        remainMoney = 0
                    } else {
                        remainMoney += e.totalRemaining;
                        e.totalRemaining = 0
                    }
                }

                if(i == (this.paymentHistoryData.length - 1)){
                    if ((e.totalRemaining + remainMoney) > 0){
                        e.totalRemaining += remainMoney
                    } else {
                        e.totalRemaining = 0
                    }
                };
            });
            this.paymentHistoryData = [...this.paymentHistoryData];
        }
    }

    transformInterestSlipsData() {
        if (this.deposit && this.deposit.interestCalculations) {
            this.interestSlipsData = [];
            this.deposit.interestCalculations
                .reverse()
                .forEach((slip, index) => {
                    this.interestSlipsData.push({
                        stt: index + 1,
                        ...slip,
                    });
                });
            this.interestSlipsData = [...this.interestSlipsData];
        }
    }

    disableForm() {
        this.mainForm.disable();
        this.isDisabledAddress = true;
    }

    changeShow(event) {
        this.isShowOwned = event;
    }

    changeHuman(event) {
        this.mainForm.patchValue({
            gender: event.value,
        });
    }

    private filterCustomer(filterValue: string): Observable<any[]> {
        return this.customerService
            .findByCode(filterValue)
            .pipe(takeUntil(this.unsubscribe$));
    }

    private filterTicketList(filterValue: string) {
        if (filterValue.indexOf("(") > -1) {
            let filterArray = filterValue.split("(");
            if (filterArray && filterArray.length > 0) {
                filterValue = filterArray[0];
            }
        }

        const param = {
            keywords: filterValue.trim(),
            isCheckProject: true,
            type: ContractEnum.ContractType.DEPOSIT,
            page: 1,
            pageSize: 10,
        };
        this.contractService
            .getHDC(param)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe((res) => {
                this.filteredlistHDC = res.rows;
            });
    }

    onBack() {
        this.router.navigate([`${ this.isTransferred ? 'contract/transfer' : 'contract/purchase' }`]);
    }
    onApprove() {
        const body = {
            id: this.id,
            status: ContractEnum.StatusEnum.ACCOUNTANT_WAITING,
        };
        this.contractService.approvePurchase(body).subscribe((res) => {
            if (res && res.status < 400) {
                this.toastr.success("Thành công!", "Duyệt hợp đồng mua bán/ thuê");
                this.onBack();
            }
        });
    }

    onUploadedFilesChange() {
        if (!this.isBtnSave) {
            this.displaySaveDocBtn = true;
        }
    }

    onSaveDocument() {
        const body = {
            id: this.id,
            files: this.uploadedFiles,
        };
        this.contractService.updateFile(body).subscribe((res) => {
            if (res && res.status < 400) {
                this.toastr.success("Thành công!", "Chỉnh sửa hợp đồng mua bán/ thuê");
                this.isBtnSave = false;
                this.displaySaveDocBtn = false;
                this.disableForm();
            } else {
                this.toastr.error("Không thành công!");
            }
        });
    }

    changeTab($event) {
        this.selectedTab = $event;
    }
    onDownLoadPaymentProgress(data) {
        this.isLoading$.next(true)
        const fileName = `${this.deposit.name}_${getCurrentTimeSigned()}`;
        this.contractService
        .historyPayment(this.id, {type: 'HD_COC'}, fileName)
        .subscribe((res) => {
            this.fileUrl = res
            setTimeout( () => {
                const popup = this.dialog.open(PreviewExportDialog, {
                    width: '80%',
                    data: {
                        url: res.url
                    }
                })
                this.isLoading$.next(false);
            }, 3000)
        },
            (error) => {
            this.isLoading$.next(false)
            this.toastr.error("Vui lòng tạo template trong tiến độ thanh toán trước khi dùng chức năng này", "Không thành công! ");
        });
    }

    onSave() {
        const priceType = this.mainForm.get("priceType").value;
        let calcPriceVat = false, calcContractPrice = false;
        if (priceType === 'contract') {
            calcContractPrice = true;
        } else {
            calcPriceVat = priceType === 'vat';
        }
        const startDate = this.mainForm.get("releaseStartDate").value;
        const endDate = this.mainForm.get("releaseEndDate").value;
        if (moment(startDate).isAfter(endDate)) {
            this.toastr.error('Lỗi!', 'Ngày kết thúc phải lớn hơn ngày bắt đầu.');
            return;
        }

        let body = this.deposit;
        const maintenanceFeeValue = this.mainForm.get('maintenanceFeeValue').value + '';
        body = {
            ...body,
            policyScheduleId: this.mainForm.get('paymentProgress').value,
            policyPaymentId: this.mainForm.get('policyPayment').value,
            policyDiscountIds: this.mainForm.get('policyDiscounts').value,
            calcCurrencyFirst: this.mainForm.get('calcCurrencyFirst').value,
            calcPriceVat,
            calcContractPrice,
            maintenanceFee: {
                stagePayment: this.mainForm.get('stagePayment').value,
                type: this.mainForm.get('maintenanceFeeType').value,
                value: maintenanceFeeValue ? parseInt(maintenanceFeeValue.replace(/\,/g, '')) : 0,
            },
            releaseStartDate: this.mainForm.get('releaseStartDate').value,
            releaseEndDate: this.mainForm.get('releaseEndDate').value,
            customer2: this.mainForm.get('nameOwner').value !== '' ? {
                code: this.mainForm.get('customerCode').value,
                name: this.mainForm.get('nameOwner').value,
                gender: this.mainForm.get('gender').value,
                birthday: this.mainForm.get('birthday').value,
                phone: this.mainForm.get('phone').value,
                email: this.mainForm.get('email').value,
                identityNumber: this.mainForm.get('identityNumber').value,
                identityIssueDate: this.mainForm.get('identityIssueDate').value,
                identityIssueLocation: this.mainForm.get('identityIssueLocation').value,
                rootAddress: this.rootAddress,
                address: this.address,
            } : null,
            primaryTransactionId: this.primaryTransactionId,
        };
        if (this.isBtnSave) {
            markAsTouched(this.mainForm);
            if (
                !this.isValidPaymentPolicy ||
                !this.isValidDiscountPolicy ||
                !this.mainForm.valid
            ) {
                this.toastr.error("Vui lòng kiểm tra lại thông tin");
                return;
            }
            body.signedDate = this.mainForm.get("signedDate").value;
            body.isDebtRemind = this.mainForm.get("isDebtRemind").value;
            body.policyPaymentId = this.mainForm.get("policyPayment").value;
        }
        body.files = this.uploadedFiles;
        if(this.requireDocument && this.uploadedFiles.length === 0) {
            this.toastr.error('là bắt buộc', 'Tài liệu đính kèm');
            return;
        }
        if (!this.id) {
            this.contractService.createPurchase(body).subscribe((res: any) => {
                if (res && res.status < 400) {
                    const id = JSON.parse(res._body).id;
                    this.toastr.success("Thành công!", "Tạo hợp đồng mua bán/ thuê");
                    this.router.navigate([`contract/purchase/${id}`]);
                    this.id = id;
                    this.isBtnSave = false;
                    this.disableForm();
                    this.isCreate = false;
                    this.submitPermission = true;
                    this.isBtnEdit = true;
                    this.displaySaveDocBtn = false;
                } else {
                    this.toastr.error("Không thành công!");
                }
            });

            return;
        }
        if (!this.deposit.deposit) {
            body.depositId = this.deposit.id;
            body.depositName = this.deposit.name;
            body.depositCode = this.deposit.code;
        } else {
            body.depositId = this.deposit.deposit.id;
            body.depositName = this.deposit.deposit.name;
            body.depositCode = this.deposit.deposit.code;
        }
        body.id = this.id;
        const updateConfirm = this.dialog.open(ConfirmPopup, {
            width: "600px",
            data: {
                title: `Xác nhận chỉnh sửa hợp đồng`,
                textCancel: 'Hủy bỏ',
                textOk: 'Xác nhận',
                isReason: true
            },
        });
        updateConfirm.afterClosed().subscribe((result) => {
            if (typeof result === "undefined") {
                return;
            }
            if (result.execute) {
                body.id = this.id;
                body.reason = result.reason;
                this.contractService.updatePurchase(body).subscribe((res) => {
                    if (res && res.status < 400) {
                        this.toastr.success("Thành công!", "Chỉnh sửa hợp đồng mua bán/ thuê");
                        this.isBtnSave = false;
                        this.isBtnEdit = true;
                        this.displaySaveDocBtn = false;
                        this.disableForm();
                    } else {
                        this.toastr.error("Không thành công!");
                    }
                });
            }
        })
    }

    onEdit() {
        this.enableForm();
        this.isBtnSave = true;
        this.isBtnEdit = false;
        this.approvePermission = false;
        this.submitPermission = false;
    }

    onSelectCustomer(customer: any) {
        const identity =
            customer.personalInfo &&
            customer.personalInfo.identities &&
            customer.personalInfo.identities.length
                ? customer.personalInfo.identities.find(
                      (item) => item.type === "CMND"
                  )
                : null;
        const bankInfo = customer.bankInfo
            ? { code: customer.bankInfo.type, value: customer.bankInfo.value }
            : null;
        this.mainForm.patchValue({
            nameOwner: customer.personalInfo
                ? customer.personalInfo.name.toUpperCase()
                : "",
            gender: customer.info ? customer.info.gender : "male",
            birthday:
                customer.info &&
                customer.info.birthday &&
                customer.info.birthday !== "//"
                    ? moment(customer.info.birthday).format("DD/MM/YYYY")
                    : null,
            phone: customer.personalInfo ? customer.personalInfo.phone : "",
            email: customer.personalInfo ? customer.personalInfo.email : "",
            identityNumber: identity ? identity.value : null,
            identityIssueDate: identity ? identity.date : null,
            identityIssueLocation: identity ? identity.place : null,
            bankInfo: bankInfo,
        });
        this.rootAddress =
            customer.info && customer.info.rootAddress
                ? customer.info.rootAddress
                : new Address();
        this.address =
            customer.info && customer.info.address
                ? customer.info.address
                : new Address();
        this.formAddress.initForm(this.rootAddress, this.address);
    }

    onChangeRootAddress(data: any) {
        this.rootAddress = data;
    }

    onChangeAddress(data: any) {
        this.address = data;
    }

    setupPermission() {
        this.authorizeService
            .hasAuthority(["PRIMARY", "CONTRACT", "APPROVED"])
            .then((isHasRole) => {
                this.isPermission = isHasRole;
            });
         this.authorizeService.hasAuthority([ 'PRIMARY', 'CONTRACT', 'UPDATE', 'APPROVEMENT'])
            .then(isHasRole => {
                if(this.id) {
                    this.allowEditAfterApproval = isHasRole;
                    this.isBtnEdit = true
                } else {
                    this.allowEditAfterApproval = false
                }
            });
    }

    getUser() {
        this.userV2Service.user$.subscribe((user: CUser) => {
            if (user && user.id) {
                this.user = user;
                this.setupPermission();
                this.checkPermission(this.deposit);
            }
        });
    }

    showProvisionalInterest() {
        const dialogRef = this.dialog.open(
            PopupProvisionalInterestDetailComponent,
            {
                width: "580px",
                panelClass: "overflow-dialog-panel",
                data: this.deposit,
            }
        );
        dialogRef.componentInstance.slipCreated.subscribe(() => {
            dialogRef.close();
            this.tabGroup.selectedIndex = 1;
        });
        dialogRef.afterClosed().subscribe(() => {
            dialogRef.componentInstance.slipCreated.unsubscribe();
        });
    }

    showInterestSlip(slip: any) {
        const cloneDeposit = { ...this.deposit };
        cloneDeposit.interestCalculations = cloneDeposit.interestCalculations.filter(
            (interestCalculation) => {
                return interestCalculation.id === slip.id;
            }
        );
        const dialogRef = this.dialog.open(PopupInterestSlipDetailComponent, {
            width: "580px",
            panelClass: "overflow-dialog-panel",
            data: cloneDeposit,
        });
    }

    deleteInterestSlip(event) {
        const deleteConfirm = this.dialog.open(ConfirmPopup, {
            width: "600px",
            data: {
                title: `Xác nhận xóa phiếu tính lãi ${event.code}?`,
                textCancel: "Hủy bỏ",
                textOk: "Xác nhận",
            },
        });
        deleteConfirm.afterClosed().subscribe((result) => {
            if (typeof result === "undefined") {
                return;
            }
            if (result.execute) {
                this.contractService
                    .deleteInterestSlip({
                        id: this.deposit.id,
                        interestCalculation: {
                            id: event.id,
                        },
                    })
                    .subscribe((response) => {});
            }
        });
    }

    onDownload(data) {
        const fileName = `${this.deposit.name}_${getCurrentTimeSigned()}`;
        if (data && data.stage === 4) {
            this.contractService.confirmPayment(this.id, data, fileName).subscribe((res) => {
            });
        } else {
            this.contractService
                .downloadByIdDeposit(this.id, {type: this.deposit.isTransferred ? 'HD_CHUYEN_NHUONG' : 'HD_MUA_BAN'}, fileName)
                .subscribe((res) => {},
                    (error) => {
                    this.toastr.error("Vui lòng tạo template trong dự án trước khi thực hiện thao tác này", "Không thành công! ");
                });
        }
    }

    isHasCreateLiquidation() {
        return this.status === ContractEnum.StatusEnum.APPROVED;
    }

    onCreateLiquidation() {
        this.router.navigate([`/liquidation/contract/create`], {queryParams: {id: this.id}});
    }

    get contractStatusEnum(): typeof ContractEnum.StatusEnum {
        return ContractEnum.StatusEnum;
    }

    enableFields(){
        this.mainForm.get('stagePayment').enable();
        this.mainForm.get('maintenanceFeeType').enable();
        this.mainForm.get('maintenanceFeeValue').enable();
    }

    changeListDiscount(event) {
        let listFilterDiscount: any = this.listPolicyDiscount
        if(
            !this.mainForm.get('housePrice').value && 
            !this.mainForm.get('housePriceVat').value && 
            !this.mainForm.get('landPrice').value && 
            !this.mainForm.get('landPriceVat').value
        ) {
            listFilterDiscount =   this.listPolicyDiscount.filter( discount => {
                if(discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.HOUSE && discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.LAND) {
                    return discount
                }
            }) 
        }

        if(event === 'contract') {
            this.listCurrentPolicyDiscount = listFilterDiscount.filter( discount => {
                if(discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.HOUSE && discount.discount.typeRealEstate !== DiscountTypeRealEstateEnum.LAND) {
                    return discount
                }
            })
        } else {
            this.listCurrentPolicyDiscount = listFilterDiscount
        }
    }
}
