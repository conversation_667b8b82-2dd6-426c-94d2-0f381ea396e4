//.example-container {
//  position: absolute;
//  top: 0;
//  bottom: 0;
//  left: 0;
//  right: 0;
//}
.app.fixed-header .mat-drawer-content {
  overflow: auto !important;
}
.mat-drawer-container {
  height: auto !important;
}
body {
  height: auto !important;
}
.mat-sidenav-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: unset;
  width: 100%;
  height: 100%;
  .icon-close {
    position: relative;
    width: 20px;
    height: 20px;

    i {
      position: absolute;
      content: "";
      background-color: #ffffff;
      width: 1px;
      height: 23px;
      left: 8px;
      top: 0;
    }

    .icon-left {
      transform: rotate(45deg);
    }

    .icon-right {
      transform: rotate(-45deg);
    }
  }

  mat-sidenav-content {
    min-height: 100vh;
    transform: none !important;
  }
}
 .mat-sidenav-container, .mat-drawer-content {
   background: transparent !important;
 }
.button-close {
  position: absolute;
  top: 8px;
  right: 8px;
  color: transparent;
  padding: 0;
  width: 20px;
  height: 20px;
  min-width: unset;
  z-index: 3;
}
.button-open {
  position: fixed;
  top: 0;
  left: 0;
  width: 50px;
  height: 55px;
  min-width: unset;
  padding: 0;
  z-index: 100;
  .icon-open {
    background: #003b76;
    position: relative;
    width: 50px;
    height: 55px;
    z-index: 1;

    i {
      position: absolute;
      z-index: 1;
      content: '';
      height: 3px;
      background-color: #ffffff;
      left: 10px;

      &:first-child {
        width: 20px;
        top: 21px;
      }

      &:nth-child(2) {
        width: 16px;
        top: 27px;
      }

      &:nth-child(3) {
        width: 20px;
        bottom: 19px;
      }
      }
    }
}
::ng-deep {
  app-quick-link,
  section.su-kien,
  #stringeeChatIframe,
  .stringeeX_chatbox_iframe_wrapper {
    display: none !important;
  }

  .app.indigo-light .mat-drawer-side {
    border-right: none;
  }

  //.opened .tab-container, .opened #wcp-editor-main-buttons {
  //  margin-left: 40px !important;
  //}
}

.eq-link {
  position: fixed;
  bottom: 160px;
  right: 22px;
  z-index: 1;
}

// Loading and Error States
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;

  .loading-text {
    margin-top: 20px;
    font-size: 16px;
    color: #666;
    font-weight: 500;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
  text-align: center;

  .error-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #f44336;
    margin-bottom: 20px;
  }

  .error-title {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }

  .error-message {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: #666;
    max-width: 400px;
    line-height: 1.5;
  }

  button {
    min-width: 120px;
  }
}

// Debug info styles
.debug-info {
  font-family: 'Courier New', monospace;
  z-index: 9999;

  div {
    margin-bottom: 2px;
  }

  // Hide in production
  &:not(.development) {
    display: none !important;
  }
}
