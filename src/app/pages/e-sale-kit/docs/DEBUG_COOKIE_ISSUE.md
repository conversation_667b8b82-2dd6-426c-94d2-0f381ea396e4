# 🐛 Debug Cookie Issue

## 🚨 **Vấn đề hiện tại:**

Method `getAccessTokenFromCookie()` không lấy được token từ cookie.

## 🔍 **Debug Steps đã thêm:**

### **1. Enhanced getAccessTokenFromCookie() method:**

```typescript
private getAccessTokenFromCookie(): string {
    // Debug: Check all cookies
    console.log('All cookies:', document.cookie);
    
    // Try different possible cookie names
    let token = this.cookieService.get('access_token');
    console.log('Token from "access_token":', token);
    
    if (!token) {
        token = this.cookieService.get('authorizationData');
        console.log('Token from "authorizationData":', token);
    }
    
    if (!token) {
        // Try manual parsing
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'access_token' || name === 'authorizationData') {
                token = value;
                console.log(`Token found manually from "${name}":`, token);
                break;
            }
        }
    }
    
    console.log('Final token result:', token ? 'Found' : 'Not found');
    return token;
}
```

### **2. Debug method để test cookie access:**

```typescript
public debugCookieAccess(): void {
    console.log('=== COOKIE DEBUG ===');
    console.log('1. All cookies via document.cookie:', document.cookie);
    
    console.log('2. CookieService methods:');
    console.log('   - cookieService.get("access_token"):', this.cookieService.get('access_token'));
    console.log('   - cookieService.get("authorizationData"):', this.cookieService.get('authorizationData'));
    console.log('   - cookieService.getAll():', this.cookieService.getAll());
    
    console.log('3. Manual cookie parsing:');
    const cookies = document.cookie.split(';');
    cookies.forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name.includes('token') || name.includes('auth') || name.includes('access')) {
            console.log(`   - Found: ${name} = ${value?.substring(0, 20)}...`);
        }
    });
    
    console.log('4. SecurityService method:');
    console.log('   - securityService.getCookieToken():', this.securityService.getCookieToken());
    
    console.log('5. Test getAccessTokenFromCookie():');
    const token = this.getAccessTokenFromCookie();
    console.log('   - Result:', token ? `Found: ${token.substring(0, 20)}...` : 'Not found');
    
    console.log('==================');
}
```

### **3. Enhanced logging:**

```typescript
public checkTokenAndLoadPermissions(): Observable<boolean> {
    console.log('=== DEBUG: checkTokenAndLoadPermissions started ===');
    
    const token = this.getAccessTokenFromCookie();

    if (!token) {
        console.warn('❌ No access_token found in cookie');
        console.log('=== DEBUG: checkTokenAndLoadPermissions failed - no token ===');
        return Observable.of(false);
    }

    console.log('✅ Found access_token in cookie, loading permissions...');
    console.log('Token preview:', token.substring(0, 20) + '...');
    // ...
}
```

## 🧪 **Cách debug:**

### **1. Load trang và xem console:**

1. **Mở trang**: `http://localhost:4400/esalekit/hc04-fpt-3169113062724011`
2. **Mở DevTools Console**
3. **Xem logs tự động:**
   ```
   E-Sale-Kit: Checking token and loading permissions...
   === COOKIE DEBUG ===
   1. All cookies via document.cookie: access_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
   2. CookieService methods:
      - cookieService.get("access_token"): [result]
      - cookieService.get("authorizationData"): [result]
      - cookieService.getAll(): [object]
   3. Manual cookie parsing:
      - Found: access_token = eyJ0eXAiOiJKV1QiLCJhbGci...
   4. SecurityService method:
      - securityService.getCookieToken(): [result]
   5. Test getAccessTokenFromCookie():
      - Result: Found: eyJ0eXAiOiJKV1QiLCJhbGci...
   ==================
   ```

### **2. Manual debug trong console:**

```javascript
// 1. Check cookies manually
document.cookie

// 2. Test UserService debug method
userService.debugCookieAccess()

// 3. Test individual methods
userService.getAccessTokenFromCookie()

// 4. Test CookieService directly
// (Cần access vào component để lấy cookieService)

// 5. Test full flow
userService.checkTokenAndLoadPermissions().subscribe(console.log)
```

### **3. Check cookie format:**

```javascript
// Check cookie structure
document.cookie.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    console.log(`Cookie: ${name} = ${value?.substring(0, 50)}...`);
});
```

## 🔍 **Possible Issues:**

### **1. Cookie name mismatch:**
- Cookie có thể không phải `"access_token"`
- Có thể là `"authorizationData"` hoặc tên khác

### **2. Cookie domain/path issues:**
- Cookie có thể set cho domain khác
- Cookie có thể có path restriction

### **3. CookieService issues:**
- CookieService có thể không hoạt động đúng
- Có thể cần sử dụng `document.cookie` trực tiếp

### **4. Cookie format issues:**
- Cookie có thể bị encode
- Cookie có thể có prefix/suffix

## 📊 **Expected Debug Output:**

### **✅ Success case:**
```
=== COOKIE DEBUG ===
1. All cookies via document.cookie: access_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
2. CookieService methods:
   - cookieService.get("access_token"): eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
   - cookieService.get("authorizationData"): 
   - cookieService.getAll(): {access_token: "eyJ0eXAi..."}
3. Manual cookie parsing:
   - Found: access_token = eyJ0eXAiOiJKV1QiLCJhbGci...
4. SecurityService method:
   - securityService.getCookieToken(): 
5. Test getAccessTokenFromCookie():
   - Result: Found: eyJ0eXAiOiJKV1QiLCJhbGci...
==================
```

### **❌ Problem case:**
```
=== COOKIE DEBUG ===
1. All cookies via document.cookie: access_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
2. CookieService methods:
   - cookieService.get("access_token"): [empty]
   - cookieService.get("authorizationData"): [empty]
   - cookieService.getAll(): {}
3. Manual cookie parsing:
   - Found: access_token = eyJ0eXAiOiJKV1QiLCJhbGci...
4. SecurityService method:
   - securityService.getCookieToken(): [empty]
5. Test getAccessTokenFromCookie():
   - Result: Found: eyJ0eXAiOiJKV1QiLCJhbGci... (từ manual parsing)
==================
```

## 🔧 **Next Steps:**

Dựa trên debug output, chúng ta sẽ biết:

1. **Cookie có tồn tại không** (từ `document.cookie`)
2. **CookieService có hoạt động không** (từ `cookieService.get()`)
3. **Manual parsing có work không** (từ manual parsing)
4. **SecurityService có issue không** (từ `getCookieToken()`)

Sau đó sẽ fix theo kết quả debug.

## 📝 **Commands to run:**

```javascript
// Load trang và xem console tự động
// Hoặc chạy manual:
userService.debugCookieAccess()
userService.checkTokenAndLoadPermissions().subscribe(console.log)
```
