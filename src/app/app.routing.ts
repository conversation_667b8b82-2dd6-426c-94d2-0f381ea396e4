import { Routes, RouterModule, PreloadAllModules } from "@angular/router";
import { ModuleWithProviders } from "@angular/core";

import { PagesComponent } from "./pages/pages.component";
import { NotFoundComponent } from "./pages/errors/not-found/not-found.component";
import { ErrorComponent } from "./pages/errors/error/error.component";
import { AppCustomPreloader } from "./AppCustomPreloader";
import { CommingSoonComponent } from "./pages/errors/coming-soon/coming-soon.component";
import { ForgotPasswordComponent } from "./pages/password/forgot-password/forgot-password.component";
import { ResetPasswordComponent } from "./pages/password/reset-password/reset-password.component";
import { EmployeePOSComponent } from "./pages/employee/employee-pos/employee-pos.component";
import { UserRouteAccessDeactivate } from "./shared/services/common/user.route.access.service";
import { RouterLinkDefault } from "./theme/components/menu/menu";
import { ConfirmChangedEmailComponent } from "./pages/employee/confirm-changed-email/confirm-changed-email.component";
import { SearchEmployeePageComponent } from "./pages/search-employee-page/search-employee-page.component";
import { EmployeePublicComponent } from "./pages/employee-public/employee-public.component";
import { MailerLogComponent } from "./pages/mailer/mailer-log/mailer-log.component";

export const routes: Routes = [
    // {
    //     path: "",
    //     canActivate: [UserRouteAccessDeactivate],
    //     component: PagesComponent,
    //     children: [
    //         { path: "", redirectTo: RouterLinkDefault, pathMatch: "full" },
    //     ],
    // },
    {
        path: "esalekit",
        loadChildren: "./pages/e-sale-kit/e-sale-kit.module#ESaleKitModule",
        data: { breadcrumb: "Sale" },
    },
    { path: "**", component: NotFoundComponent },
];

export const routing: ModuleWithProviders = RouterModule.forRoot(routes, {
    //    preloadingStrategy: PreloadAllModules,  // <- comment this line for activate lazy load
    preloadingStrategy: AppCustomPreloader,
    // useHash: true
});
