<div class="top-row">
    <span class="top-row__title">{{ eventId ? 'CHỈNH SỬA' : 'TẠO MỚI'}} SỰ KIỆN</span>
    <div class="top-row__right-section">
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
                <div class="action-wrapper" *ngIf="eventId">
                    <div class="action-item link" fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="8px" (click)="goGuestReport(eventId)" matTooltip="Báo cáo danh sách tham dự">
                        <mat-icon>comment</mat-icon>
                    </div>
                </div>
                <div class="action-wrapper" *ngIf="eventId">
                    <div class="action-item link" fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="8px" (click)="goAwardReport(eventId)"  matTooltip="Báo cáo giải thưởng">
                        <mat-icon>card_giftcard</mat-icon>
                    </div>
                </div>
                <div class="action-wrapper" *ngIf="eventId">
                    <div class="action-item link" fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="8px" (click)="goInteractionReport(eventId)"  matTooltip="Báo cáo tương tác, bình luận">
                        <mat-icon>favorite</mat-icon>
                    </div>
                </div>
            <app-form-action isBack="true" isSave="true" (back)="onBack()" (save)="onSave()"></app-form-action>
        </div>

    </div>
</div>

<mat-tab-group style="display: block" (selectedTabChange)="onLinkClick($event)">
<mat-tab label="Chỉnh sửa" class="fs-4" >
<ng-container [formGroup]="formGroup" *ngIf="formGroup">
    <div class="app-form-body">
        <div fxFlex="100" fxLayoutGap="17px">
            <div fxFlex="50" fxLayout="column" fxLayoutGap="16px">
                <div>
                    <div class="app-card__header">Thông tin chung</div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Mã sự kiện</span>
                            </div>
                            <div fxFlex="75">
                                <span class="app-label">{{ formGroup.value.code }}</span>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Tên sự kiện<span class="m-red">*</span></span>
                            </div>
                            <div fxFlex="75">
                                <textarea formControlName="name" class="app-textarea" rows="2" placeholder="Nhập tên sự kiện"></textarea>
                            </div>
                        </div>

                        <div class="input-container">
                            <div fxFlex="25">
                                Loại sự kiện
                            </div>
                            <div fxFlex="75">
                                <app-radio-group formControlName="eventType" class="mr-20" (outChange)="onChangeRadio($event)">
                                    <app-radio-button [value]="eventTypeEnum.ONLINE" [isDisabled]="eventId" >Online</app-radio-button>
                                    <app-radio-button [value]="eventTypeEnum.OFFLINE" [isDisabled]="eventId">Offline</app-radio-button>
                                </app-radio-group>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Thời gian bắt đầu</span>
                            </div>
                            <div fxFlex="75">
                                <textarea formControlName="timeStartEvent" class="app-textarea" rows="2" placeholder="Nhập thời gian bắt đầu sự kiện"></textarea>
                            </div>
                        </div>

                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Link tên sự kiện</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="urlEvent" class="app-input" placeholder="Nhập Link tên sự kiện">
                            </div>
                        </div>

                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Nội dung sự kiện</span>
                            </div>
                            <div fxFlex="75">
                                <textarea formControlName="contentEvent" class="app-textarea" rows="2" placeholder="Nhập nội dung sự kiện"></textarea>
                            </div>
                        </div>

                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Nội dung kết thúc sự kiện</span>
                            </div>
                            <div fxFlex="75">
                                <textarea formControlName="contentOffEvent" class="app-textarea" rows="2" placeholder="Nhập nội dung kết thúc sự kiện"></textarea>
                            </div>
                        </div>

                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Dự án áp dụng<span class="m-red">*</span></span>
                            </div>
                            <div fxFlex="75">
                                <app-select formControlName="projectId" [clearable]="false" [items]="projects" bindLabel="name" bindValue="id"
                                    [placeholder]="'Chọn dự án'" (change)="onProjectChange($event)"></app-select>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType && isShowLinkLive">
                            <div fxFlex="25">
                                <span class="app-label">Link Livestream<span class="m-red">*</span></span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="livestreamUrl" class="app-input" placeholder="Nhập Link livestream">
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType && isShowLinkLive">
                            <div fxFlex="25">
                                <span class="app-label">Nguồn live<span class="m-red">*</span></span>
                            </div>
                            <div fxFlex="75">
                                <app-select formControlName="typeLiveStream" placeholder="Nguồn live" [items]="typeLiveStream"
                                    bindValue="id" bindLabel="value" class="w-100">
                                </app-select>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Link đăng kí</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="registerUrl" class="app-input" placeholder="Nhập Link đăng kí">
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Hotline</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="hotline" class="app-input" placeholder="1900277211">
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Số lượng người xem</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="fLive" digitOnly class="app-input"  placeholder="Nhập Số lượng người xem">
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Thời gian bắt đầu</span>
                            </div>
                            <div fxFlex="75">
                                <div class="input-container" fxLayout="column">
                                    <span class="app-label">Thời gian bắt đầu sự kiện<span class="m-red">*</span></span>
                                    <div class="app-form-group pb-20" fxLayoutGap="15px">
                                        <div fxFlex="50">
                                            <div class="app-input-unit">
                                                <input formControlName="displayBannerStartTime" class="app-input" [ngxTimepicker]="startTime"
                                                    [disableClick]="true" [format]="24" placeholder="Chọn giờ" readonly>
                                                <ngx-material-timepicker-toggle [for]="startTime">
                                                    <img src="assets/img/icon/time.svg" ngxMaterialTimepickerToggleIcon width="20px" height="20px">
                                                </ngx-material-timepicker-toggle>
                                                <ngx-material-timepicker #startTime></ngx-material-timepicker>
                                            </div>
                                        </div>
                                        <app-date-picker formControlName="displayBannerStartDate" fxFlex="50"></app-date-picker>
                                    </div>
                                    <span class="app-label">Thời gian kết thúc sự kiện</span>
                                    <div class="app-form-group" fxLayoutGap="15px">
                                        <div fxFlex="50">
                                            <div class="app-input-unit">
                                                <input formControlName="displayBannerEndTime" class="app-input" [ngxTimepicker]="endTime" [disableClick]="true"
                                                    [format]="24" placeholder="Chọn giờ" readonly>
                                                <ngx-material-timepicker-toggle [for]="endTime">
                                                    <img src="assets/img/icon/time.svg" ngxMaterialTimepickerToggleIcon width="20px" height="20px">
                                                </ngx-material-timepicker-toggle>
                                                <ngx-material-timepicker #endTime></ngx-material-timepicker>
                                            </div>
                                        </div>
                                        <app-date-picker formControlName="displayBannerEndDate" fxFlex="50"></app-date-picker>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Thời gian check in hợp lệ -->
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Thời gian check in hợp lệ quay thưởng</span>
                            </div>
                            <div fxFlex="75">
                                <div class="input-container" fxLayout="column">
                                    <span class="app-label">Thời gian bắt đầu</span>
                                    <div class="app-form-group pb-20" fxLayoutGap="15px">
                                        <div fxFlex="50">
                                            <div class="app-input-unit">
                                                <input formControlName="startCheckInTime" class="app-input" [ngxTimepicker]="startTimeCheckIn"
                                                    [disableClick]="true" [format]="24" placeholder="Chọn giờ" readonly>
                                                <ngx-material-timepicker-toggle [for]="startTimeCheckIn">
                                                    <img src="assets/img/icon/time.svg" ngxMaterialTimepickerToggleIcon width="20px" height="20px">
                                                </ngx-material-timepicker-toggle>
                                                <ngx-material-timepicker #startTimeCheckIn></ngx-material-timepicker>
                                            </div>
                                        </div>
                                        <app-date-picker formControlName="startCheckInDate" fxFlex="50"></app-date-picker>
                                    </div>
                                    <span class="app-label">Thời gian kết thúc</span>
                                    <div class="app-form-group" fxLayoutGap="15px">
                                        <div fxFlex="50">
                                            <div class="app-input-unit">
                                                <input formControlName="endCheckInTime" class="app-input" [ngxTimepicker]="endTimeCheckIn" [disableClick]="true"
                                                    [format]="24" placeholder="Chọn giờ" readonly>
                                                <ngx-material-timepicker-toggle [for]="endTimeCheckIn">
                                                    <img src="assets/img/icon/time.svg" ngxMaterialTimepickerToggleIcon width="20px" height="20px">
                                                </ngx-material-timepicker-toggle>
                                                <ngx-material-timepicker #endTimeCheckIn></ngx-material-timepicker>
                                            </div>
                                        </div>
                                        <app-date-picker formControlName="endCheckInDate" fxFlex="50"></app-date-picker>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Địa điểm tổ chức -->
                        <section [hidden]="isOnlineType">
                            <div class="input-container">
                                <div fxLayout="row" fxFlex="25" class="input-label">
                                  <span class="app-label">Địa điểm tổ chức</span>
                                </div>
                                <div fxLayout="row" class="input-value app-form-group">
                                  <app-input-interested-area-app-select #interestedArea fxFlex="100" formControlName="interestedArea"
                                    [isRequired]="true">
                                  </app-input-interested-area-app-select>
                                </div>
                            </div>
                        </section>

                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Hình ảnh đại diện <span class="m-red">*</span></span>
                            </div>
                            <div fxFlex="75">
                                <app-image-uploader fxFlex="40" [(imageUrl)]="imageUrl"></app-image-uploader>
                            </div>
                        </div>
                        <div class="input-container">
                            <div class="w-100">
                                <div fxFlex="25">
                                    <span class="app-label">Hình ảnh Logo Trái</span>
                                </div>
                                <div fxFlex="75">
                                    <app-image-uploader fxFlex="40" [(imageUrl)]="logoLeft"></app-image-uploader>
                                </div>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div class="w-100">
                                <div fxFlex="25">
                                </div>
                                <div fxFlex="75">
                                    <span class="note">Ảnh có kích thước 155px - 55px</span>
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <div class="w-100">
                                <div fxFlex="25">
                                    <span class="app-label">Hình ảnh Logo Phải</span>
                                </div>
                                <div fxFlex="75">
                                    <app-image-uploader fxFlex="40" [(imageUrl)]="logoRight"></app-image-uploader>
                                </div>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div class="w-100">
                                <div fxFlex="25">
                                </div>
                                <div fxFlex="75">
                                    <span class="note">Ảnh có kích thước 155px - 55px</span>
                                </div>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Cho phép bỏ qua đăng nhập</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="skipAuthen"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Ghi chú</span>
                            </div>
                            <div fxFlex="75">
                                <textarea formControlName="note" class="app-textarea" rows="2" placeholder="Nhập nội dung ghi chú"></textarea>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Mô tả sự kiện</span>
                            </div>
                            <div fxFlex="75">
                                <textarea formControlName="eventDescription" class="app-textarea" rows="2" placeholder="Nhập mô tả sự kiện"></textarea>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Kích hoạt sự kiện</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="isActive"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Kết thúc sự kiện</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="isEventOff"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Ẩn bình luận</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="hideChat"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Ẩn reaction</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="hideReaction"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Khóa bình luận</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="lockComment"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Ẩn nút fullscreen</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="disableFullScreen"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Cho phép tạo Yêu cầu tư vấn/Đặt hàng</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="allowRequestDemand"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container" *ngIf="isOnlineType">
                            <div fxFlex="25">
                                <span class="app-label">Cho phép đặt câu hỏi</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="allowRaiseHand"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Đăng ký không bắt buộc email</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="unRequiredEmailRegister"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Số lượng đăng ký tối đa</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="limitRegister" class="app-input" placeholder="Nhập số lượng đăng ký tối đa">
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Danh sách đơn vị bán hàng</span>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="100" fxLayoutGap="8px" fxLayoutAlign="start center">
                                <div fxFlex="90">
                                    <input [(ngModel)]="saleUnitName" [ngModelOptions]="{standalone: true}" class="app-input" placeholder="đơn vị bán hàng">
                                </div>
                                <div fxFlex="10">
                                    <button mat-raised-button color="primary" class="h-28p" (click)="addSaleUnitNames()">
                                        <span>Thêm</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div *ngFor="let saleName of saleUnitNames;">
                            <div class="input-container" fxLayoutGap="8px" fxLayoutAlign="start center">
                                <div fxFlex="90">
                                    <div class="input-value app-form-group">
                                        <span class="sale-unit-name">{{saleName}}</span>
                                    </div>
                                </div>
                                <div fxFlex="10" fxLayoutAlign="center center">
                                    <img src="assets/img/icon/icon-delete.svg" class="w15" (click)="removeSaleUnitNames(saleName)">
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Suất đăng ký</span>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="100" fxLayoutGap="8px" fxLayoutAlign="start center">
                                <div fxFlex="90">
                                    <input [(ngModel)]="timeSlotName" [ngModelOptions]="{standalone: true}" class="app-input" placeholder="Suất đăng ký">
                                </div>
                                <div fxFlex="10">
                                    <button mat-raised-button color="primary" class="h-28p" (click)="addTimeSlotNames()">
                                        <span>Thêm</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div *ngFor="let timeSlot of timeSlotNames;">
                            <div class="input-container" fxLayoutGap="8px" fxLayoutAlign="start center">
                                <div fxFlex="90">
                                    <div class="input-value app-form-group">
                                        <span class="sale-unit-name">{{timeSlot}}</span>
                                    </div>
                                </div>
                                <div fxFlex="10" fxLayoutAlign="center center">
                                    <img src="assets/img/icon/icon-delete.svg" class="w15" (click)="removeTimeSlotNames(timeSlot)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="isOnlineType">
                    <div class="app-card__header" >Gửi thông báo trực tiếp</div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="input-container" fxLayoutGap="8px">
                            <div fxFlex="25">
                                <span class="app-label">Nội dung thông báo<span class="m-red">*</span></span>
                            </div>
                            <div fxFlex="65">
                                <textarea formControlName="notificationContent" class="app-textarea" placeholder="Nhập nội dung thông báo cần gửi"></textarea>
                            </div>
                            <div fxFlex="10">
                                <button mat-raised-button color="primary" class="h-28p" (click)="sendNotification()">
                                    <span>Gửi</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- EMAIl -->
                <div>
                    <div class="app-card__header">Mẫu Email</div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="input-container" fxLayoutGap="8px" style="height: fit-content;">
                            <div fxFlex="25">
                                <span class="app-label">Gửi lại Email</span>
                            </div>
                            <div fxFlex="50">
                                <app-checkbox-button [(ngModel)]="isSendMailAgain" [ngModelOptions]="{standalone: true}">Gửi lại email</app-checkbox-button>
                            </div>
                        </div>
                        <div class="input-container" fxLayoutGap="8px">
                            <div fxFlex="25">
                                <span class="app-label">Tiêu đề mail</span>
                            </div>
                            <div fxFlex="57">
                                <input formControlName="emailTemplateSubject" class="app-input" placeholder="Tiêu đề mail" [maxlength]="Constant.LENGTH_100"/>
                            </div>
                            <div fxFlex="10">
                                <button mat-raised-button color="primary" class="h-28p" (click)="editEmailContent(getFormControl('emailTemplateBody'))">
                                    <span>Nội dung Email</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- SMS -->
                <div>
                    <div class="app-card__header">Mẫu SMS</div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="input-container" fxLayoutGap="8px">
                            <div fxFlex="25">
                                <span class="app-label">BandName</span>
                            </div>
                            <div fxFlex="57">
                                <input formControlName="smsBrandName" class="app-input" placeholder="BandName" [maxlength]="Constant.LENGTH_100"/>
                            </div>
                            <div fxFlex="10">
                                <button mat-raised-button color="primary" class="h-28p" (click)="editSmsContent(getFormControl('smsContent'))">
                                    <span>Nội dung Sms</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="isOnlineType">
                    <div class="app-card__header">
                        <div fxLayout="row" fxLayoutAlign="space-between center">
                            <div>
                                Danh sách yêu cầu tư vấn và đặt hàng
                            </div>
                            <div *ngIf="requestDemands.length">
                                <button (click)="downloadRequestDemandList()" mat-raised-button color="primary">
                                    <div class="app-button-with-icon">
                                        <span>Tải xuống danh sách</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="box-person mt-20" *ngIf="requestDemands.length">
                            <div class="input-container">
                                <div fxFlex="100" fxLayoutGap="8px">
                                    <div fxFlex="20">
                                        <span class="app-label">Tên KH</span>
                                    </div>
                                    <div fxFlex="15">
                                        <span class="app-label">Số điện thoại</span>
                                    </div>
                                    <div fxFlex="20">
                                        <span class="app-label">Email</span>
                                    </div>
                                    <div fxFlex="45">
                                        <span class="app-label">Mô tả nhu cầu</span>
                                    </div>
                                </div>
                            </div>
                            <div class="content-person">
                                <ng-container *ngFor="let item of requestDemands; let i = index">
                                    <div class="input-container">
                                        <div fxFlex="100" fxLayoutGap="8px">
                                            <div fxFlex="20">
                                                <input class="app-input" disabled [value]="item.name">
                                            </div>
                                            <div fxFlex="15">
                                                <input class="app-input" disabled [value]="item.phoneNumber">
                                            </div>
                                            <div fxFlex="20" fxLayoutAlign="center center">
                                                <input class="app-input" disabled [value]="item.email">
                                            </div>
                                            <div fxFlex="45" fxLayoutAlign="center center">
                                                <input class="app-input" disabled [value]="item.requestDemand">
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div fxFlex="50" fxLayout="column" fxLayoutGap="16px">
                <div>
                    <div class="app-card__header">Quay số may mắn theo người tham dự</div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Tên chương trình Quay số</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="priceName" class="app-input" placeholder="VD: Quay số trúng thưởng - Nhận quà lý tưởng">
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Màu chữ</span>
                            </div>
                            <div fxFlex="75">
                                <div class="app-input-unit">
                                    <input formControlName="colorTitle" style="border-right: none" class="app-input"  placeholder="VD: #ffffff" >
                                    <img class="color-picker" src="./assets/img/icon/color-picker.png" [(colorPicker)]="color" (colorPickerChange)="onChangeColorTitle($event)" [cpPosition]="'left'" [cpDialogDisplay]="'popup'" >
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <span class="app-label">Giải thưởng</span>
                        </div>
                        <div fxLayout="column">
                            <div class="input-container" fxFlex="100">
                                <div fxFlex="100" fxLayoutGap="8px" fxLayoutAlign="start center">
                                    <div fxFlex="25">
                                        <input formControlName="prizeName" class="app-input" placeholder="Tên giải thưởng">
                                    </div>
                                    <div fxFlex="30">
                                        <div class="app-input-unit input-currency">
                                            <input formControlName="prizeValue" class="app-input" placeholder="Giá trị giải thưởng">
                                        </div>
                                    </div>
                                    <div fxFlex="15">
                                        <input formControlName="prizeAmount" class="app-input" placeholder="Số lượng giải"
                                            [textMask]="{mask: Constant.numberMask}">
                                    </div>
                                    <div fxFlex="15" class="img-prize">
                                        <app-image-uploader [(imageUrl)]="prizeImage"></app-image-uploader>
                                    </div>
                                    <div fxFlex="10">
                                        <button mat-raised-button color="primary" class="h-28p" (click)="addPrize()">
                                            <span>Thêm giải</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="prizes.controls.length" class="item-add">
                                <div [formGroup]="dynamicFormGroup">
                                    <div *ngFor="let prize of prizes.controls; let i = index; ">
                                        <div class="input-container" [formGroup]="prize" fxLayoutGap="8px" fxLayoutAlign="start center">
                                            <div fxFlex="25">
                                                <div class="input-value app-form-group">
                                                    <input formControlName="priceName" class="app-input">
                                                </div>
                                            </div>
                                            <div fxFlex="30">
                                                <div class="app-input-unit input-currency" style="width: 100%;">
                                                    <input formControlName="prizeValue" class="app-input">
                                                </div>
                                            </div>
                                            <div fxFlex="15">
                                                <div class="input-value app-form-group">
                                                    <input formControlName="amount" class="app-input" [textMask]="{mask: Constant.numberMask}">
                                                </div>
                                            </div>
                                            <div fxFlex="15" class="img-prize">
                                                <app-image-uploader [(imageUrl)]="prizeImages[i]"></app-image-uploader>
                                            </div>
                                            <div fxFlex="10" fxLayoutAlign="center center">
                                                <img src="assets/img/icon/icon-delete.svg" class="w15" (click)="deletePrize(i)">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="mb-15">Ảnh có kích thước xxxpx - xxxpx. Định dạng .jpeg, .png</span>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Màu tên giải thưởng</span>
                            </div>
                            <div fxFlex="75">
                                <div class="app-input-unit">
                                    <input formControlName="colorPrize" style="border-right: none" class="app-input"  placeholder="VD: #ffffff" >
                                    <img class="color-picker" src="./assets/img/icon/color-picker.png" [(colorPicker)]="color" (colorPickerChange)="onChangeColorPrize($event)" [cpPosition]="'left'" [cpDialogDisplay]="'popup'">
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Màu tên người trúng thưởng</span>
                            </div>
                            <div fxFlex="75">
                                <div class="app-input-unit">
                                    <input formControlName="colorWiner" style="border-right: none" class="app-input"  placeholder="VD: #ffffff" >
                                    <img class="color-picker" src="./assets/img/icon/color-picker.png" [(colorPicker)]="color" (colorPickerChange)="onChangeColorWiner($event)" [cpPosition]="'left'" [cpDialogDisplay]="'popup'">
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Cho phép admin xác nhận hộ</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="allowAdminConfirm"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Quay thưởng bằng CMND/CCCD</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="allowIDCardSpin"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Cho phép khách vãng lai quay thưởng</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="allowPassersby"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Ảnh background</span>
                            </div>
                            <div fxFlex="75">
                                <app-image-uploader
                                    fxFlex="50"
                                    [(imageUrl)]="training.prizeBackground"
                                    (imageUrlChange)="onBackgroundChange($event, 'customer')"
                                    [fileExtensions]="['jpeg', 'jpg', 'png']"
                                >
                                </app-image-uploader>
                            </div>
                        </div>
                        <div class="input-container">
                            <div class="w-100">
                                <div fxFlex="25">
                                    <span class="app-label">Hình ảnh banner quay số</span>
                                </div>
                                <div fxFlex="75">
                                    <app-image-uploader fxFlex="40" [(imageUrl)]="prizeBackgroundLive"></app-image-uploader>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="app-card__header">Quay số may mắn theo giao dịch</div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Tên chương trình Quay số</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="transactionPriceName" class="app-input" placeholder="VD: Quay số trúng thưởng - Nhận quà lý tưởng">
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Màu chữ</span>
                            </div>
                            <div fxFlex="75">
                                <div class="app-input-unit">
                                    <input formControlName="colorTitleTransaction" style="border-right: none" class="app-input"  placeholder="VD: #ffffff" >
                                    <img class="color-picker" src="./assets/img/icon/color-picker.png" [(colorPicker)]="color" (colorPickerChange)="onChangeColorTitleTransaction($event)" [cpPosition]="'left'" [cpDialogDisplay]="'popup'" [cpPositionRelativeToArrow]="'true'">
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <span class="app-label">Giải thưởng</span>
                        </div>
                        <div fxLayout="column">
                            <div class="input-container" fxFlex="100">
                                <div fxFlex="100" fxLayoutGap="8px" fxLayoutAlign="start center">
                                    <div fxFlex="25">
                                        <input formControlName="transactionPrizeName" class="app-input" placeholder="Tên giải thưởng">
                                    </div>
                                    <div fxFlex="30">
                                        <div class="app-input-unit input-currency">
                                            <input formControlName="transactionPrizeValue" class="app-input" placeholder="Giá trị giải thưởng">
                                        </div>
                                    </div>
                                    <div fxFlex="15">
                                        <input formControlName="transactionPrizeAmount" class="app-input" placeholder="Số lượng giải"
                                               [textMask]="{mask: Constant.numberMask}">
                                    </div>
                                    <div fxFlex="15" class="img-prize">
                                        <app-image-uploader [(imageUrl)]="transactionPrizeImage"></app-image-uploader>
                                    </div>
                                    <div fxFlex="10">
                                        <button mat-raised-button color="primary" class="h-28p" (click)="addTransactionPrize()">
                                            <span>Thêm giải</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="transactionPrizes.controls.length" class="item-add">
                                <div [formGroup]="dynamicFormGroup">
                                    <div *ngFor="let prize of transactionPrizes.controls; let i = index; ">
                                        <div class="input-container" [formGroup]="prize" fxLayoutGap="8px" fxLayoutAlign="start center">
                                            <div fxFlex="25">
                                                <div class="input-value app-form-group">
                                                    <input formControlName="priceName" class="app-input">
                                                </div>
                                            </div>
                                            <div fxFlex="30">
                                                <div class="app-input-unit input-currency" style="width: 100%;">
                                                    <input formControlName="prizeValue" class="app-input">
                                                </div>
                                            </div>
                                            <div fxFlex="15">
                                                <div class="input-value app-form-group">
                                                    <input formControlName="amount" class="app-input" [textMask]="{mask: Constant.numberMask}">
                                                </div>
                                            </div>
                                            <div fxFlex="15" class="img-prize">
                                                <app-image-uploader [(imageUrl)]="transactionPrizeImages[i]"></app-image-uploader>
                                            </div>
                                            <div fxFlex="10" fxLayoutAlign="center center">
                                                <img src="assets/img/icon/icon-delete.svg" class="w15" (click)="deleteTransactionPrize(i)">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="mb-15">Ảnh có kích thước xxxpx - xxxpx. Định dạng .jpeg, .png</span>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Màu tên giải thưởng</span>
                            </div>
                            <div fxFlex="75">
                                <div class="app-input-unit">
                                    <input formControlName="colorPrizeTransaction" style="border-right: none" class="app-input"  placeholder="VD: #ffffff" >
                                    <img class="color-picker" src="./assets/img/icon/color-picker.png" [(colorPicker)]="color" (colorPickerChange)="onChangeColorPrizeTransaction($event)" [cpPosition]="'left'" [cpDialogDisplay]="'popup'" [cpPositionRelativeToArrow]="'true'">
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Màu tên người trúng thưởng</span>
                            </div>
                            <div fxFlex="75">
                                <div class="app-input-unit">
                                    <input formControlName="colorWinerTransaction" style="border-right: none" class="app-input"  placeholder="VD: #ffffff" >
                                    <img class="color-picker" src="./assets/img/icon/color-picker.png" [(colorPicker)]="color" (colorPickerChange)="onChangeColorWinerTransaction($event)" [cpPosition]="'left'" [cpDialogDisplay]="'popup'" [cpPositionRelativeToArrow]="'true'">
                                </div>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Chương trình bán hàng</span>
                            </div>
                            <div fxFlex="75">
                                <app-select formControlName="salesProgramId" placeholder="Chương trình bán hàng" [items]="salesPrograms"
                                            bindValue="id" bindLabel="name" class="w-100">
                                </app-select>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Trạng thái được quay thưởng</span>
                            </div>
                            <div fxFlex="75">
                                <app-select formControlName="transactionStatuses" [multiple]="true" placeholder="Trạng thái" [items]="transactionStatusList"
                                            bindValue="id" bindLabel="name" class="w-100">
                                </app-select>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Cho phép admin xác nhận hộ</span>
                            </div>
                            <div fxFlex="75">
                                <mat-slide-toggle formControlName="allowAdminConfirmTransaction"></mat-slide-toggle>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Ảnh background</span>
                            </div>
                            <div fxFlex="75">
                                <app-image-uploader
                                    fxFlex="50"
                                    [(imageUrl)]="training.transactionPrizeBackground"
                                    (imageUrlChange)="onBackgroundChange($event, 'transaction')"
                                    [fileExtensions]="['jpeg', 'jpg', 'png']"
                                >
                                </app-image-uploader>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="app-card__header">Danh sách bổ sung quay thưởng giao dịch</div>
                    <div class="app-card__body" fxLayout="column">
                        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="10px">
                            <div>
                                <button mat-raised-button color="primary" (click)="downloadTemplateAdditional()" *userAuthority="['USER','IMPORT', 'FILE']">
                                    <div class="app-button-with-icon">
                                        <span>Tải biểu mẫu</span>
                                    </div>
                                </button>
                                <button mat-raised-button color="primary" (click)="handleClickAdditionalInputFile(fileInputAdditional)" class="ml-20" *userAuthority="['USER','IMPORT', 'FILE']">
                                    <div class="app-button-with-icon">
                                        <span> Tải lên danh sách </span>
                                        <input #fileInputAdditional type="file" value="primary" (change)="importSpinAdditional($event)" accept=".xls,.xlsx" style="display:none;" />
                                    </div>
                                </button>
                            </div>
                            <!-- <div>
                                <button mat-raised-button color="primary" (click)="downloadImportList()" *userAuthority="['USER', 'DOWNLOAD', 'IMPORT', 'FILE']">
                                    <div class="app-button-with-icon">
                                        <span>Tải xuống danh sách</span>
                                    </div>
                                </button>
                            </div> -->
                        </div>
                        <div class="box-person mt-20" *ngIf="listSpinAdditional.length">
                            <div class="input-container">
                                <div fxFlex="100" fxLayoutGap="8px">
                                    <div fxFlex="35">
                                        <span class="app-label">Tên KH</span>
                                    </div>
                                    <div fxFlex="35">
                                        <span class="app-label">Số điện thoại</span>
                                    </div>
                                    <div fxFlex="35">
                                        <span class="app-label">Email</span>
                                    </div>
                                    <!-- <div fxFlex="20" fxLayoutAlign="center center">
                                        <span class="app-label">Checkin</span>
                                    </div>
                                    <div fxFlex="20" fxLayoutAlign="center center">
                                        <span class="app-label">Checkout</span>
                                    </div> -->
                                    <div fxFlex="30">
                                        <span class="app-label"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="content-person">
                                <ng-container *ngFor="let item of listSpinAdditional; let i = index">
                                    <div class="input-container">
                                        <div fxFlex="100" fxLayoutGap="8px">
                                            <div fxFlex="35">
                                                <input class="app-input" disabled [value]="item.name">
                                            </div>
                                            <div fxFlex="35">
                                                <input class="app-input" disabled [value]="item.phoneNumber">
                                            </div>
                                            <div fxFlex="35">
                                                <input class="app-input" disabled [value]="item.email">
                                            </div>
                                            <!-- <div fxFlex="20" fxLayoutAlign="center center">
                                                <a fxLayoutAlign="center center" href="javascript:;" replaceUrl="true" class="button-green" style="cursor: unset" *ngIf="item.isCheckIn">
                                                    <mat-icon>done</mat-icon>
                                                    <span>{{item.checkInDate | date: 'dd/MM/yyyy, hh:mm'}}</span>
                                                </a>
                                                <a fxLayoutAlign="center center" href="javascript:;" replaceUrl="true" class="button-delete" style="cursor: unset" *ngIf="!item.isCheckIn">
                                                    <mat-icon>clear</mat-icon>
                                                </a>
                                            </div> -->
                                            <!-- <div fxFlex="20" fxLayoutAlign="center center">
                                                <a fxLayoutAlign="center center" href="javascript:;" replaceUrl="true" class="button-green" style="cursor: unset" *ngIf="item.checkOutDate">
                                                    <mat-icon>done</mat-icon>
                                                    <span>{{item.checkOutDate | date: 'dd/MM/yyyy, hh:mm'}}</span>
                                                </a>
                                                <a fxLayoutAlign="center center" href="javascript:;" replaceUrl="true" class="button-delete" style="cursor: unset" *ngIf="!item.checkOutDate">
                                                    <mat-icon>clear</mat-icon>
                                                </a>
                                            </div> -->
                                            <div fxFlex="30" fxLayoutAlign="center center">
                                                <img src="assets/img/icon/icon-delete.svg" class="w15" (click)="deleteCustomerAdditional(item , i)">
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="app-card__header">Thành phần tham dự</div>
                    <div class="app-card__body" fxLayout="column">
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Quản trị viên</span>
                            </div>
                            <div fxFlex="75">
                                <input formControlName="listAdminSearch" [matAutocomplete]="autocompleteListAdmin" [maxLength]="Constant.LENGTH_50"
                                    class="app-input" placeholder="Nhập để tìm kiếm">
                                <mat-spinner *ngIf="isLoadingListAdmin$ | async" matSuffix [diameter]="20" color="primary">
                                </mat-spinner>
                                <mat-autocomplete #autocompleteListAdmin="matAutocomplete" (optionSelected)="loadListAdmin($event)">
                                    <mat-option *ngFor="let item of filteredListAdmin" [value]="item">
                                        <div fxLayout="row" class="small-text">
                                            <div fxLayout="column" fxLayoutAlign="center center">
                                                <mat-icon color="primary">contact_mail</mat-icon>
                                            </div>
                                            <div fxLayout="column" fxLayoutAlign="center start">{{item.name}}</div>
                                        </div>
                                    </mat-option>
                                </mat-autocomplete>
                                <mat-list *ngIf="listAdmin.length" role="list" class="list-member">
                                    <mat-list-item role="listitem" *ngFor="let admin of listAdmin | memberFilter: formGroup.controls.listAdminSearch.value">
                                        <div fxLayout="row" fxFlex="100" fxLayoutAlign="start center">
                                            <span fxFlex="100">{{admin.name}}</span>
                                            <button mat-icon-button color="warn" (click)="handleListAdmin(admin, true)" [disabled]="isEdit">
                                                <mat-icon>close</mat-icon>
                                            </button>
                                        </div>
                                    </mat-list-item>
                                </mat-list>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Tham dự theo</span>
                            </div>
                            <div fxFlex="75">
                                <app-checkbox-button fxFlex="30" [(ngModel)]="isPos" [ngModelOptions]="{standalone: true}" (change)="onChangeCheckbox(true)">
                                    <span>Sàn</span>
                                </app-checkbox-button>
                                <app-checkbox-button fxFlex="30" [(ngModel)]="isPerson" [ngModelOptions]="{standalone: true}"
                                    (change)="onChangeCheckbox(false)">
                                    <span>Cá nhân / NVKD</span>
                                </app-checkbox-button>
                            </div>
                        </div>
                        <!-- thêm field cho phép đơn vị bán hàng đăng ký khách hàng -->
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Cho phép ĐVBH đăng ký khách hàng</span>
                            </div>
                            <div fxFlex="75">
                                <!-- <mat-slide-toggle formControlName="allowRegisterCustomer"></mat-slide-toggle> -->
                                <app-checkbox-button fxFlex="30" formControlName="allowRegisterCustomer">
                                </app-checkbox-button>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Đơn vị / NVKD tham dự</span>
                            </div>
                            <div fxFlex="75" fxLayoutGap="15px">
                                <div fxFlex="50" *ngIf="isPos">
                                    <app-select formControlName="pos" [clearable]="false" [items]="listPos" bindLabel="name" bindValue="id"
                                        [placeholder]="'Chọn đơn vị'"></app-select>
                                </div>
                                <div fxFlex="50" *ngIf="isPerson">
                                    <div class="app-input-unit">
                                        <input class="app-input" placeholder="Nhập Mã Khách Hàng" formControlName="person" [matAutocomplete]="auto">
                                    </div>
                                    <mat-autocomplete #auto="matAutocomplete" autoActiveFirstOption>
                                        <mat-option *ngFor="let item of listPerson | async" [value]="item.id" (click)="onSelectPerson(item)">
                                            <span>{{item.name}}</span>
                                        </mat-option>
                                    </mat-autocomplete>
                                </div>
                                <button fxFlex="20" fxLayoutAlign="center center" mat-stroked-button color="primary" class="h-28p"
                                    (click)="addEmployee()">Thêm</button>
                            </div>
                        </div>
                        <div class="input-container">
                            <div fxFlex="25">
                                <span class="app-label">Tổng đơn vị / NVKD được chọn</span>
                            </div>
                            <div fxFlex="75" fxLayoutGap="15px">
                                <span class="t-label">{{getTotalPos()}} Đơn vị</span>
                                <span class="t-label"> {{listItemEmployee.length}} NVKD</span>
                            </div>
                        </div>
                        <div class="box-person">
                            <div class="input-container">
                                <div fxFlex="100" fxLayoutGap="8px">
                                    <div fxFlex="25">
                                        <span class="app-label">Mã nhân viên</span>
                                    </div>
                                    <div fxFlex="70">
                                        <span class="app-label">Họ tên</span>
                                    </div>
                                </div>
                            </div>
                            <div class="content-person">
                                <ng-container *ngFor="let item of listItemEmployee; let i = index">
                                    <div class="input-container item-first" *ngIf="item.isFirst">
                                        <div fxFlex="100">
                                            <div fxFlex="97">
                                                <span class="app-label">{{item.posName}}</span>
                                            </div>
                                            <div fxFlex="3" (click)="setShow(item)" style="cursor: pointer;">
                                                <img [src]="getShow(item) ? './assets/img/dxres/collapse.svg' : './assets/img/dxres/uncollapse.svg'" alt="">
                                            </div>
                                        </div>
                                    </div>
                                    <ng-container *ngIf="item.show" [@expandCollapse]="getShow(item)">
                                        <div class="input-container">
                                            <div fxFlex="100" fxLayoutGap="8px">
                                                <div fxFlex="25">
                                                    <input class="app-input" disabled [value]="item.code">
                                                </div>
                                                <div fxFlex="70">
                                                    <input class="app-input" disabled [value]="item.name">
                                                </div>
                                                <div fxFlex="5" fxLayoutAlign="center center">
                                                    <img src="assets/img/icon/icon-delete.svg" class="w15" (click)="onRemoveEmployee(item)">
                                                </div>
                                            </div>
                                        </div>
                                    </ng-container>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-container>
</mat-tab>

<mat-tab label="Danh sách khách mời" class="fs-4" >
    <div class="app-card__body" fxLayout="column">
        <div
            fxLayout="row"
            fxLayoutAlign="space-between center"
            fxLayoutGap="10px"
            *ngIf="currentTabIndex === 2"
        >
            <filter isQuery="true" (apply)="onApply($event)" isButtonSearchHidden="true" isButtonSetupHidden="true" fxLayout="column" fxFlex="50">
            </filter>
                <div>
                <button
                    mat-raised-button
                    color="primary"
                    (click)="reSendEmail()"
                    class="ml-20"
                    [disabled]="!selectedToSendEmail.length"
                >
                    <div class="app-button-with-icon">
                        <span>Gửi lại email</span>
                    </div>
                </button>
                <button
                    mat-raised-button
                    color="primary"
                    (click)="downloadTemplate()"
                    class="ml-20"
                    *userAuthority="['USER', 'IMPORT', 'FILE']"
                >
                    <div class="app-button-with-icon">
                        <span>Tải biểu mẫu</span>
                    </div>
                </button>
                <button
                    mat-raised-button
                    color="primary"
                    (click)="handleClickInputFile(fileInput)"
                    class="ml-20"
                    *userAuthority="['USER', 'IMPORT', 'FILE']"
                >
                    <div class="app-button-with-icon">
                        <span> Tải lên danh sách </span>
                        <input
                            #fileInput
                            type="file"
                            value="primary"
                            (change)="import($event)"
                            accept=".xls,.xlsx"
                            style="display: none"
                        />
                    </div>
                </button>
                <button
                    mat-raised-button
                    color="primary"
                    (click)="downloadImportList()"
                    class="ml-20"
                    *userAuthority="['USER', 'DOWNLOAD', 'IMPORT', 'FILE']"
                >
                    <div class="app-button-with-icon">
                        <span>Tải xuống danh sách</span>
                    </div>
                </button>
            </div>
        </div>

        <div class="box-person mt-20">
            <ag-grid-angular
                id="grid-indicator"
                class="ag-theme-balham"
                [gridOptions]="gridOptions"
                [columnDefs]="columnDefs"
                [rowData]="rowData"
                (gridReady)="onGridReady1($event)"
            ></ag-grid-angular>

            <ng-template #sendEmailHeader>
                <div fxLayoutGap="5px">
                    <mat-checkbox [(ngModel)]="isSelectAllForEmail" (change)="toggleSelectAllForEmail($event)"></mat-checkbox>
                </div>
            </ng-template>

            <ng-template #sendEmailRow let-row>
                <div fxLayoutGap="5px">
                    <mat-checkbox [(ngModel)]="row.selectedForEmail" (change)="toggleSelectForEmail(row, $event)"></mat-checkbox>
                </div>
            </ng-template>

            <ng-template #statusCheckIn let-row>
                <div
                    *ngIf="row.isCheckIn"
                    fxLayout="row"
                    fxFlex="20"
                    fxLayoutAlign="start center"
                    class="approve-item"
                >
                    <mat-icon class="success">done</mat-icon>
                    <span class="success">{{row.checkInDate | date: 'dd/MM/yyyy, hh:mm:ss'}}</span>
                </div>
                <div
                    *ngIf="!row.isCheckIn"
                    fxLayout="row"
                    fxFlex="20"
                    fxLayoutAlign="start center"
                    class="reject-item"
                >
                    <mat-icon class="danger">clear</mat-icon>
                </div>
            </ng-template>

            <ng-template #statusCheckOut let-row>
                <div
                    *ngIf="row.checkOutDate"
                    fxLayout="row"
                    fxFlex="20"
                    fxLayoutAlign="start center"
                    class="approve-item"
                >
                    <mat-icon class="success">done</mat-icon>
                    <span class="success">{{row.checkOutDate | date: 'dd/MM/yyyy, hh:mm'}}</span>
                </div>
                <div
                    *ngIf="!row.checkOutDate"
                    fxLayout="row"
                    fxFlex="20"
                    fxLayoutAlign="start center"
                    class="reject-item"
                >
                    <mat-icon class="danger">clear</mat-icon>
                </div>
            </ng-template>
            <ng-template #remove let-row>
                <img src="assets/img/icon/trash.svg" class="w15" (click)="onRemoveUser(row)" >
            </ng-template>
            <ng-template #modifiedDate let-row>
                <span>{{row.modifiedDate | date: 'dd/MM/yyyy, hh:mm'}}</span>
            </ng-template>
        </div>
    </div>
    <app-paging [data]="usersPagination" (dataChange)="onChangePaging($event)"></app-paging>
</mat-tab>
</mat-tab-group>
