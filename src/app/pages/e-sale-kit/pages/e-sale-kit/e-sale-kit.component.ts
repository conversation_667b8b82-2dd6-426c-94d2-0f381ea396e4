import {ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {MatDrawer} from '@angular/material';
import { SidenavService } from '../../services/sidenav.service';
import { Unsubscriber } from 'app/shared/models/unsubscriber';
import { ESaleKitService } from '../../services/e-sale-kit.service';
import { UserService } from 'app/shared/services/common/user.service';

@Component({
  selector: 'app-e-sale-kit',
  templateUrl: './e-sale-kit.component.html',
  styleUrls: ['./e-sale-kit.component.scss'],
  providers: [SidenavService, Unsubscriber],
})
export class ESaleKitComponent implements OnInit, OnDestroy {
  opened: boolean = false;
  projectId: string = '';
  isAuthenticated: boolean = false;
  urlEsalekit: string;
  // @ViewChild('sidenav', {static: true}) sidenav: MatDrawer;
  @ViewChild('sidenav') sidenav: MatDrawer;

  constructor(
    private activeRouter: ActivatedRoute,
    private sidenavService: SidenavService,
    private unsubscriber: Unsubscriber,
    private _changeDetectorRef: ChangeDetectorRef,
    private eSaleKitService: ESaleKitService,
    private userService: UserService
  ) {
  }

  ngOnInit() {
    // Check token và load permissions trước khi load component
    this.checkTokenAndLoadPermissions();

    this.unsubscriber.safeSubscriptions(
      this.sidenavService.opened$.subscribe(val => {
        this.opened = val;
        this._changeDetectorRef.detectChanges();
      }),
    );
    const params = this.activeRouter.snapshot.params;
    this.urlEsalekit = params['id'];
    this.getProject(this.urlEsalekit);
  }

  /**
   * Check access_token trong cookie và load permissions
   */
  private checkTokenAndLoadPermissions(): void {
    console.log('E-Sale-Kit: Checking token and loading permissions...');

    this.userService.checkTokenAndLoadPermissions().subscribe(
      (success) => {
        if (success) {
          console.log('✅ E-Sale-Kit: Permissions loaded successfully');
          this.isAuthenticated = true;
        } else {
          console.log('❌ E-Sale-Kit: Failed to load permissions');
          this.isAuthenticated = false;
          // Có thể redirect về login hoặc hiển thị thông báo
        }
      },
      (error) => {
        console.error('E-Sale-Kit: Error loading permissions:', error);
        this.isAuthenticated = false;
      }
    );
  }

  ngOnDestroy(): void {
    this.unsubscriber.unsubscribeAll();
  }

  toogleSidenav() {
    this.sidenav.toggle();
  }

  getProject(esalekit: string) {
    this.eSaleKitService.getProjectDetail(esalekit).subscribe(res => {
      this.projectId = res.id;
    });
  }
}
