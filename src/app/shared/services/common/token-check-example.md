# 🔑 Token Check & Load Permissions - Usage Guide

## 📋 **New Methods Added**

Đã thêm 3 methods mới vào `UserService`:

### **1. `checkTokenAndLoadPermissions()`**
- ✅ Kiểm tra access_token trong cookie
- ✅ Nếu có token → gọi API lấy permissions
- ✅ Convert và lưu permissions vào sessionStorage
- ✅ Return Observable<boolean>

### **2. `isUserAuthenticated()`**
- ✅ Kiểm tra user đã authenticate chưa
- ✅ Check cả token và permissions
- ✅ Return boolean

### **3. `initializeFromExistingToken()`**
- ✅ Khởi tạo app từ token có sẵn
- ✅ Skip API call nếu đã có permissions
- ✅ Dùng khi app startup

## 🚀 **Cách sử dụng:**

### **1. Kiểm tra token và load permissions:**

```typescript
// Trong component hoặc service
this.userService.checkTokenAndLoadPermissions().subscribe(
  (success) => {
    if (success) {
      console.log('✅ Permissions loaded successfully');
      // Redirect to main app
      this.router.navigate(['/dashboard']);
    } else {
      console.log('❌ No token or failed to load permissions');
      // Redirect to login
      this.router.navigate(['/login']);
    }
  }
);
```

### **2. Kiểm tra trạng thái authentication:**

```typescript
// Check nhanh user đã authenticate chưa
if (this.userService.isUserAuthenticated()) {
  console.log('User is already authenticated');
  // Continue with app
} else {
  console.log('User needs to authenticate');
  // Show login or check token
}
```

### **3. Khởi tạo app từ token có sẵn:**

```typescript
// Trong app.component.ts hoặc app initialization
this.userService.initializeFromExistingToken().subscribe(
  (initialized) => {
    if (initialized) {
      console.log('App initialized from existing token');
      this.appReady = true;
    } else {
      console.log('Need to login');
      this.router.navigate(['/login']);
    }
  }
);
```

## 🔄 **Luồng hoạt động:**

### **Scenario 1: User có token hợp lệ**
```
App Start → checkTokenAndLoadPermissions()
    ↓
Found token in cookie → Call API with Bearer token
    ↓
API Success → Convert permissions → Save to sessionStorage
    ↓
Return true → App ready to use
```

### **Scenario 2: User không có token**
```
App Start → checkTokenAndLoadPermissions()
    ↓
No token in cookie → Return false
    ↓
Redirect to login page
```

### **Scenario 3: Token invalid**
```
App Start → checkTokenAndLoadPermissions()
    ↓
Found token → Call API → 401/403 Error
    ↓
Log warning → Return false → Redirect to login
```

## 🧪 **Test Commands:**

### **1. Test trong browser console:**

```javascript
// Check có token không
userService.isUserAuthenticated()

// Force check token và load permissions
userService.checkTokenAndLoadPermissions().subscribe(
  result => console.log('Result:', result)
);

// Check permissions sau khi load
sessionStorage.getItem('permissions')

// Check AuthorizeService
authorizeService.checkAuthorities()
```

### **2. Test manual:**

```javascript
// 1. Clear permissions để test
sessionStorage.removeItem('permissions');

// 2. Check token vẫn còn
document.cookie

// 3. Load permissions từ token
userService.checkTokenAndLoadPermissions().subscribe(console.log);

// 4. Verify permissions loaded
JSON.parse(sessionStorage.getItem('permissions'))
```

## 📱 **Integration Examples:**

### **A. App Component (App Startup):**

```typescript
// app.component.ts
export class AppComponent implements OnInit {
  appReady = false;
  
  ngOnInit() {
    // Khởi tạo app từ token có sẵn
    this.userService.initializeFromExistingToken().subscribe(
      (initialized) => {
        this.appReady = initialized;
        if (!initialized) {
          this.router.navigate(['/login']);
        }
      }
    );
  }
}
```

### **B. Route Guard:**

```typescript
// auth.guard.ts
canActivate(): Observable<boolean> {
  if (this.userService.isUserAuthenticated()) {
    return of(true);
  }
  
  return this.userService.checkTokenAndLoadPermissions().pipe(
    tap(success => {
      if (!success) {
        this.router.navigate(['/login']);
      }
    })
  );
}
```

### **C. Login Component:**

```typescript
// login.component.ts
ngOnInit() {
  // Check có token sẵn không
  if (this.userService.isUserAuthenticated()) {
    this.router.navigate(['/dashboard']);
    return;
  }
  
  // Thử load từ token có sẵn
  this.userService.checkTokenAndLoadPermissions().subscribe(
    (success) => {
      if (success) {
        this.router.navigate(['/dashboard']);
      } else {
        // Show login form
        this.showLoginForm = true;
      }
    }
  );
}
```

## 🔍 **Debug Info:**

### **Console Logs sẽ hiển thị:**

```
✅ Success case:
"Found access_token in cookie, loading permissions..."
"New API response: {roles: [...]}"
"Converted permissions: [{featureName: '...', action: {...}}]"
"Permissions loaded successfully from existing token"

❌ No token case:
"No access_token found in cookie"

❌ Invalid token case:
"Found access_token in cookie, loading permissions..."
"Error loading permissions with existing token: 401"
"Token appears to be invalid"
```

### **SessionStorage sẽ chứa:**

```javascript
// Key: "permissions"
{
  "property": {
    "unit": {
      "import": { "isAuthor": true }
    }
  },
  "esalekit": {
    "edit": { "isAuthor": true }
  }
}

// Key: "isFullPermission" 
"true" // hoặc ""
```

## 📝 **Notes:**

- **Non-blocking**: Không ảnh hưởng đến flow hiện tại
- **Error handling**: Graceful fallback khi API fail
- **Performance**: Skip API call nếu đã có permissions
- **Security**: Validate token qua API call thực tế
- **Flexible**: Có thể dùng ở nhiều nơi khác nhau
