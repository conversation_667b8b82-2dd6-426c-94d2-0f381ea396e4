import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ESaleKitLoginService } from '../services/e-sale-kit-login.service';
import { AuthApiService } from '../../../shared/services/auth/auth-api.service';

@Component({
  selector: 'app-esalekit-login-example',
  template: `
    <div class="login-container">
      <mat-card class="login-card">
        <mat-card-header>
          <mat-card-title>Đăng nhập E-Sale-Kit</mat-card-title>
          <mat-card-subtitle>Sử dụng tài khoản CRM để truy cập</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Tên đăng nhập</mat-label>
              <input matInput 
                     formControlName="username" 
                     placeholder="Nhập tên đăng nhập"
                     [class.is-invalid]="loginForm.get('username')?.invalid && loginForm.get('username')?.touched">
              <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
                Tên đăng nhập là bắt buộc
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Mật khẩu</mat-label>
              <input matInput 
                     type="password" 
                     formControlName="password" 
                     placeholder="Nhập mật khẩu"
                     [class.is-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
              <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                Mật khẩu là bắt buộc
              </mat-error>
              <mat-error *ngIf="loginForm.get('password')?.hasError('minlength')">
                Mật khẩu phải có ít nhất 6 ký tự
              </mat-error>
            </mat-form-field>

            <mat-checkbox formControlName="rememberMe" class="remember-me">
              Ghi nhớ đăng nhập
            </mat-checkbox>

            <div class="login-actions">
              <button mat-raised-button 
                      color="primary" 
                      type="submit" 
                      [disabled]="loginForm.invalid || isLoading"
                      class="login-button">
                <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
                <span *ngIf="!isLoading">Đăng nhập</span>
                <span *ngIf="isLoading">Đang đăng nhập...</span>
              </button>
            </div>

            <div class="error-message" *ngIf="errorMessage">
              <mat-icon>error</mat-icon>
              <span>{{ errorMessage }}</span>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Debug info -->
      <mat-card class="debug-card" *ngIf="showDebugInfo">
        <mat-card-header>
          <mat-card-title>Debug Info</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div><strong>Token:</strong> {{ currentToken || 'Không có' }}</div>
          <div><strong>User:</strong> {{ currentUser?.username || 'Chưa đăng nhập' }}</div>
          <div><strong>Authorities:</strong> {{ currentAuthorities?.join(', ') || 'Không có' }}</div>
          <div><strong>Can View ESaleKit:</strong> {{ canViewESaleKit }}</div>
          <div><strong>Can Edit ESaleKit:</strong> {{ canEditESaleKit }}</div>
          
          <div class="debug-actions">
            <button mat-button (click)="refreshUserData()">Refresh User Data</button>
            <button mat-button (click)="clearToken()">Clear Token</button>
            <button mat-button (click)="toggleDebugInfo()">Hide Debug</button>
          </div>
        </mat-card-content>
      </mat-card>

      <button mat-fab 
              class="debug-toggle" 
              (click)="toggleDebugInfo()" 
              *ngIf="!showDebugInfo">
        <mat-icon>bug_report</mat-icon>
      </button>
    </div>
  `,
  styles: [`
    .login-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background-color: #f5f5f5;
      padding: 20px;
    }

    .login-card {
      width: 100%;
      max-width: 400px;
      margin-bottom: 20px;
    }

    .debug-card {
      width: 100%;
      max-width: 600px;
      background-color: #263238;
      color: white;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .remember-me {
      margin-bottom: 20px;
    }

    .login-actions {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
    }

    .login-button {
      width: 100%;
      height: 48px;
    }

    .error-message {
      display: flex;
      align-items: center;
      color: #f44336;
      font-size: 14px;
      margin-top: 8px;
    }

    .error-message mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }

    .debug-actions {
      margin-top: 16px;
    }

    .debug-actions button {
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .debug-toggle {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #ff9800;
    }

    .is-invalid {
      border-color: #f44336 !important;
    }
  `]
})
export class ESaleKitLoginExampleComponent implements OnInit, OnDestroy {
  loginForm: FormGroup;
  isLoading = false;
  errorMessage = '';
  showDebugInfo = false;
  
  currentToken: string | null = null;
  currentUser: any = null;
  currentAuthorities: string[] = [];
  canViewESaleKit = false;
  canEditESaleKit = false;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private loginService: ESaleKitLoginService,
    private authApiService: AuthApiService
  ) {
    this.createForm();
  }

  ngOnInit(): void {
    this.updateDebugInfo();
    
    // Lắng nghe thay đổi user data
    this.authApiService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUser = user;
        this.updateDebugInfo();
      });

    // Lắng nghe thay đổi authorities
    this.authApiService.authorities$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authorities => {
        this.currentAuthorities = authorities;
        this.updateDebugInfo();
      });

    // Lắng nghe trạng thái loading
    this.loginService.isLoggingIn$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isLoading => {
        this.isLoading = isLoading;
      });

    // Auto login nếu có token
    this.loginService.autoLogin().subscribe(success => {
      if (success) {
        console.log('Auto login successful');
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): void {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.errorMessage = '';
      
      this.loginService.login(this.loginForm.value).subscribe({
        next: (response) => {
          if (response.success) {
            console.log('Login successful:', response);
            this.loginService.redirectAfterLogin();
          } else {
            this.errorMessage = response.message || 'Đăng nhập thất bại';
          }
        },
        error: (error) => {
          console.error('Login error:', error);
          this.errorMessage = error.error?.message || 'Có lỗi xảy ra khi đăng nhập';
        }
      });
    }
  }

  refreshUserData(): void {
    this.authApiService.refreshUserData().subscribe({
      next: () => {
        console.log('User data refreshed');
        this.updateDebugInfo();
      },
      error: (error) => {
        console.error('Error refreshing user data:', error);
      }
    });
  }

  clearToken(): void {
    this.loginService.logout();
    this.updateDebugInfo();
  }

  toggleDebugInfo(): void {
    this.showDebugInfo = !this.showDebugInfo;
  }

  private updateDebugInfo(): void {
    this.currentToken = this.authApiService.getTokenFromCookie();
    this.currentUser = this.authApiService.getCurrentUser();
    this.currentAuthorities = this.authApiService.getCurrentAuthorities();
    this.canViewESaleKit = this.loginService.canAccessESaleKit();
    this.canEditESaleKit = this.loginService.canEditESaleKit();
  }
}
