import { Component, Input, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, SimpleChanges, AfterViewInit, OnChanges } from '@angular/core';

import { ActivatedRoute, Router } from '@angular/router';
import { DialogContentService } from '../../services/dialog-content.service';
import { SubmenuService } from '../../services/submenu.service';
import { HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {MatDialog} from "@angular/material/dialog";
import { Unsubscriber } from 'app/shared/models/unsubscriber';
import { Menu, MenuPopupData, SubMenu } from '../../models/models';
import { DEFAULT_MENU_CONFIG, EDIT_PERMISSION, EDIT_PERMISSION_OVERVIEW, EDIT_PERMISSION_PRODUCT, EDIT_PERMISSION_TERM, EDIT_PERMISSION_UTILITY, Menus } from '../../constants/constants';
import { UploadFileService } from 'app/shared/services/common/upload-file.service';
import { AlertMessageService } from 'app/shared/services/alert-message.service';
import { MenuPopupComponent } from '../menu-popup/menu-popup.component';
import { environment } from 'environments/environment';
import { CDocument } from 'app/api-models';
import { AuthorizeService } from 'app/shared/authenticate';
import { ESaleKitAuthService } from '../../services/e-sale-kit-auth.service';

@Component({
  selector: 'app-e-menu',
  templateUrl: './e-menu.component.html',
  styleUrls: ['./e-menu.component.scss'],
  providers: [DialogContentService, Unsubscriber],
})
export class EMenuComponent implements OnInit, OnDestroy, OnChanges {

  public isLoading$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  @Input() projectId: string;
  @Input() urlEsalekit: string;
  menus: Menu[];
  subMenus: SubMenu[] = [];
  selectMenuId: string;
  showItem: boolean = false;
  config = DEFAULT_MENU_CONFIG;
  isOpenPopup: boolean = false;
  selectSubMenu: any;
  menuId: string;
  urlLogo: string = '';
  projectName: string = '';
  dataProject: any = [];
  editPermissions = EDIT_PERMISSION;
  hasEditPermission: boolean = false;
  isChange360: boolean = false;
  urlToanCanh: string;
  mode: 'add' | 'edit' | 'delete';
  isFirst: boolean = true;

  constructor(
    public dialogService: DialogContentService,
    private activeRouter: ActivatedRoute,
    public unsubscriber: Unsubscriber,
    private submenuService: SubmenuService,
    private router: Router,
    private uploadFileService: UploadFileService,
    private alertMessageService: AlertMessageService,
    private authorizeService: AuthorizeService,
    private matDialog: MatDialog,
    private eSaleKitAuthService: ESaleKitAuthService
  ) {
  }

  ngOnInit() {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (this.isFirst) {
      this.isFirst = false;
      return;
    }
    const childParams = this.activeRouter.firstChild.snapshot.params;
    this.menuId = childParams['menuId'];
    this.unsubscriber.safeSubscriptions(
        this.dialogService.openPopup$.subscribe(val => {
          this.isOpenPopup = val;
        }),
        this.authorizeService.reloadAuth$.subscribe(_ => {
          this.checkPermission();
        }),
        this.eSaleKitAuthService.permissions$.subscribe(permissions => {
          this.hasEditPermission = permissions.canEdit;
        })
      );
      this.menus = Menus.map((e: Menu) => {
        return {
          ...e,
          url: e.static ? e.url : (e.isHasSub ? `${e.id}/${e.url}` : `${e.id}/${this.projectId}-${e.id}`)
        }
      });
      this.getSubMenu();
      this.getDataProjectId();
  }

  ngOnDestroy() {
    this.unsubscriber.unsubscribeAll();
  }

  checkPermission() {
    // Sử dụng cả old và new permission system để đảm bảo tương thích
    this.authorizeService.hasAuthority(EDIT_PERMISSION)
      .then(isHasPermission => {
        this.hasEditPermission = isHasPermission || this.eSaleKitAuthService.canEdit();
      });
  }

  /**
   * Kiểm tra quyền chỉnh sửa module cụ thể
   */
  canEditModule(module: string): boolean {
    return this.eSaleKitAuthService.canEditModule(module);
  }

  /**
   * Kiểm tra quyền chỉnh sửa chung
   */
  canEdit(): boolean {
    return this.hasEditPermission || this.eSaleKitAuthService.canEdit();
  }

  getEditPermission(item) {
    switch (item.id) {
      case '1':
        return EDIT_PERMISSION_OVERVIEW;
      case '3':
        return EDIT_PERMISSION_UTILITY;
      case '4':
        return EDIT_PERMISSION_PRODUCT;
      case '5':
        return EDIT_PERMISSION_TERM;
      default:
        return;
    }
  }

  showMenu(item) {
    if (this.selectMenuId === item.id) {
      this.showItem = false;
      this.selectMenuId = null;
      return false;
    } else {
      this.selectMenuId = item.id;
      this.showItem = true;
      return true;
    }
  }
  checkShowSubmenu(parentId: string) {
    if (this.showItem && this.selectMenuId) {
      if (parentId === this.selectMenuId) {
        return 'dblock';
      }
    }
    return 'dnone';
  }

  onAddMenuBtnClicked(item: any) {
    this.selectSubMenu = item;
    this.mode = 'add';
    const data: MenuPopupData = {
      mode: 'add',
      menuNameModel: '',
      title: 'THÊM SUB MENU',
      titleClass: 'title-center'
    };
    this.showPopup(data);
  }

  onEditMenuBtnClicked(item: any) {
    this.selectSubMenu = item;
    this.mode = 'edit';
    const data: MenuPopupData = {
      mode: 'edit',
      menuNameModel: item.menuName.toUpperCase(),
      title: 'SỬA SUB MENU',
      titleClass: 'title-center'
    };
    this.showPopup(data);
  }

  showPopup(data) {
    const dialogRef = this.matDialog.open(MenuPopupComponent, {
      width: '520px',
      panelClass: 'menu-no-padding-dialog',
      backdropClass: 'app-dialog-backdrop',
      data: data
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.processAction(result);
      }
    });
  }
  processAction(data) {
    let params;

    switch (data.mode) {
      case 'add':
        params = {
          menuName: data.menuNameModel,
          parentId: this.projectId + '-' + this.selectSubMenu.id,
          projectId: this.projectId
        };
        this.submenuService.addSubMenu(params).subscribe(_ => {
          this.getSubMenu();
        });
        break;
      case 'edit':
        params = {
          menuName: data.menuNameModel,
          id: this.selectSubMenu.id
        };
        this.submenuService.editSubmenu(params).subscribe(_ => {
          this.getSubMenu();
        });
        break;
      case 'delete':
        this.submenuService.deleteSubMenu(this.selectSubMenu.id).subscribe((_: any) => {
          this.getSubMenu();
          if (this.menuId == this.selectSubMenu.id) {
            let url = '/esalekit/' + this.projectId + '/' + this.projectId + '-1?edit=true';
            this.router.navigateByUrl(url, { queryParamsHandling: "merge" });
          }
        });
        break;
    }
  }

  getId(str: string) {
    if (!str) {
      return '';
    }
    return str.substr(str.length - 1, 1);
  }

  getSubMenu() {
    this.submenuService.getMenu({ projectId: this.projectId }).subscribe((submenus: any) => {
      this.subMenus = submenus || [];
      this.subMenus.forEach(e => {
        if (this.menuId === e.id) {
          const subMenu = this.subMenus.find(item => item.id === this.menuId);
          if (subMenu) {
            this.showItem = true;
            this.selectMenuId = this.getId(subMenu.parentId);
          }
        }
      })
    })
  }

  getDataProjectId() {
    if(this.projectId) {
      this.submenuService.getProjectId(this.projectId).subscribe(val => {
        if (val) {
          this.dataProject = val || [];
          this.urlLogo = val.logo || '';
          this.projectName = val.name || '';
        }
      },
      (err: HttpErrorResponse) => {
        this.urlLogo = 'assets/images/esalekit/logo-default.png';
        // let url = environment.webUrl;
        // this.router.navigate(['']);
      })
    }
  }

  selectFile(event: Event) {
    event.preventDefault();
    event.stopPropagation();

    const obFile = event.target['files'];
    const selectedFiles: File[] = Object.keys(obFile).map((elfile) => obFile[elfile]);
    const selectedDocuments: CDocument[] = new CDocument().decodeFileList(selectedFiles);

    if (this.isPermissionUpload(selectedDocuments)) {
      event.target['value'] = null;
      this.onUpload(selectedDocuments, event);
    }
  }

  onUpload(selectedDocuments: CDocument[], event: Event) {
    this.isLoading$.next(true);
    this.uploadFileService.uploadFileV3(
      selectedDocuments.map((doc: CDocument) => doc.file), null, '', true
    )
    .pipe(finalize(()=> this.isLoading$.next(false))
    ).subscribe((res) => {
      this.urlLogo = res[0].url;
      this.updateLogo(res[0].url);
    },
      (err: HttpErrorResponse) => {
        event.target['value'] = null;
        const errors = err.error.errors;
        if (errors['file.invalid.input.error']) {
          this.alertMessageService.error(errors['file.invalid.input.error']);
        }
      });
  }

  editBgImg() {
    $('#inputLogo').click();
  }

  private isPermissionUpload(selectedDocuments: CDocument[]): boolean {
    // const totalSize = selectedDocuments.reduce((previousValue: number, currentValue: CDocument) => (previousValue + currentValue.size), 0);

    // if (this.maxSize && selectedDocuments.some((el: CDocument) => el.size > this.maxSize)) {
    //   this.alertMessageService.error(`Size của một tấm hình vượt quá giới hạn. Vui lòng kiểm tra lại hình ảnh của bạn.`);
    //   return false;
    // }

    // if (this.maxSizeUpload && +this.maxSizeUpload < totalSize) {
    //   this.alertMessageService.error(`Tổng size vượt quá giới hạn. Vui lòng kiểm tra lại hình ảnh của bạn.`);
    //   return false;
    // }

    // if (this.maxUploadFile && selectedDocuments.length > this.maxUploadFile) {
    //   this.alertMessageService.error(`Số lượng hình ảnh không đúng quy định. Vui lòng kiểm tra lại hình ảnh của bạn.`);
    //   return false;
    // }

    return true;
  }

  updateLogo(urlLogo) {
    let params = {
      backgroundImage: this.dataProject.backgroundImage || '',
      name: this.dataProject.name || '',
      logo: urlLogo,
      status: this.dataProject.status || '',
      projectId: this.projectId
    }
    this.submenuService.updateProjectId(params).subscribe(val => {
      this.reloadPage();
    })
  }
  reloadPage() {
    this.router.navigate([], {relativeTo: this.activeRouter, queryParamsHandling: "merge" });
  }

  change360() {
    this.isChange360 = true;
  }

  onSave360() {

  }

  onCancel360() {
    this.isChange360 = false;
  }
}

