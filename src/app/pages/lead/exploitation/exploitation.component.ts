import {Component, Injector, OnDestroy, OnInit} from '@angular/core';
import {BaseComponent} from '../../../shared/components/base.component';
import {LeadService} from '../lead.service';
import {LeadModel} from '../lead.model';
import {ExploitStatusEnum} from '../../../shared/enum/exploitStatus.enum';
import { CTicket } from 'app/api-models';
import { catchError, finalize, map, take, takeUntil } from 'rxjs/operators';
import { BehaviorSubject, Observable, Subject, Subscription, throwError, timer } from 'rxjs';
import { LifeCycleStatusEnum } from 'app/shared/enum/lead.enum';
import { ToastrService } from 'app/shared/services/common';
import { TicketService } from 'app/api-services';
import { ExportTypeEnum } from 'app/shared/enum/export.enum';
import * as moment from 'moment';
import { StorageService } from 'app/shared/services';
import { WsNotifyService } from 'app/shared/components/ws-notify.service';


@Component({
    selector: "app-exploitation-leads",
    templateUrl: "exploitation.component.html",
    styleUrls: ["exploitation.component.scss"],
})
export class ExploitationComponent
    extends BaseComponent
    implements OnInit, OnDestroy {
    public isShowDetails = false;
    public selectedLead: any = null;
    public statusHistory;
    public surveyDetail = [];
    private unsubscribe$: Subject<any> = new Subject();
    public isLoading$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    public remainTimeMap = [];

    public leads = [];

    public boardColumns: any[] = [
        {
            title: "Đang phân bổ",
            cards: [],
            cardPrimaryColor: "#005cf7",
            cardSecondaryColor: "#d5e4ff",
            countDown: true,
        },
        {
            title: "Đang khảo sát",
            cards: [],
            cardPrimaryColor: "#f6bd43",
            cardSecondaryColor: "#fff2c3",
            countDown: false,
        },
        {
            title: "Đã xử lý",
            cards: [],
            cardPrimaryColor: "#00a21f",
            cardSecondaryColor: "#e6ffe6",
            countDown: false,
        },
        {
            title: "Đã trả về",
            cards: [],
            cardPrimaryColor: "#ee7a76",
            cardSecondaryColor: "#ffd1d1",
            countDown: false,
        },
    ];

    constructor(
        injector: Injector,
        private readonly leadSrv: LeadService,
        private readonly toastr: ToastrService,
        public readonly service: TicketService,
        public readonly wsNotifySrv: WsNotifyService,
    ) {
        super(injector.get(LeadService), LeadModel, injector);
    }

    ngOnInit() {
        const userId = this.storageService.retrieve('userId');
        if (userId) {
            this.infoMessagingPattern = [
                this.messagingPattern + "msx-adsg.lead.assigned." + userId,
            ];
            this.subscribe();
            // this.wsNotifySrv.subscribe([
            //     this.messagingPattern + "msx-adsg.notification.user-" + userId,
            // ])
            this.eventSubscribe = this.eventChangeService.emitChangeSource.subscribe(
                (data) => {
                    try {
                        if (data.broadcast === 'leadAssigned') {
                            const leads = data.message.data || [];
                            if (leads.length) {
                                leads.forEach((item) => {
                                    Object.assign(item, {
                                        visiblePhone: (item.visiblePhone) ? item.phone : this.hiddenPhone(item.phone),
                                        remainTime: {
                                            total: 0,
                                            hour: 0,
                                            minute: 0,
                                            second: 0,
                                        },
                                    });
                                    this.initCounter(item);
                                });
                                this.boardColumns[0].cards.push(...leads);
                                // this.leads.push(...leads);
                                const noti = data.message.noti
                                    ? data.message.noti
                                    : `Bạn có ${leads.length} lead mới được phân bổ !!!`;
                                this.toastr.success(noti);
                            }
                        }
                    } catch (error) {
                        console.log(error);
                    }
                }
            );
        }

        this.fetchData();
    }

    resetData(reInitLastId = null) {
        this.leads = [];
        this.boardColumns = [
            {
                title: "Đang phân bổ",
                cards: [],
                cardPrimaryColor: "#005cf7",
                cardSecondaryColor: "#d5e4ff",
                countDown: true,
            },
            {
                title: "Đang khảo sát",
                cards: [],
                cardPrimaryColor: "#f6bd43",
                cardSecondaryColor: "#fff2c3",
                countDown: false,
            },
            {
                title: "Đã xử lý",
                cards: [],
                cardPrimaryColor: "#00a21f",
                cardSecondaryColor: "#e6ffe6",
                countDown: false,
            },
            {
                title: "Đã trả về",
                cards: [],
                cardPrimaryColor: "#ee7a76",
                cardSecondaryColor: "#ffd1d1",
                countDown: false,
            },
        ];

        if (reInitLastId) {
            this.fetchData(reInitLastId);
        }
    }

    fetchData(lastSelectedId = null) {
        const params = {aggs: 1};
        this.leadSrv
            .getLeadsByStatus(params)
            .pipe(
                finalize(() => this.isLoading$.next(false)),
                takeUntil(this.unsubscribe$)
            )
            .subscribe((res: any) => {
                if (res.assign && res.assign.length) {
                    this.boardColumns[0].cards = res.assign.map(item => {
                        return {
                            ...item,
                            visiblePhone: (item.visiblePhone) ? item.phone : this.hiddenPhone(item.phone),
                            remainTime : {
                                total: 0,
                                hour: 0,
                                minute: 0,
                                second: 0
                              }
                        };
                    });
                }
                if (res.reassign && res.reassign.length) {
                    this.boardColumns[0].cards = this.boardColumns[0].cards.concat(
                        res.reassign.map(item => {
                            return {
                                ...item,
                                visiblePhone: (item.visiblePhone) ? item.phone : this.hiddenPhone(item.phone),
                                remainTime : {
                                    total: 0,
                                    hour: 0,
                                    minute: 0,
                                    second: 0
                                  }
                            };
                        })
                    );
                }
                if (res.processing && res.processing.length) {
                    this.boardColumns[1].cards = res.processing.map(item => {
                        return {
                            ...item,
                            visiblePhone: (item.visiblePhone) ? item.phone : this.hiddenPhone(item.phone),
                            remainTime: {
                                total: 0,
                                hour: 0,
                                minute: 0,
                                second: 0,
                            },
                        };
                    });
                }
                if (res.done && res.done.length) {
                    this.boardColumns[2].cards = res.done.map(item => {
                        return {
                            ...item,
                            visiblePhone: (item.visiblePhone) ? item.phone : this.hiddenPhone(item.phone),
                        };
                    });
                }
                if (res.cancel && res.cancel.length) {
                    this.boardColumns[3].cards = res.cancel.map(item => {
                        return {
                            ...item,
                            visiblePhone: (item.visiblePhone) ? item.phone : this.hiddenPhone(item.phone),
                        };
                    });
                }

                /*
                Object.values(res).forEach(
                    (v) => (this.leads = this.leads.concat(v as any).map(lead => {
                        let cardPrimaryColor = '';
                        let cardSecondaryColor = '';

                        switch (lead.exploitStatus) {
                            case ExploitStatusEnum.ASSIGN:
                            case ExploitStatusEnum.REASSIGN:
                                cardPrimaryColor = this.boardColumns[0].cardPrimaryColor;
                                cardSecondaryColor = this.boardColumns[0].cardSecondaryColor;
                                break;
                            case ExploitStatusEnum.PROCESSING:
                                cardPrimaryColor = this.boardColumns[1].cardPrimaryColor;
                                cardSecondaryColor = this.boardColumns[1].cardSecondaryColor;
                                break;
                            case ExploitStatusEnum.DONE:
                                cardPrimaryColor = this.boardColumns[2].cardPrimaryColor;
                                cardSecondaryColor = this.boardColumns[2].cardSecondaryColor;
                                break;
                            case ExploitStatusEnum.CANCEL:
                                cardPrimaryColor = this.boardColumns[3].cardPrimaryColor;
                                cardSecondaryColor = this.boardColumns[3].cardSecondaryColor;
                                break;
                        }

                        return {
                            ...lead,
                            visiblePhone: (lead.visiblePhone) ? lead.phone : this.hiddenPhone(lead.phone),
                            cardPrimaryColor,
                            cardSecondaryColor,
                            canCall: !![ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN, ExploitStatusEnum.PROCESSING].includes(lead.exploitStatus),
                        };
                    }))
                );
                */

                this.boardColumns[0].cards.forEach(i => this.initCounter(i));
                this.boardColumns[1].cards.forEach((i) => {
                    if (i.assignDuration !== -1) {
                        this.initCounter(i);
                    }
                });

                if (lastSelectedId) {
                    this.showDetails(lastSelectedId);
                }
            });
    }

    async showDetails(id: string) {
        const existed = this.leads.find((lead) => lead && lead.id === id);
        if (!existed || !existed.cached) {
            this.selectedLead = await this.leadSrv.getLeadById(id).toPromise();

            let cardPrimaryColor = '';
            let cardSecondaryColor = '';

            switch (this.selectedLead.exploitStatus) {
                case ExploitStatusEnum.ASSIGN:
                case ExploitStatusEnum.REASSIGN:
                    cardPrimaryColor = this.boardColumns[0].cardPrimaryColor;
                    cardSecondaryColor = this.boardColumns[0].cardSecondaryColor;
                    break;
                case ExploitStatusEnum.PROCESSING:
                    cardPrimaryColor = this.boardColumns[1].cardPrimaryColor;
                    cardSecondaryColor = this.boardColumns[1].cardSecondaryColor;
                    break;
                case ExploitStatusEnum.DONE:
                    cardPrimaryColor = this.boardColumns[2].cardPrimaryColor;
                    cardSecondaryColor = this.boardColumns[2].cardSecondaryColor;
                    break;
                case ExploitStatusEnum.CANCEL:
                    cardPrimaryColor = this.boardColumns[3].cardPrimaryColor;
                    cardSecondaryColor = this.boardColumns[3].cardSecondaryColor;
                    break;
            }

            Object.assign(this.selectedLead, {
                visiblePhone: (this.selectedLead.visiblePhone) ? this.selectedLead.phone : this.hiddenPhone(this.selectedLead.phone),
                cardPrimaryColor,
                cardSecondaryColor,
                cached:  true,
                canCall: !![ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN, ExploitStatusEnum.PROCESSING].includes(this.selectedLead.exploitStatus),
            });

            this.leads = this.updateData(this.leads, this.selectedLead.id, this.selectedLead);
        } else {
            this.selectedLead = existed;
        }

        if ([ExploitStatusEnum.DONE, ExploitStatusEnum.CANCEL].includes(this.selectedLead.exploitStatus)) {
            this.getHistory(this.selectedLead);
        }
        this.getSurvey(this.selectedLead);

        switch (this.selectedLead.exploitStatus) {
            case ExploitStatusEnum.ASSIGN:
            case ExploitStatusEnum.REASSIGN:
                this.selectedLead.exploitStatusText = 'Đang phân bổ';
                break;
            case ExploitStatusEnum.PROCESSING:
                this.selectedLead.exploitStatusText = 'Đang khảo sát';
                break;
            case ExploitStatusEnum.DONE:
                this.selectedLead.exploitStatusText = 'Đã xử lý';
                break;
            case ExploitStatusEnum.CANCEL:
                this.selectedLead.exploitStatusText = 'Đã trả về';
                break;
        }

        if (!this.isShowDetails) {
            this.isShowDetails = true;
        }
    }

    closeShowDetails() {
        this.selectedLead = null;
        this.isShowDetails = false;
    }

    createSurvey(ticket: CTicket, calling = false) {
        if (
            [
                ExploitStatusEnum.ASSIGN,
                ExploitStatusEnum.REASSIGN,
                ExploitStatusEnum.PROCESSING,
            ].includes(ticket.exploitStatus)
        ) {
            if (ticket.exploitStatus === ExploitStatusEnum.PROCESSING && !calling) {
                this.router.navigate(["lead/create/" + ticket.id]);
            }
            if (ticket.exploitStatus === ExploitStatusEnum.PROCESSING && calling) {
                this.leadSrv
                    .updateLead({
                        id: this.selectedLead.id,
                        assignDuration: -1,
                    })
                    .pipe()
                    .subscribe(() => {
                        console.log("calling...");
                        this.router.navigate(["lead/create/" + ticket.id]);
                    });
            }
            if (ticket.exploitStatus !== ExploitStatusEnum.PROCESSING) {
                this.service
                    .holdTicket(ticket.id)
                    .pipe(takeUntil(this.unsubscribe$))
                    .subscribe((res: any) => {
                        this.leadSrv
                            .updateExploitStatus(
                                this.selectedLead.id,
                                ExploitStatusEnum.PROCESSING,
                                calling && -1
                            )
                            .pipe(catchError((err) => throwError(err)))
                            .subscribe(
                                () => {
                                    this.router.navigate(["lead/create/" + ticket.id]);
                                },
                                (err) => {
                                    this.toastr.error(
                                        "Có lỗi xảy ra. Vui lòng thử lại sau!"
                                    );
                                }
                            );
                        ticket.exploitStatus = ExploitStatusEnum.PROCESSING;
                    });
            }
        }
    }

    isHot(item) {
        return item.isHot &&   [ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN].includes(
            item.exploitStatus
        );
    }

    showDetailButton(item) {
        const processing = this.boardColumns[1].cards.find(i => i && i.id === item.id);
        if (processing) {
            return [ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN, ExploitStatusEnum.PROCESSING].includes(
                item.exploitStatus
            );
        }
        return !item.isHot && [ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN, ExploitStatusEnum.PROCESSING].includes(
            item.exploitStatus
        );
    }

    confirmHotLead(event, item) {
        event.stopPropagation();
        this.leadSrv
            .updateExploitStatus(item.id, ExploitStatusEnum.PROCESSING)
            .pipe(catchError((err) => throwError(err)))
            .subscribe(
                () => {
                    this.resetData(item.id);
                    // this.updateData(this.boardColumns[1].cards, null, item);
                    this.toastr.success("Cập nhật Lead thành công");
                },
                (err) => {
                    this.toastr.error("Có lỗi xảy ra. Vui lòng thử lại sau!");
                }
            );
    }

    expireWarning(item) {
        const { exploitHistory, assignDuration } = item;
        if (exploitHistory && exploitHistory.length) {
            const targetHistory = exploitHistory.filter(
                (history) =>
                    history.status === item.exploitStatus &&
                    [
                        ExploitStatusEnum.ASSIGN,
                        ExploitStatusEnum.REASSIGN,
                    ].includes(item.exploitStatus)
            );
            if (!targetHistory.length) {
                return false;
            }

            const time = targetHistory.pop();
            const { updatedAt } = time;
            const statusTime = moment(updatedAt).valueOf();
            const expireTime = statusTime + assignDuration * 60000;
            const now = moment().valueOf();

            if (now < statusTime || now > expireTime) {
                return false;
            }

            return expireTime - statusTime - (now - statusTime) < 60000;
        }

        return false;
    }

    getDate(date) {
        return moment(date).format('DD/MM/YYYY, HH:mm');
    }

    hiddenPhone(phone) {
        let chars = phone.split('');

        chars[chars.length - 4] = '*';
        chars[chars.length - 5] = '*';
        chars[chars.length - 6] = '*';

        const newPhone = chars.join('');

        return newPhone;
    }

    showHistory(item) {
        return [ExploitStatusEnum.CANCEL, ExploitStatusEnum.DONE].includes(item.status);
    }

    getHistory(item) {
        const {exploitHistory, callHistory} = item;

        const latestCallHistory = callHistory.pop() || {};

        const latestAssigned = exploitHistory.filter(i => [ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN].includes(i.status)).pop() || {};

        const processed = exploitHistory.find(i => i.status === ExploitStatusEnum.PROCESSING) || {};
        const completed = exploitHistory.find(i => i.status === ExploitStatusEnum.DONE) || {};
        const rejected = exploitHistory.find(i => i.status === ExploitStatusEnum.CANCEL) || {};

        this.statusHistory = {
            processed: this.getTime(processed.updatedAt),
            assigned: this.getTime(latestAssigned.updatedAt),
            completed: this.getTime(completed.updatedAt),
            rejected: this.getTime(rejected.updatedAt),
            callHistory: {
                time: latestCallHistory.answerTime,
                note: item.note,
            }
        };
    }

    getTime(value) {
        if (!value) {
            return '';
        }

        return moment(value).format('DD/MM/YYYY HH:mm');
    }

    getSurvey(item) {
        const { surveys } = item;
        this.surveyDetail = surveys.map((i) => ({
            name: i.name,
            answers: i.valueName,
        }));
    }

    getRemainTime(item) {
        const { latestAssignHistory, assignDuration } = item;
        if (latestAssignHistory) {
            const { updatedAt } = latestAssignHistory;
            const statusTime = moment(updatedAt).valueOf();
            const expireTime = statusTime + assignDuration * 60000;
            const now = moment().valueOf();

            if (now >= expireTime) {
                return 0;
            }

            return Math.round((expireTime - now) / 1000);
        }

        return 0;
    }

    initCounter(item) {
        const total = this.getRemainTime(item);
        if (total === 0) {
            if (
                [ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN].includes(
                    item.exploitStatus
                )
            ) {
                this.boardColumns[0].cards = this.updateData(
                    this.boardColumns[0].cards,
                    item.id
                );
            }
            if (item.exploitStatus === ExploitStatusEnum.PROCESSING) {
                this.boardColumns[1].cards = this.updateData(
                    this.boardColumns[1].cards,
                    item.id
                );
            }
            this.leads = this.updateData(this.leads, item.id);
        } else {
            item.remainTime.total = total + 1;
            // Unsubscribe old counter
            if (item.remainTimeCounterSubscription) {
                item.remainTimeCounterSubscription.unsubscribe();
            }
            // Setup counter
            const remainTimeCounter$: Observable<any> = timer(0, 1000).pipe(
                take(item.remainTime.total),
                map(() => {
                    item.remainTime.total = item.remainTime.total - 1;
                    const currentMoment = moment.duration(
                        item.remainTime.total * 1000
                    ); // Milisecond
                    // https://momentjs.com/docs/#/durations/
                    item.remainTime.hour = currentMoment.hours();
                    item.remainTime.minute = currentMoment.minutes();
                    item.remainTime.second = currentMoment.seconds();
                }),
                takeUntil(this.unsubscribe$)
            );
            item.remainTimeCounterSubscription = remainTimeCounter$.subscribe(
                () => {
                    if (!item.remainTime || !item.remainTime.total) {
                        if (
                            [ExploitStatusEnum.ASSIGN, ExploitStatusEnum.REASSIGN].includes(
                                item.exploitStatus
                            )
                        ) {
                            this.boardColumns[0].cards = this.updateData(
                                this.boardColumns[0].cards,
                                item.id
                            );
                        }
                        if (item.exploitStatus === ExploitStatusEnum.PROCESSING) {
                            this.boardColumns[1].cards = this.updateData(
                                this.boardColumns[1].cards,
                                item.id
                            );
                        }
                        this.leads = this.updateData(this.leads, item.id);
                    }
                }
            );
        }

      }

      isCount(item) {
          return item.assignDuration !== -1 && [ExploitStatusEnum.REASSIGN, ExploitStatusEnum.ASSIGN, ExploitStatusEnum.PROCESSING].includes(item.exploitStatus);
      }

      private updateData(src: any[], id: string, record?: any) {
        const index = src.findIndex(item => item && item.id === id);
        if (!id || index < 0) {
          src = src.concat(record).filter(Boolean);
        } else {
          src = [...src.slice(0, index), record, ...src.slice(index + 1)].filter(Boolean);
        }

        return src;
      }

}
