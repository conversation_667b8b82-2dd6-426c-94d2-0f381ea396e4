<!-- <PERSON><PERSON> dụ sử dụng directive hasPermission trong E-Sale-Kit -->

<!-- 1. Hi<PERSON><PERSON> thị nút chỉnh sửa chỉ khi có quyền edit -->
<button mat-raised-button color="primary" *hasPermission="'esalekit.edit'">
  Chỉnh sửa
</button>

<!-- 2. Hiển thị menu admin chỉ khi có quyền edit overview -->
<div class="admin-menu" *hasPermission="['esalekit', 'edit', 'overview']">
  <h3>Menu quản trị</h3>
  <ul>
    <li>Cấu hình trang chủ</li>
    <li>Quản lý nội dung</li>
  </ul>
</div>

<!-- 3. Hiển thị section chỉ khi có ít nhất một trong các quyền -->
<section *hasPermission="['esalekit.edit.media', 'esalekit.edit.document']">
  <h3>Quản lý tài liệu và media</h3>
  
  <!-- Upload media chỉ khi có quyền edit media -->
  <div *hasPermission="'esalekit.edit.media'">
    <button mat-button>Upload hình ảnh</button>
  </div>
  
  <!-- Upload document chỉ khi có quyền edit document -->
  <div *hasPermission="'esalekit.edit.document'">
    <button mat-button>Upload tài liệu</button>
  </div>
</section>

<!-- 4. Yêu cầu có tất cả các quyền -->
<div *hasPermission="['esalekit.edit', 'esalekit.admin']; requireAll: true">
  <h3>Chức năng admin cao cấp</h3>
  <p>Chỉ hiển thị khi user có cả quyền edit và admin</p>
</div>

<!-- 5. Sử dụng trong menu component -->
<nav class="main-menu">
  <ul>
    <li>
      <a routerLink="/overview">Tổng quan</a>
    </li>
    
    <li *hasPermission="'esalekit.edit.ground'">
      <a routerLink="/ground">Quản lý mặt bằng</a>
    </li>
    
    <li *hasPermission="'esalekit.edit.utility'">
      <a routerLink="/utility">Quản lý tiện ích</a>
    </li>
    
    <li *hasPermission="'esalekit.edit.product'">
      <a routerLink="/product">Quản lý sản phẩm</a>
    </li>
    
    <li *hasPermission="'esalekit.edit.media'">
      <a routerLink="/media">Thư viện media</a>
    </li>
    
    <li *hasPermission="'esalekit.edit.document'">
      <a routerLink="/document">Tài liệu dự án</a>
    </li>
    
    <li *hasPermission="'esalekit.edit.news'">
      <a routerLink="/news">Tin tức</a>
    </li>
  </ul>
</nav>

<!-- 6. Sử dụng trong form -->
<form>
  <mat-form-field>
    <mat-label>Tên dự án</mat-label>
    <input matInput [(ngModel)]="projectName" [readonly]="!canEdit()">
  </mat-form-field>
  
  <!-- Chỉ hiển thị nút lưu khi có quyền edit -->
  <div class="form-actions" *hasPermission="'esalekit.edit'">
    <button mat-raised-button color="primary" type="submit">
      Lưu thay đổi
    </button>
    <button mat-button type="button">
      Hủy
    </button>
  </div>
</form>

<!-- 7. Sử dụng trong gallery component -->
<div class="gallery-container">
  <div class="gallery-header">
    <h3>Thư viện hình ảnh</h3>
    
    <!-- Nút upload chỉ hiển thị khi có quyền -->
    <button mat-fab color="primary" *hasPermission="'esalekit.edit.media'">
      <mat-icon>add</mat-icon>
    </button>
  </div>
  
  <div class="gallery-grid">
    <div *ngFor="let image of images" class="gallery-item">
      <img [src]="image.url" [alt]="image.title">
      
      <!-- Nút xóa chỉ hiển thị khi có quyền -->
      <button mat-icon-button 
              class="delete-btn" 
              *hasPermission="'esalekit.edit.media'"
              (click)="deleteImage(image)">
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>
</div>

<!-- 8. Sử dụng trong toolbar -->
<mat-toolbar>
  <span>E-Sale Kit</span>
  
  <span class="spacer"></span>
  
  <!-- Menu admin chỉ hiển thị khi có quyền -->
  <button mat-icon-button [matMenuTriggerFor]="adminMenu" *hasPermission="'esalekit.edit'">
    <mat-icon>settings</mat-icon>
  </button>
  
  <mat-menu #adminMenu="matMenu">
    <button mat-menu-item *hasPermission="'esalekit.edit.overview'">
      <mat-icon>dashboard</mat-icon>
      <span>Cấu hình trang chủ</span>
    </button>
    
    <button mat-menu-item *hasPermission="'esalekit.edit.media'">
      <mat-icon>photo_library</mat-icon>
      <span>Quản lý media</span>
    </button>
    
    <button mat-menu-item *hasPermission="'esalekit.admin'">
      <mat-icon>admin_panel_settings</mat-icon>
      <span>Quản trị hệ thống</span>
    </button>
  </mat-menu>
</mat-toolbar>

<!-- 9. Conditional styling dựa trên permissions -->
<div class="content-area" 
     [class.edit-mode]="canEdit()"
     [class.view-only]="!canEdit()">
  
  <div class="content-block" *ngFor="let block of contentBlocks">
    <div class="content-display">
      {{ block.content }}
    </div>
    
    <!-- Edit controls chỉ hiển thị khi có quyền -->
    <div class="edit-controls" *hasPermission="'esalekit.edit'">
      <button mat-icon-button (click)="editBlock(block)">
        <mat-icon>edit</mat-icon>
      </button>
      <button mat-icon-button (click)="deleteBlock(block)">
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>
</div>

<!-- 10. Sử dụng với multiple permissions và logic phức tạp -->
<div class="advanced-features">
  <!-- Hiển thị khi có quyền edit hoặc admin -->
  <div *hasPermission="['esalekit.edit', 'esalekit.admin']" class="management-section">
    <h3>Quản lý nâng cao</h3>
    
    <!-- Chỉ admin mới thấy -->
    <div *hasPermission="'esalekit.admin'" class="admin-only">
      <button mat-raised-button color="warn">
        Reset toàn bộ dữ liệu
      </button>
    </div>
    
    <!-- Editor và admin đều thấy -->
    <div *hasPermission="['esalekit.edit', 'esalekit.admin']">
      <button mat-raised-button>
        Xuất báo cáo
      </button>
    </div>
  </div>
</div>
