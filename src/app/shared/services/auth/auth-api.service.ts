import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { CookieService } from 'ngx-cookie-service';
import { environment } from 'environments/environment';

export interface UserAccount {
  id: string;
  username: string;
  email: string;
  fullName: string;
  authorities: string[];
  roles: any[];
  permissions: any[];
  isActive: boolean;
  organizationId?: string;
  departmentId?: string;
}

export interface AuthResponse {
  success: boolean;
  data: UserAccount;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthApiService {
  private readonly API_URL = 'https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account';
  private readonly TOKEN_COOKIE_NAME = 'access_token';
  
  private currentUserSubject = new BehaviorSubject<UserAccount | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  
  private authoritiesSubject = new BehaviorSubject<string[]>([]);
  public authorities$ = this.authoritiesSubject.asObservable();

  constructor(
    private http: HttpClient,
    private cookieService: CookieService
  ) {
    this.initializeAuth();
  }

  /**
   * Khởi tạo authentication từ cookie
   */
  private initializeAuth(): void {
    const token = this.getTokenFromCookie();
    if (token) {
      this.loadUserAccount().subscribe();
    }
  }

  /**
   * Lấy bearer token từ cookie
   */
  public getTokenFromCookie(): string | null {
    try {
      const token = this.cookieService.get(this.TOKEN_COOKIE_NAME);
      return token || null;
    } catch (error) {
      console.error('Error getting token from cookie:', error);
      return null;
    }
  }

  /**
   * Lưu token vào cookie
   */
  public setTokenToCookie(token: string, expirationDays: number = 7): void {
    try {
      const expires = new Date();
      expires.setDate(expires.getDate() + expirationDays);
      
      this.cookieService.set(
        this.TOKEN_COOKIE_NAME, 
        token, 
        expires, 
        '/', 
        undefined, 
        true, // secure
        'Strict' // sameSite
      );
    } catch (error) {
      console.error('Error setting token to cookie:', error);
    }
  }

  /**
   * Xóa token khỏi cookie
   */
  public removeTokenFromCookie(): void {
    try {
      this.cookieService.delete(this.TOKEN_COOKIE_NAME, '/');
    } catch (error) {
      console.error('Error removing token from cookie:', error);
    }
  }

  /**
   * Tạo HTTP headers với bearer token
   */
  private createAuthHeaders(): HttpHeaders {
    const token = this.getTokenFromCookie();
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Gọi API để lấy thông tin tài khoản và quyền từ access_token có sẵn trong cookies
   */
  public loadUserAccount(): Observable<UserAccount> {
    const token = this.getTokenFromCookie();

    if (!token) {
      const error = new Error('No access_token found in cookies');
      console.error('Auth Error:', error.message);
      this.handleAuthError();
      return throwError(() => error);
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('Loading user account from API with token from cookies...');

    return this.http.get<AuthResponse>(this.API_URL, { headers }).pipe(
      map(response => {
        if (response.success && response.data) {
          const user = response.data;
          this.currentUserSubject.next(user);
          this.authoritiesSubject.next(user.authorities || []);
          console.log('User account loaded successfully:', {
            username: user.username,
            authorities: user.authorities
          });
          return user;
        } else {
          throw new Error(response.message || 'Failed to load user account');
        }
      }),
      catchError(error => {
        console.error('Error loading user account:', error);

        // Nếu token không hợp lệ (401/403), xóa token khỏi cookie
        if (error.status === 401 || error.status === 403) {
          console.warn('Token invalid, removing from cookie');
          this.removeTokenFromCookie();
        }

        this.handleAuthError();
        return throwError(() => error);
      })
    );
  }

  /**
   * Kiểm tra quyền của user
   */
  public hasAuthority(authorities: string[]): boolean {
    const userAuthorities = this.authoritiesSubject.value;
    if (!userAuthorities || userAuthorities.length === 0) {
      return false;
    }

    // Kiểm tra xem user có ít nhất một trong các quyền yêu cầu
    return authorities.some(authority => 
      userAuthorities.includes(authority.toLowerCase())
    );
  }

  /**
   * Kiểm tra quyền async
   */
  public hasAuthorityAsync(authorities: string[]): Promise<boolean> {
    return new Promise((resolve) => {
      const currentAuthorities = this.authoritiesSubject.value;
      if (currentAuthorities && currentAuthorities.length > 0) {
        resolve(this.hasAuthority(authorities));
      } else {
        // Nếu chưa có authorities, thử load lại
        this.loadUserAccount().subscribe(
          () => resolve(this.hasAuthority(authorities)),
          () => resolve(false)
        );
      }
    });
  }

  /**
   * Lấy thông tin user hiện tại
   */
  public getCurrentUser(): UserAccount | null {
    return this.currentUserSubject.value;
  }

  /**
   * Lấy danh sách quyền hiện tại
   */
  public getCurrentAuthorities(): string[] {
    return this.authoritiesSubject.value;
  }

  /**
   * Kiểm tra xem user đã đăng nhập chưa
   */
  public isAuthenticated(): boolean {
    const token = this.getTokenFromCookie();
    const user = this.getCurrentUser();
    return !!(token && user);
  }

  /**
   * Xử lý lỗi authentication
   */
  private handleAuthError(): void {
    this.currentUserSubject.next(null);
    this.authoritiesSubject.next([]);
    this.removeTokenFromCookie();
  }

  /**
   * Đăng xuất
   */
  public logout(): void {
    this.handleAuthError();
  }

  /**
   * Refresh thông tin user và quyền
   */
  public refreshUserData(): Observable<UserAccount> {
    return this.loadUserAccount();
  }
}
