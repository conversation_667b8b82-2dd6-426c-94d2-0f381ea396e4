# Tích hợp Authentication với API CRM cho E-Sale-Kit

## Tổng quan

Hệ thống authentication mới được tích hợp để lấy thông tin quyền từ API CRM thay vì từ hệ thống login cũ. Bearer token đượ<PERSON> lưu trong cookies và tự động gửi kèm các request.

## Các thành phần chính

### 1. AuthApiService
- **File**: `src/app/shared/services/auth/auth-api.service.ts`
- **Chức năng**: Quản lý token, gọi API lấy thông tin user và quyền
- **API Endpoint**: `https://uat-api-crm.datxanh.com.vn/msx-sts/api/query/v1/account`

### 2. ESaleKitAuthService  
- **File**: `src/app/pages/e-sale-kit/services/e-sale-kit-auth.service.ts`
- **Chứ<PERSON> năng**: <PERSON><PERSON><PERSON><PERSON> lý permissions cụ thể cho E-Sale-Kit module

### 3. HasPermissionDirective
- **File**: `src/app/pages/e-sale-kit/directives/has-permission.directive.ts`
- **Chức năng**: Directive để ẩn/hiện elements dựa trên permissions

### 4. TokenInterceptor (Updated)
- **File**: `src/app/shared/services/interceptors/token.interceptor.ts`
- **Chức năng**: Tự động thêm bearer token vào HTTP headers

## Cách sử dụng

### 1. Kiểm tra permissions trong component

```typescript
import { ESaleKitAuthService } from '../services/e-sale-kit-auth.service';

export class MyComponent {
  constructor(private authService: ESaleKitAuthService) {}

  canEdit(): boolean {
    return this.authService.canEdit();
  }

  canEditModule(module: string): boolean {
    return this.authService.canEditModule(module);
  }
}
```

### 2. Sử dụng directive trong template

```html
<!-- Hiển thị khi có quyền edit -->
<button *hasPermission="'esalekit.edit'">Chỉnh sửa</button>

<!-- Hiển thị khi có ít nhất một trong các quyền -->
<div *hasPermission="['esalekit.edit.media', 'esalekit.edit.document']">
  Quản lý tài liệu
</div>

<!-- Yêu cầu có tất cả các quyền -->
<div *hasPermission="['esalekit.edit', 'esalekit.admin']; requireAll: true">
  Chức năng admin
</div>
```

### 3. Lấy thông tin user

```typescript
import { AuthApiService } from '../../../shared/services/auth/auth-api.service';

export class MyComponent {
  constructor(private authApiService: AuthApiService) {}

  getCurrentUser() {
    return this.authApiService.getCurrentUser();
  }

  getCurrentAuthorities() {
    return this.authApiService.getCurrentAuthorities();
  }
}
```

## Cấu hình Permissions

### Các quyền E-Sale-Kit được định nghĩa trong constants:

```typescript
// src/app/pages/e-sale-kit/constants/constants.ts
export const EDIT_PERMISSION = ['esalekit', 'edit'];
export const EDIT_PERMISSION_OVERVIEW = ['esalekit', 'edit', 'overview'];
export const EDIT_PERMISSION_GROUND = ['esalekit', 'edit', 'ground'];
export const EDIT_PERMISSION_UTILITY = ['esalekit', 'edit', 'utility'];
export const EDIT_PERMISSION_PRODUCT = ['esalekit', 'edit', 'product'];
export const EDIT_PERMISSION_TERM = ['esalekit', 'edit', 'term'];
export const EDIT_PERMISSION_MEDIA = ['esalekit', 'edit', 'media'];
export const EDIT_PERMISSION_DOCUMENT = ['esalekit', 'edit', 'document'];
export const EDIT_PERMISSION_NEWS = ['esalekit', 'edit', 'news'];
```

## Token Management

### Lưu token vào cookie:
```typescript
authApiService.setTokenToCookie(token, expirationDays);
```

### Lấy token từ cookie:
```typescript
const token = authApiService.getTokenFromCookie();
```

### Xóa token:
```typescript
authApiService.removeTokenFromCookie();
```

## API Response Format

API CRM cần trả về format sau:

```json
{
  "success": true,
  "data": {
    "id": "user-id",
    "username": "username",
    "email": "<EMAIL>",
    "fullName": "Full Name",
    "authorities": [
      "esalekit.edit",
      "esalekit.edit.overview",
      "esalekit.edit.media"
    ],
    "roles": [...],
    "permissions": [...],
    "isActive": true
  }
}
```

## Error Handling

### Authentication Errors:
- Token không hợp lệ → Redirect to login
- API không phản hồi → Hiển thị error message
- Không có quyền truy cập → Hiển thị access denied

### Trong component:
```typescript
this.authService.checkAuthenticationAndLoadUser().subscribe({
  next: (isAuthenticated) => {
    if (isAuthenticated) {
      // Load data
    }
  },
  error: (error) => {
    // Handle error
    this.router.navigate(['/login']);
  }
});
```

## Testing

### 1. Test với token hợp lệ:
- Đặt token vào cookie
- Reload trang
- Kiểm tra permissions được load đúng

### 2. Test với token không hợp lệ:
- Đặt token sai vào cookie  
- Reload trang
- Kiểm tra redirect to login

### 3. Test permissions:
- Login với user có quyền khác nhau
- Kiểm tra UI hiển thị đúng theo permissions

## Migration từ hệ thống cũ

### 1. Backup code cũ
### 2. Update imports trong components
### 3. Replace permission checks
### 4. Test thoroughly
### 5. Deploy và monitor

## Troubleshooting

### Token không được gửi:
- Kiểm tra TokenInterceptor đã được config đúng
- Kiểm tra cookie domain và path
- Kiểm tra CORS settings

### Permissions không hoạt động:
- Kiểm tra API response format
- Kiểm tra authorities mapping
- Kiểm tra directive syntax

### Performance issues:
- Cache permissions locally
- Debounce API calls
- Use OnPush change detection

## Security Considerations

1. **Token Storage**: Sử dụng httpOnly cookies khi có thể
2. **HTTPS**: Luôn sử dụng HTTPS trong production
3. **Token Expiration**: Implement token refresh mechanism
4. **CORS**: Cấu hình CORS đúng cho API endpoints
5. **XSS Protection**: Validate và sanitize user inputs
