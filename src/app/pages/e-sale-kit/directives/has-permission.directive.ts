import { Directive, Input, TemplateRef, ViewContainerRef, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ESaleKitAuthService } from '../services/e-sale-kit-auth.service';

@Directive({
  selector: '[hasPermission]'
})
export class HasPermissionDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private permissions: string[] = [];
  private requireAll: boolean = false;

  @Input() set hasPermission(permissions: string | string[]) {
    this.permissions = Array.isArray(permissions) ? permissions : [permissions];
    this.updateView();
  }

  @Input() set hasPermissionRequireAll(requireAll: boolean) {
    this.requireAll = requireAll;
    this.updateView();
  }

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private eSaleKitAuthService: ESaleKitAuthService
  ) {}

  ngOnInit(): void {
    // Lắng nghe thay đổi permissions
    this.eSaleKitAuthService.permissions$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateView();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateView(): void {
    if (this.hasRequiredPermissions()) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }

  private hasRequiredPermissions(): boolean {
    if (!this.permissions || this.permissions.length === 0) {
      return true; // Nếu không có permissions yêu cầu, cho phép hiển thị
    }

    const userAuthorities = this.eSaleKitAuthService.getCurrentAuthorities();
    
    if (!userAuthorities || userAuthorities.length === 0) {
      return false;
    }

    if (this.requireAll) {
      // Yêu cầu có tất cả permissions
      return this.permissions.every(permission => 
        userAuthorities.includes(permission.toLowerCase())
      );
    } else {
      // Chỉ cần có ít nhất một permission
      return this.permissions.some(permission => 
        userAuthorities.includes(permission.toLowerCase())
      );
    }
  }
}
