import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthApiService } from '../../../shared/services/auth/auth-api.service';
import { ESaleKitAuthService } from './e-sale-kit-auth.service';

export interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  success: boolean;
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  user: {
    id: string;
    username: string;
    email: string;
    fullName: string;
  };
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ESaleKitLoginService {
  private readonly LOGIN_API_URL = 'https://uat-api-crm.datxanh.com.vn/msx-sts/api/auth/login';
  
  private isLoggingIn = new BehaviorSubject<boolean>(false);
  public isLoggingIn$ = this.isLoggingIn.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private authApiService: AuthApiService,
    private eSaleKitAuthService: ESaleKitAuthService
  ) {}

  /**
   * Đăng nhập với username và password
   */
  public login(credentials: LoginRequest): Observable<LoginResponse> {
    this.isLoggingIn.next(true);

    return this.http.post<LoginResponse>(this.LOGIN_API_URL, credentials).pipe(
      tap(response => {
        if (response.success && response.access_token) {
          // Lưu token vào cookie
          const expirationDays = credentials.rememberMe ? 30 : 7;
          this.authApiService.setTokenToCookie(response.access_token, expirationDays);
          
          // Load user data và permissions
          this.authApiService.loadUserAccount().subscribe({
            next: () => {
              console.log('User data loaded successfully after login');
            },
            error: (error) => {
              console.error('Error loading user data after login:', error);
            }
          });
        }
      }),
      catchError(error => {
        console.error('Login error:', error);
        throw error;
      }),
      map(response => {
        this.isLoggingIn.next(false);
        return response;
      })
    );
  }

  /**
   * Đăng xuất
   */
  public logout(): void {
    // Clear token và user data
    this.authApiService.logout();
    this.eSaleKitAuthService.logout();
    
    // Redirect to login page
    this.router.navigate(['/login']);
  }

  /**
   * Kiểm tra trạng thái đăng nhập
   */
  public isLoggedIn(): boolean {
    return this.authApiService.isAuthenticated();
  }

  /**
   * Lấy thông tin user hiện tại
   */
  public getCurrentUser() {
    return this.authApiService.getCurrentUser();
  }

  /**
   * Refresh token (nếu API hỗ trợ)
   */
  public refreshToken(): Observable<any> {
    // Implement refresh token logic nếu API hỗ trợ
    // Hiện tại chỉ reload user data
    return this.authApiService.refreshUserData();
  }

  /**
   * Kiểm tra và redirect nếu chưa đăng nhập
   */
  public checkAuthenticationAndRedirect(currentUrl?: string): boolean {
    if (!this.isLoggedIn()) {
      // Lưu URL hiện tại để redirect sau khi login
      if (currentUrl) {
        sessionStorage.setItem('redirectUrl', currentUrl);
      }
      
      this.router.navigate(['/login']);
      return false;
    }
    
    return true;
  }

  /**
   * Redirect sau khi login thành công
   */
  public redirectAfterLogin(): void {
    const redirectUrl = sessionStorage.getItem('redirectUrl');
    sessionStorage.removeItem('redirectUrl');
    
    if (redirectUrl) {
      this.router.navigateByUrl(redirectUrl);
    } else {
      // Default redirect
      this.router.navigate(['/dashboard']);
    }
  }

  /**
   * Validate token hiện tại
   */
  public validateCurrentToken(): Observable<boolean> {
    return this.authApiService.loadUserAccount().pipe(
      map(() => true),
      catchError(() => {
        // Token không hợp lệ, logout
        this.logout();
        return [false];
      })
    );
  }

  /**
   * Auto login nếu có token trong cookie
   */
  public autoLogin(): Observable<boolean> {
    const token = this.authApiService.getTokenFromCookie();
    
    if (!token) {
      return new Observable(observer => {
        observer.next(false);
        observer.complete();
      });
    }

    return this.validateCurrentToken();
  }

  /**
   * Kiểm tra quyền truy cập E-Sale-Kit
   */
  public canAccessESaleKit(): boolean {
    return this.eSaleKitAuthService.canView();
  }

  /**
   * Kiểm tra quyền chỉnh sửa E-Sale-Kit
   */
  public canEditESaleKit(): boolean {
    return this.eSaleKitAuthService.canEdit();
  }
}
