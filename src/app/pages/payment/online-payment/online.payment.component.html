<div class="payment-container">
    <div *ngIf="!paymentInfo?.invoice">
        <mat-toolbar color="primary">
            <mat-toolbar-row class="toolbar">
                <div fxLayoutAlign="center" fxFlex="100">
                    <div fxLayout="row" fxLayoutAlign="space-between center" class="wrap-container">
                        <img class="rounded-circle logo" src="/assets/img/logo/FPT_logo.png">
                        <div fxLayoutAlign="center center">
                            <img (click)="setLang('vn')" src="assets/img/icon/vi.svg" class="language mr-10" [class.selected]="paymentData?.language === 'vn'">
                            <img (click)="setLang('en')" src="assets/img/icon/en.svg" class="language" [class.selected]="paymentData?.language === 'en'">
                        </div>
                    </div>
                </div>
            </mat-toolbar-row>
        </mat-toolbar>
    </div>
    <div fxLayout="row" fxLayoutAlign="center center" *ngIf="isLoading">
        <div id="app-spinner">
          <mat-spinner color="primary"></mat-spinner>
        </div>
    </div>
    <div *ngIf="paymentInfo?.id">
        <div fxLayoutAlign="center" fxFlex="100">
            <div fxLayout="column" class="wrap-container payment-info-wrap">
                <span class="header-title">{{ 'paymentInfo' | translate }}</span>
                <div fxLayout="column" fxLayoutGap="16px" fxLayout.gt-md="row">
                    <div fxFlex.gt-md="34" fxLayout="column" fxLayoutAlign="center center" class="payment-info-item">
                        <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="15px" class="mt-15">
                            <img class="invoice-icon" src="assets/img/icon/invoice.svg">
                            <div>
                                <span class="invoice-value normal-text fw-bold">{{ 'orderValue' | translate }}</span>
                                <div>
                                    <span class="money-text fw-bold fz-30">{{ paymentInfo?.amount | money }}</span>
                                    <span class="normal-text fz-20"> {{ 'vnd' | translate }}</span>
                                </div>
                            </div>
                        </div>
                        <hr *ngIf="paymentInfo?.accountConfig?.logoUrl || paymentInfo?.accountConfig?.companyName || paymentInfo?.accountConfig?.companyAddress" />
                        <img *ngIf="paymentInfo?.accountConfig?.logoUrl" class="logo-img mb-15" [src]="paymentInfo?.accountConfig?.logoUrl">
                        <span *ngIf="paymentInfo?.accountConfig?.companyName" class="normal-text fw-bold fz-14 mb-15">{{ 'companyName' | translate }}</span>
                        <span *ngIf="paymentInfo?.accountConfig?.companyAddress" class="normal-text fz-15 mb-13 px-30 text-center">{{ 'companyAddress' | translate }}</span>
                    </div>
                    <div fxFlex.gt-md="66" fxLayout="column" class="payment-info-item">
                        <div fxLayout="column" fxLayoutGap="15px" class="info">
                            <div fxLayout="row" fxFlex="100">
                                <div fxFlex="40"><b>{{ 'code' | translate }}</b></div>
                                <div class="code">{{ paymentInfo?.code }}</div>
                            </div>
                            <div fxLayout="row" fxFlex="100">
                                <div fxFlex="40"><b>{{ 'full_name' | translate }}</b></div>
                                <div>{{ paymentInfo?.name }}</div>
                            </div>
                            <div fxLayout="row" fxFlex="100">
                                <div fxFlex="40"><b>{{ 'phone' | translate }}</b></div>
                                <div>{{ paymentInfo?.phone }}</div>
                            </div>
                            <div fxLayout="row" fxFlex="100">
                                <div fxFlex="40"><b>Email</b></div>
                                <div>{{ paymentInfo?.email }}</div>
                            </div>
                            <div fxLayout="row" fxFlex="100">
                                <div fxFlex="40"><b>{{ 'description' | translate }}</b></div>
                                <div>{{ paymentInfo?.content }}</div>
                            </div>
                            <div fxLayout="row" fxFlex="100" *ngIf="paymentInfo?.transferredAmount > 0">
                                <div fxFlex="40"><b>{{ 'transferredAmount' | translate }}</b></div>
                                <div>{{ paymentInfo?.transferredAmount | money }} {{ 'vnd' | translate }}</div>
                            </div>
                            <div fxLayout="row" fxFlex="100" *ngIf="paymentInfo?.transferredAmount > 0">
                                <div fxFlex="40"><b>{{ 'remainingAmount' | translate }}</b></div>
                                <div>{{ paymentInfo?.remainingAmount | money }} {{ 'vnd' | translate }}</div>
                            </div>
                            <div fxLayout="row" fxFlex="100">
                                <div fxFlex="40"><b>{{ 'status' | translate }}</b></div>
                                <div>{{ paymentInfo?.status | translate }}</div>
                            </div>
                        </div>
                        <div *ngIf="paymentInfo?.status !== 'PAID'" class="input-amount-area" [class.hide]="!paymentInfo.allowSubPayment">
                            <div fxLayout="column" fxLayout.gt-xs="row" fxFlex="100" fxLayoutAlign="center start" fxLayoutAlign.gt-xs="center center">
                                <div fxFlex="40"><b>{{ 'enterAmount' | translate }}</b></div>
                                <div fxFlex="100" fxFlex.gt-xs="60" class="w-100">
                                    <div class="app-input-unit">
                                        <input
                                            class="app-input w-100 mh-40"
                                            [(ngModel)]="paymentData.amount"
                                            [textMask]="{mask: numberMask}"
                                            placeholder="{{ 'enterAmount' | translate }}"
                                        >
                                        <span class="input-unit">
                                            {{ 'vnd' | translate }}
                                        </span>
                                    </div>
                                    <mat-error *ngIf="!validAmount()">
                                        <span *ngIf="!validAmount()" class="mt-5">{{ 'invalidAmount' | translate }}</span>
                                    </mat-error>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="paymentInfo?.id && paymentInfo?.status !== 'PAID' && paymentInfo?.status !== 'CANCEL'">
        <div fxLayoutAlign="center" fxFlex="100" class="payment-process-wrap">
            <div fxLayout="column" class="wrap-container payment-process-item">
                <span class="header-title">{{ 'choosePaymentMethod' | translate }}</span>
                <div fxLayout="row wrap" fxFlex="100">
                    <div fxFlex="50" fxFlex.gt-xs="25" *ngFor="let method of methodList; let i = index" class="payment-method-wrap">
                        <div
                            fxLayout="column" class="payment-method" fxLayoutAlign="start center"
                            (click)="paymentMethodTypeChange(method.code, i)"
                            [class.selected]="selectedMethodIdx === i"
                            fxFlex="100"
                        >
                            <img src="assets/img/icon/{{getPaymentMethodImage(method.code)}}.svg" class="mb-10 image">
                            <span class="normal-text text-center">{{ method.code  | translate }}</span>
                        </div>
                    </div>
                </div>
                <div id="endPage"></div>
                <div class="payment-method-item-wrap mt-15" *ngIf="selectedMethodIdx !== -1" fxLayout="column">
                    <div *ngIf="imgBase64" fxLayoutAlign="center center" fxLayout="column" fxFlex="100"
                         class="qr-code">
                        <img [src]="imgBase64" alt="Mã thanh toán" />
                        <i>{{ 'qr_payment' | translate }}</i>
                    </div>
                    <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px grid"
                         [class.hide]="methodList[selectedMethodIdx].code !== paymentMethod">
                        <div fxFlex="15" fxFlex.md="22" fxFlex.sm="22" fxFlex.xs="45" fxLayout="row"
                             *ngFor="let item of methodList[selectedMethodIdx].list" class="bank-logo" [class.selected]="
                                paymentData.paymentMethod === item.code
                            " (click)="selectBank(item.code)">
                            <img [src]="item.logoUrl" [alt]="item.name" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="loading-modal" *ngIf="isLoadingBanking">
        <div fxLayout="row"  fxLayoutAlign="center center" class="spinner-position">
            <div id="app-spinner" >
                <mat-spinner color="primary"></mat-spinner>
            </div>
        </div>
    </div>

</div>
